/**
 * Springfield Town Builder - Building System
 * Handles building placement, management, and interactions
 */

class BuildingSystem extends EventEmitter {
    constructor(engine3D, currencySystem, questSystem = null) {
        super();
        this.engine3D = engine3D;
        this.currencySystem = currencySystem;
        this.questSystem = questSystem;
        
        // Building data and state
        this.buildingData = null;
        this.placedBuildings = new Map();
        this.selectedBuilding = null;
        this.placementMode = false;
        this.placementPreview = null;

        // Move mode state
        this.moveMode = false;
        this.buildingToMove = null;
        
        // Building categories
        this.categories = {
            residential: { name: 'Residential', icon: '🏠' },
            commercial: { name: 'Commercial', icon: '🏪' },
            entertainment: { name: 'Entertainment', icon: '🎪' },
            community: { name: 'Community', icon: '🏛️' },
            industrial: { name: 'Industrial', icon: '🏭' },
            roads: { name: 'Roads', icon: '🛣️' },
            decorations: { name: 'Decorations', icon: '🌳' }
        };
        
        this.currentCategory = 'residential';
        
        this.init();
    }
    
    async init() {
        await this.loadBuildingData();
        this.setupEventListeners();
        console.log('Building System initialized');
    }
    
    async loadBuildingData() {
        try {
            const response = await fetch('../../data/buildings.json');
            const data = await response.json();
            this.buildingData = data.buildings;
            console.log('Building data loaded:', Object.keys(this.buildingData).length, 'buildings');
            this.emit('buildingDataLoaded');
        } catch (error) {
            console.error('Failed to load building data:', error);
            this.buildingData = {};
            // Provide fallback data
            this.createFallbackBuildingData();
            this.emit('buildingDataLoaded');
        }
    }

    createFallbackBuildingData() {
        this.buildingData = {
            'simpson_house': {
                id: 'simpson_house',
                name: 'Simpson House',
                description: 'The iconic home of the Simpson family',
                category: 'residential',
                image: 'assets/buildings/simpson_house.png',
                size: { width: 4, height: 3, depth: 4 },
                unlockLevel: 1,
                cost: { type: 'free' },
                buildTime: 0,
                income: { amount: 100, interval: 3600 },
                xpReward: 50,
                maxLevel: 5,
                upgrades: [
                    {
                        level: 2,
                        cost: { type: 'money', amount: 5000 },
                        income: { amount: 150, interval: 3600 },
                        description: 'Add a garage and improve the yard'
                    },
                    {
                        level: 3,
                        cost: { type: 'money', amount: 10000 },
                        income: { amount: 200, interval: 3600 },
                        description: 'Renovate the kitchen and living room'
                    }
                ]
            },
            'kwik_e_mart': {
                id: 'kwik_e_mart',
                name: 'Kwik-E-Mart',
                description: 'Springfield\'s premier convenience store',
                category: 'commercial',
                image: 'assets/buildings/kwik_e_mart.png',
                size: { width: 3, height: 2, depth: 3 },
                unlockLevel: 2,
                cost: { type: 'money', amount: 2500 },
                buildTime: 300,
                income: { amount: 75, interval: 1800 },
                xpReward: 100,
                maxLevel: 5,
                upgrades: [
                    {
                        level: 2,
                        cost: { type: 'money', amount: 7500 },
                        income: { amount: 100, interval: 1800 },
                        description: 'Add a Squishee machine and expand inventory'
                    }
                ]
            },
            'moes_tavern': {
                id: 'moes_tavern',
                name: 'Moe\'s Tavern',
                description: 'Springfield\'s favorite dive bar',
                category: 'commercial',
                image: 'assets/buildings/moes_tavern.png',
                size: { width: 3, height: 2, depth: 2 },
                unlockLevel: 3,
                cost: { type: 'money', amount: 3500 },
                buildTime: 600,
                income: { amount: 90, interval: 2400 },
                xpReward: 150,
                maxLevel: 4,
                upgrades: [
                    {
                        level: 2,
                        cost: { type: 'money', amount: 8000 },
                        income: { amount: 120, interval: 2400 },
                        description: 'Add a pool table and jukebox'
                    }
                ]
            },
            'krusty_burger': {
                id: 'krusty_burger',
                name: 'Krusty Burger',
                description: 'Fast food that\'s... well, it\'s fast',
                category: 'commercial',
                image: 'assets/buildings/krusty_burger.png',
                size: { width: 3, height: 2, depth: 3 },
                unlockLevel: 4,
                cost: { type: 'money', amount: 5500 },
                buildTime: 900,
                income: { amount: 110, interval: 3000 },
                xpReward: 200,
                maxLevel: 4
            },
            'springfield_elementary': {
                id: 'springfield_elementary',
                name: 'Springfield Elementary',
                description: 'Where young minds go to... well, it\'s complicated',
                category: 'community',
                image: 'assets/buildings/springfield_elementary.png',
                size: { width: 5, height: 3, depth: 4 },
                unlockLevel: 5,
                cost: { type: 'money', amount: 8000 },
                buildTime: 1200,
                income: { amount: 120, interval: 4800 },
                xpReward: 250,
                maxLevel: 3
            },
            'androids_dungeon': {
                id: 'androids_dungeon',
                name: 'The Android\'s Dungeon',
                description: 'Comic Book Guy\'s temple to all things nerdy',
                category: 'commercial',
                image: 'assets/buildings/androids_dungeon.png',
                size: { width: 2, height: 2, depth: 3 },
                unlockLevel: 6,
                cost: { type: 'donuts', amount: 40 },
                buildTime: 1800,
                income: { amount: 85, interval: 3600 },
                xpReward: 300,
                maxLevel: 3
            },
            'flanders_house': {
                id: 'flanders_house',
                name: 'Flanders House',
                description: 'The perfectly maintained home of Ned Flanders',
                category: 'residential',
                image: 'assets/buildings/flanders_house.png',
                size: { width: 3, height: 3, depth: 3 },
                unlockLevel: 4,
                cost: { type: 'donuts', amount: 35 },
                buildTime: 600,
                income: { amount: 120, interval: 4200 },
                xpReward: 180,
                maxLevel: 4
            },
            'tree': {
                id: 'tree',
                name: 'Tree',
                description: 'A nice tree for decoration',
                category: 'decorations',
                image: 'assets/decorations/tree.png',
                size: { width: 1, height: 2, depth: 1 },
                unlockLevel: 1,
                cost: { type: 'money', amount: 100 },
                buildTime: 0,
                income: { amount: 0, interval: 0 },
                xpReward: 5,
                maxLevel: 1
            },
            'bench': {
                id: 'bench',
                name: 'Park Bench',
                description: 'A place for characters to sit and relax',
                category: 'decorations',
                image: 'assets/decorations/bench.png',
                size: { width: 2, height: 1, depth: 1 },
                unlockLevel: 2,
                cost: { type: 'money', amount: 250 },
                buildTime: 0,
                income: { amount: 0, interval: 0 },
                xpReward: 10,
                maxLevel: 1
            },
            'fountain': {
                id: 'fountain',
                name: 'Fountain',
                description: 'A decorative fountain for the town square',
                category: 'decorations',
                image: 'assets/decorations/fountain.png',
                size: { width: 2, height: 2, depth: 2 },
                unlockLevel: 8,
                cost: { type: 'donuts', amount: 20 },
                buildTime: 0,
                income: { amount: 0, interval: 0 },
                xpReward: 50,
                maxLevel: 1
            },

            // Roads
            'road_straight': {
                id: 'road_straight',
                name: 'Straight Road',
                description: 'A straight section of road for connecting buildings',
                category: 'roads',
                image: 'assets/roads/road_straight.png',
                size: { width: 2, height: 0.1, depth: 2 },
                unlockLevel: 1,
                cost: { type: 'money', amount: 25 },
                buildTime: 0,
                income: null,
                xpReward: 2,
                maxLevel: 1,
                icon: '🛣️'
            },
            'road_corner': {
                id: 'road_corner',
                name: 'Corner Road',
                description: 'A corner section of road for turns',
                category: 'roads',
                image: 'assets/roads/road_corner.png',
                size: { width: 2, height: 0.1, depth: 2 },
                unlockLevel: 1,
                cost: { type: 'money', amount: 25 },
                buildTime: 0,
                income: null,
                xpReward: 2,
                maxLevel: 1,
                icon: '🔄'
            },
            'road_intersection': {
                id: 'road_intersection',
                name: 'Road Intersection',
                description: 'A four-way intersection for complex road networks',
                category: 'roads',
                image: 'assets/roads/road_intersection.png',
                size: { width: 2, height: 0.1, depth: 2 },
                unlockLevel: 2,
                cost: { type: 'money', amount: 50 },
                buildTime: 0,
                income: null,
                xpReward: 5,
                maxLevel: 1,
                icon: '✚'
            },
            'road_t_junction': {
                id: 'road_t_junction',
                name: 'T-Junction',
                description: 'A T-shaped road junction',
                category: 'roads',
                image: 'assets/roads/road_t_junction.png',
                size: { width: 2, height: 0.1, depth: 2 },
                unlockLevel: 1,
                cost: { type: 'money', amount: 35 },
                buildTime: 0,
                income: null,
                xpReward: 3,
                maxLevel: 1,
                icon: '⊥'
            }
        };
        console.log('Using fallback building data');
    }
    
    setupEventListeners() {
        // Listen for ground clicks for building placement and moving
        this.engine3D.on('groundClicked', (position) => {
            if (this.placementMode && this.selectedBuilding) {
                this.placeBuildingAt(position);
            } else if (this.moveMode && this.buildingToMove) {
                this.moveBuildingTo(position);
            }
        });

        // Listen for building clicks
        this.engine3D.on('buildingClicked', (building) => {
            this.selectBuilding(building);
        });

        // Listen for currency changes to update building availability
        this.currencySystem.on('moneyChanged', () => this.updateBuildingAvailability());
        this.currencySystem.on('donutsChanged', () => this.updateBuildingAvailability());
        this.currencySystem.on('levelUp', () => this.updateBuildingAvailability());

        // Listen for mouse movement to update placement preview
        this.engine3D.canvas.addEventListener('mousemove', (event) => {
            if (this.placementMode && this.placementPreview) {
                this.updatePlacementPreviewFromMouse(event);
            }
        });

        // Listen for right-click to cancel placement or move mode
        this.engine3D.canvas.addEventListener('contextmenu', (event) => {
            if (this.placementMode) {
                event.preventDefault();
                this.exitPlacementMode();
            } else if (this.moveMode) {
                event.preventDefault();
                this.exitMoveMode();
            }
        });
    }
    
    // Building placement methods
    enterPlacementMode(buildingType) {
        console.log('Entering placement mode for:', buildingType);
        console.log('Building data available:', !!this.buildingData);
        console.log('Building data keys:', Object.keys(this.buildingData || {}));

        if (!this.buildingData[buildingType]) {
            console.error('Unknown building type:', buildingType);
            alert(`Building type "${buildingType}" not found!`);
            return false;
        }

        const building = this.buildingData[buildingType];
        console.log('Building data:', building);

        // Check if player can afford the building
        if (!this.canAffordBuilding(building)) {
            console.log('Cannot afford building');
            this.emit('buildingNotAffordable', building);
            alert(`Not enough ${building.cost.type === 'donuts' ? 'donuts' : 'money'} to build ${building.name}!`);
            return false;
        }

        // Check if building is unlocked
        if (!this.isBuildingUnlocked(building)) {
            console.log('Building not unlocked');
            this.emit('buildingNotUnlocked', building);
            alert(`${building.name} unlocks at level ${building.unlockLevel}!`);
            return false;
        }

        this.placementMode = true;
        this.selectedBuilding = buildingType;
        this.createPlacementPreview(building);

        console.log('Placement mode entered successfully');
        this.emit('placementModeEntered', buildingType);
        return true;
    }
    
    exitPlacementMode() {
        this.placementMode = false;
        this.selectedBuilding = null;

        if (this.placementPreview) {
            this.engine3D.removeBuilding(this.placementPreview);
            this.placementPreview = null;
        }

        if (this.placementGrid) {
            this.engine3D.removeBuilding(this.placementGrid);
            this.placementGrid = null;
        }

        this.emit('placementModeExited');
    }

    // Move mode methods
    startMoveMode(building) {
        this.moveMode = true;
        this.buildingToMove = building;

        // Make the building semi-transparent to show it's being moved
        building.traverse(child => {
            if (child.isMesh) {
                child.material = child.material.clone();
                child.material.transparent = true;
                child.material.opacity = 0.6;
            }
        });

        this.emit('moveModeEntered', building);
    }

    exitMoveMode() {
        if (this.buildingToMove) {
            // Restore building opacity
            this.buildingToMove.traverse(child => {
                if (child.isMesh) {
                    child.material.transparent = false;
                    child.material.opacity = 1.0;
                }
            });
        }

        this.moveMode = false;
        this.buildingToMove = null;

        this.emit('moveModeExited');
    }

    moveBuildingTo(position) {
        if (!this.buildingToMove || !this.moveMode) return false;

        const snappedPosition = this.snapToGrid(position);

        // Check if the new position is valid (excluding the building being moved)
        const originalPosition = this.buildingToMove.position.clone();
        this.buildingToMove.position.set(999, 999, 999); // Temporarily move out of the way

        const isValid = this.isValidPlacement(snappedPosition, this.buildingToMove.userData.type);

        if (isValid) {
            // Move the building to the new position
            this.buildingToMove.position.copy(snappedPosition);

            // Exit move mode
            this.exitMoveMode();

            this.emit('buildingMoved', {
                building: this.buildingToMove,
                oldPosition: originalPosition,
                newPosition: snappedPosition
            });

            console.log(`Moved ${this.buildingToMove.userData.name} to`, snappedPosition);
            return true;
        } else {
            // Restore original position
            this.buildingToMove.position.copy(originalPosition);

            // Show error message
            this.emit('invalidMove', { building: this.buildingToMove, position: snappedPosition });
            return false;
        }
    }
    
    createPlacementPreview(building) {
        // Create enhanced placement preview with better visuals
        const geometry = this.createBuildingGeometry(building);

        let preview;

        if (geometry instanceof THREE.Group) {
            // Handle complex building geometry
            preview = geometry.clone();
            this.applyPreviewMaterialsToGroup(preview);
        } else {
            // Handle simple geometry
            const material = new THREE.MeshPhongMaterial({
                color: 0x00ff00,
                transparent: true,
                opacity: 0.6,
                wireframe: true,
                emissive: 0x002200
            });
            preview = new THREE.Mesh(geometry, material);
        }

        this.placementPreview = preview;
        this.placementPreview.userData = {
            isPreview: true,
            buildingType: building.id
        };

        // Add enhanced pulsing animation with color changes
        const animate = () => {
            if (this.placementPreview && this.placementMode) {
                const time = Date.now() * 0.005;
                const pulse = 0.4 + Math.sin(time) * 0.2;

                // Update opacity and scale for all materials
                this.placementPreview.traverse((child) => {
                    if (child.isMesh && child.material) {
                        child.material.opacity = pulse;
                        // Add subtle scale pulsing
                        const scale = 1 + Math.sin(time * 1.5) * 0.05;
                        child.scale.setScalar(scale);
                    }
                });

                // Add rotation for visual appeal
                this.placementPreview.rotation.y += 0.01;

                requestAnimationFrame(animate);
            }
        };
        animate();

        // Add placement grid indicator
        this.addPlacementGrid();

        this.engine3D.addBuilding(this.placementPreview);
    }

    applyPreviewMaterialsToGroup(group) {
        group.traverse((child) => {
            if (child.isMesh) {
                child.material = new THREE.MeshPhongMaterial({
                    color: 0x00ff00,
                    transparent: true,
                    opacity: 0.6,
                    wireframe: true,
                    emissive: 0x002200
                });
            }
        });
    }

    addPlacementGrid() {
        // Create a grid indicator around the placement area
        const gridSize = 10;
        const gridHelper = new THREE.GridHelper(gridSize, 10, 0x00ff00, 0x004400);
        gridHelper.material.transparent = true;
        gridHelper.material.opacity = 0.3;
        gridHelper.userData.isPlacementGrid = true;

        this.placementGrid = gridHelper;
        this.engine3D.addBuilding(this.placementGrid);
    }
    
    updatePlacementPreview(position) {
        if (this.placementPreview && position) {
            // Snap to grid
            const snappedPosition = this.snapToGrid(position);
            this.placementPreview.position.copy(snappedPosition);

            // Check if position is valid
            const isValid = this.isValidPlacement(snappedPosition, this.selectedBuilding);
            this.placementPreview.material.color.setHex(isValid ? 0x00ff00 : 0xff0000);
        }
    }

    updatePlacementPreviewFromMouse(event) {
        if (!this.placementPreview) return;

        // Get mouse position and raycast to ground
        const rect = this.engine3D.canvas.getBoundingClientRect();
        const mouse = new THREE.Vector2();
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.engine3D.camera);

        // Intersect with ground plane
        const groundIntersects = raycaster.intersectObjects(this.engine3D.terrain.children);
        if (groundIntersects.length > 0) {
            this.updatePlacementPreview(groundIntersects[0].point);
        }
    }
    
    placeBuildingAt(position) {
        if (!this.selectedBuilding || !this.placementMode) return false;

        const building = this.buildingData[this.selectedBuilding];
        const snappedPosition = this.snapToGrid(position);

        // Check if building is unlocked
        if (this.questSystem && !this.questSystem.isUnlocked('building', this.selectedBuilding)) {
            this.emit('buildingNotUnlocked', { building, requirements: this.questSystem.getUnlockRequirements('building', this.selectedBuilding) });
            return false;
        }

        // Validate placement
        if (!this.isValidPlacement(snappedPosition, this.selectedBuilding)) {
            this.emit('invalidPlacement', { position: snappedPosition, building });
            return false;
        }

        // Check affordability again
        if (!this.canAffordBuilding(building)) {
            this.emit('buildingNotAffordable', building);
            return false;
        }
        
        // Deduct cost
        if (building.cost.type === 'money') {
            this.currencySystem.spendMoney(building.cost.amount, `building_${building.id}`);
        } else if (building.cost.type === 'donuts') {
            this.currencySystem.spendDonuts(building.cost.amount, `building_${building.id}`);
        }
        
        // Create and place the building
        const buildingInstance = this.createBuilding(building, snappedPosition);
        this.placedBuildings.set(buildingInstance.userData.id, buildingInstance);
        
        // Award XP
        this.currencySystem.addXP(building.xpReward, `building_${building.id}`);
        
        // Exit placement mode
        this.exitPlacementMode();
        
        this.emit('buildingPlaced', {
            building: buildingInstance,
            type: this.selectedBuilding,
            position: snappedPosition
        });

        // Show success message
        this.showBuildingPlacedMessage(building);

        console.log(`Placed ${building.name} at`, snappedPosition);
        return true;
    }
    
    createBuilding(buildingData, position) {
        const geometry = this.createBuildingGeometry(buildingData);
        const materials = this.createBuildingMaterial(buildingData);

        let building;

        if (geometry instanceof THREE.Group) {
            // Handle complex building with multiple parts
            building = geometry;
            this.applyMaterialsToGroup(building, materials);
        } else {
            // Handle simple building
            building = new THREE.Mesh(geometry, materials[0] || materials);
        }

        building.position.copy(position);
        this.setupBuildingShadows(building);

        // Add building data
        building.userData = {
            id: this.generateBuildingId(),
            type: buildingData.id,
            name: buildingData.name,
            level: 1,
            income: buildingData.income,
            lastCollection: Date.now(),
            isBuilding: true,
            isRoad: buildingData.category === 'roads'
        };

        this.engine3D.addBuilding(building);

        // Start income generation (only for non-road buildings)
        if (buildingData.category !== 'roads') {
            this.startIncomeGeneration(building);
        }

        // Add subtle entrance animation
        this.animateBuildingEntrance(building);

        return building;
    }

    applyMaterialsToGroup(group, materials) {
        let materialIndex = 0;
        group.traverse((child) => {
            if (child.isMesh) {
                child.material = materials[materialIndex % materials.length] || materials[0];
                materialIndex++;
            }
        });
    }

    setupBuildingShadows(building) {
        building.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });
    }

    animateBuildingEntrance(building) {
        // Store original scale
        const originalScale = building.scale.clone();

        // Start small and grow
        building.scale.set(0.1, 0.1, 0.1);

        const startTime = Date.now();
        const duration = 800; // 800ms animation

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOut = 1 - Math.pow(1 - progress, 3);

            building.scale.lerpVectors(
                new THREE.Vector3(0.1, 0.1, 0.1),
                originalScale,
                easeOut
            );

            // Add slight bounce at the end
            if (progress > 0.8) {
                const bounceProgress = (progress - 0.8) / 0.2;
                const bounce = 1 + Math.sin(bounceProgress * Math.PI * 2) * 0.1 * (1 - bounceProgress);
                building.scale.multiplyScalar(bounce);
            }

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                building.scale.copy(originalScale);
            }
        };

        animate();
    }
    
    createBuildingGeometry(building) {
        // Create more detailed building geometries based on building type
        const width = building.size.width;
        const height = building.size.height;
        const depth = building.size.depth;

        // Create building-specific geometries
        switch (building.id) {
            case 'simpson_house':
                return this.createHouseGeometry(width, height, depth);
            case 'kwik_e_mart':
                return this.createStoreGeometry(width, height, depth);
            case 'moes_tavern':
                return this.createTavernGeometry(width, height, depth);
            case 'springfield_elementary':
                return this.createSchoolGeometry(width, height, depth);
            case 'nuclear_power_plant':
                return this.createPowerPlantGeometry(width, height, depth);
            case 'krusty_burger':
                return this.createRestaurantGeometry(width, height, depth);

            // Road geometries
            case 'road_straight':
                return this.createStraightRoadGeometry(width, height, depth);
            case 'road_corner':
                return this.createCornerRoadGeometry(width, height, depth);
            case 'road_intersection':
                return this.createIntersectionRoadGeometry(width, height, depth);
            case 'road_t_junction':
                return this.createTJunctionRoadGeometry(width, height, depth);

            default:
                return this.createGenericBuildingGeometry(width, height, depth);
        }
    }

    createHouseGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main house body
        const houseGeometry = new THREE.BoxGeometry(width, height * 0.7, depth);
        const house = new THREE.Mesh(houseGeometry);
        house.position.y = height * 0.35;
        group.add(house);

        // Roof
        const roofGeometry = new THREE.ConeGeometry(width * 0.8, height * 0.5, 4);
        const roof = new THREE.Mesh(roofGeometry);
        roof.position.y = height * 0.7 + height * 0.25;
        roof.rotation.y = Math.PI / 4;
        group.add(roof);

        // Chimney
        const chimneyGeometry = new THREE.BoxGeometry(width * 0.15, height * 0.3, depth * 0.15);
        const chimney = new THREE.Mesh(chimneyGeometry);
        chimney.position.set(width * 0.25, height * 0.85, depth * 0.25);
        group.add(chimney);

        return group;
    }

    createStoreGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main store body
        const storeGeometry = new THREE.BoxGeometry(width, height * 0.8, depth);
        const store = new THREE.Mesh(storeGeometry);
        store.position.y = height * 0.4;
        group.add(store);

        // Store sign
        const signGeometry = new THREE.BoxGeometry(width * 0.8, height * 0.2, depth * 0.1);
        const sign = new THREE.Mesh(signGeometry);
        sign.position.set(0, height * 0.9, depth * 0.55);
        group.add(sign);

        // Entrance canopy
        const canopyGeometry = new THREE.BoxGeometry(width * 0.6, height * 0.1, depth * 0.3);
        const canopy = new THREE.Mesh(canopyGeometry);
        canopy.position.set(0, height * 0.6, depth * 0.4);
        group.add(canopy);

        return group;
    }

    createTavernGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main tavern body
        const tavernGeometry = new THREE.BoxGeometry(width, height * 0.75, depth);
        const tavern = new THREE.Mesh(tavernGeometry);
        tavern.position.y = height * 0.375;
        group.add(tavern);

        // Flat roof
        const roofGeometry = new THREE.BoxGeometry(width * 1.1, height * 0.1, depth * 1.1);
        const roof = new THREE.Mesh(roofGeometry);
        roof.position.y = height * 0.8;
        group.add(roof);

        // Neon sign
        const neonGeometry = new THREE.BoxGeometry(width * 0.4, height * 0.15, depth * 0.05);
        const neon = new THREE.Mesh(neonGeometry);
        neon.position.set(0, height * 0.6, depth * 0.55);
        group.add(neon);

        return group;
    }

    createSchoolGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main school building
        const schoolGeometry = new THREE.BoxGeometry(width, height * 0.8, depth);
        const school = new THREE.Mesh(schoolGeometry);
        school.position.y = height * 0.4;
        group.add(school);

        // School tower
        const towerGeometry = new THREE.BoxGeometry(width * 0.3, height * 0.6, depth * 0.3);
        const tower = new THREE.Mesh(towerGeometry);
        tower.position.set(0, height * 1.1, 0);
        group.add(tower);

        // Flag pole
        const poleGeometry = new THREE.CylinderGeometry(0.05, 0.05, height * 0.4);
        const pole = new THREE.Mesh(poleGeometry);
        pole.position.set(width * 0.4, height * 0.6, 0);
        group.add(pole);

        return group;
    }

    createPowerPlantGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main plant building
        const plantGeometry = new THREE.BoxGeometry(width, height * 0.6, depth);
        const plant = new THREE.Mesh(plantGeometry);
        plant.position.y = height * 0.3;
        group.add(plant);

        // Cooling towers
        for (let i = 0; i < 2; i++) {
            const towerGeometry = new THREE.CylinderGeometry(width * 0.15, width * 0.2, height * 0.8);
            const tower = new THREE.Mesh(towerGeometry);
            tower.position.set((i - 0.5) * width * 0.6, height * 0.4, depth * 0.3);
            group.add(tower);
        }

        // Reactor dome
        const domeGeometry = new THREE.SphereGeometry(width * 0.2, 16, 8);
        const dome = new THREE.Mesh(domeGeometry);
        dome.position.set(0, height * 0.7, -depth * 0.2);
        group.add(dome);

        return group;
    }

    createRestaurantGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main restaurant body
        const restaurantGeometry = new THREE.BoxGeometry(width, height * 0.7, depth);
        const restaurant = new THREE.Mesh(restaurantGeometry);
        restaurant.position.y = height * 0.35;
        group.add(restaurant);

        // Drive-thru window
        const windowGeometry = new THREE.BoxGeometry(width * 0.3, height * 0.2, depth * 0.1);
        const window = new THREE.Mesh(windowGeometry);
        window.position.set(width * 0.4, height * 0.4, depth * 0.55);
        group.add(window);

        // Restaurant sign
        const signGeometry = new THREE.CylinderGeometry(width * 0.1, width * 0.1, height * 0.8);
        const sign = new THREE.Mesh(signGeometry);
        sign.position.set(-width * 0.6, height * 0.4, 0);
        group.add(sign);

        return group;
    }

    createGenericBuildingGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main building
        const buildingGeometry = new THREE.BoxGeometry(width, height * 0.8, depth);
        const building = new THREE.Mesh(buildingGeometry);
        building.position.y = height * 0.4;
        group.add(building);

        // Simple roof
        const roofGeometry = new THREE.BoxGeometry(width * 1.05, height * 0.1, depth * 1.05);
        const roof = new THREE.Mesh(roofGeometry);
        roof.position.y = height * 0.85;
        group.add(roof);

        return group;
    }

    // Road geometry creation methods
    createStraightRoadGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main road surface
        const roadGeometry = new THREE.BoxGeometry(width, height, depth);
        const road = new THREE.Mesh(roadGeometry);
        road.position.y = height / 2;
        group.add(road);

        // Road markings - center line
        const lineGeometry = new THREE.BoxGeometry(width * 0.05, height + 0.01, depth * 0.8);
        const centerLine = new THREE.Mesh(lineGeometry);
        centerLine.position.y = height / 2 + 0.005;
        group.add(centerLine);

        return group;
    }

    createCornerRoadGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main road surface
        const roadGeometry = new THREE.BoxGeometry(width, height, depth);
        const road = new THREE.Mesh(roadGeometry);
        road.position.y = height / 2;
        group.add(road);

        // Corner markings - curved line
        const segments = 8;
        for (let i = 0; i < segments; i++) {
            const angle = (i / segments) * Math.PI / 2;
            const x = Math.cos(angle) * width * 0.3;
            const z = Math.sin(angle) * depth * 0.3;

            const markGeometry = new THREE.BoxGeometry(0.1, height + 0.01, 0.1);
            const mark = new THREE.Mesh(markGeometry);
            mark.position.set(x, height / 2 + 0.005, z);
            group.add(mark);
        }

        return group;
    }

    createIntersectionRoadGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main road surface
        const roadGeometry = new THREE.BoxGeometry(width, height, depth);
        const road = new THREE.Mesh(roadGeometry);
        road.position.y = height / 2;
        group.add(road);

        // Intersection markings - crosswalk lines
        const lineWidth = 0.05;
        const lineSpacing = 0.2;

        // Horizontal crosswalk
        for (let i = -2; i <= 2; i++) {
            const lineGeometry = new THREE.BoxGeometry(width * 0.6, height + 0.01, lineWidth);
            const line = new THREE.Mesh(lineGeometry);
            line.position.set(0, height / 2 + 0.005, i * lineSpacing);
            group.add(line);
        }

        // Vertical crosswalk
        for (let i = -2; i <= 2; i++) {
            const lineGeometry = new THREE.BoxGeometry(lineWidth, height + 0.01, depth * 0.6);
            const line = new THREE.Mesh(lineGeometry);
            line.position.set(i * lineSpacing, height / 2 + 0.005, 0);
            group.add(line);
        }

        return group;
    }

    createTJunctionRoadGeometry(width, height, depth) {
        const group = new THREE.Group();

        // Main road surface
        const roadGeometry = new THREE.BoxGeometry(width, height, depth);
        const road = new THREE.Mesh(roadGeometry);
        road.position.y = height / 2;
        group.add(road);

        // T-junction markings
        const lineGeometry = new THREE.BoxGeometry(width * 0.05, height + 0.01, depth * 0.4);
        const centerLine = new THREE.Mesh(lineGeometry);
        centerLine.position.set(0, height / 2 + 0.005, depth * 0.3);
        group.add(centerLine);

        // Stop line
        const stopLineGeometry = new THREE.BoxGeometry(width * 0.8, height + 0.01, 0.1);
        const stopLine = new THREE.Mesh(stopLineGeometry);
        stopLine.position.set(0, height / 2 + 0.005, -depth * 0.4);
        group.add(stopLine);

        return group;
    }
    
    createBuildingMaterial(building) {
        // Create enhanced materials with multiple materials for different parts
        const materials = this.getBuildingMaterials(building);

        if (Array.isArray(materials)) {
            return materials;
        }
        return materials;
    }

    getBuildingMaterials(building) {
        switch (building.id) {
            case 'simpson_house':
                return this.createHouseMaterials();
            case 'kwik_e_mart':
                return this.createStoreMaterials();
            case 'moes_tavern':
                return this.createTavernMaterials();
            case 'springfield_elementary':
                return this.createSchoolMaterials();
            case 'nuclear_power_plant':
                return this.createPowerPlantMaterials();
            case 'krusty_burger':
                return this.createRestaurantMaterials();

            // Road materials
            case 'road_straight':
            case 'road_corner':
            case 'road_intersection':
            case 'road_t_junction':
                return this.createRoadMaterials();

            default:
                return this.createGenericBuildingMaterials(building);
        }
    }

    createHouseMaterials() {
        return [
            // Main house - Yellow
            new THREE.MeshPhongMaterial({
                color: 0xFFD700,
                shininess: 30,
                specular: 0x111111
            }),
            // Roof - Dark red
            new THREE.MeshPhongMaterial({
                color: 0x8B0000,
                shininess: 10
            }),
            // Chimney - Red brick
            new THREE.MeshPhongMaterial({
                color: 0xB22222,
                shininess: 5
            })
        ];
    }

    createStoreMaterials() {
        return [
            // Main store - Blue
            new THREE.MeshPhongMaterial({
                color: 0x4169E1,
                shininess: 40
            }),
            // Sign - Bright yellow
            new THREE.MeshPhongMaterial({
                color: 0xFFFF00,
                emissive: 0x222200,
                shininess: 80
            }),
            // Canopy - White
            new THREE.MeshPhongMaterial({
                color: 0xFFFFFF,
                shininess: 20
            })
        ];
    }

    createTavernMaterials() {
        return [
            // Main tavern - Dark brown
            new THREE.MeshPhongMaterial({
                color: 0x8B4513,
                shininess: 20
            }),
            // Roof - Dark gray
            new THREE.MeshPhongMaterial({
                color: 0x2F2F2F,
                shininess: 10
            }),
            // Neon sign - Bright red with glow
            new THREE.MeshPhongMaterial({
                color: 0xFF0000,
                emissive: 0x440000,
                shininess: 100
            })
        ];
    }

    createSchoolMaterials() {
        return [
            // Main school - Red brick
            new THREE.MeshPhongMaterial({
                color: 0xFF6347,
                shininess: 15
            }),
            // Tower - Darker red
            new THREE.MeshPhongMaterial({
                color: 0xCD5C5C,
                shininess: 20
            }),
            // Flag pole - Metal
            new THREE.MeshPhongMaterial({
                color: 0xC0C0C0,
                shininess: 90,
                specular: 0x444444
            })
        ];
    }

    createPowerPlantMaterials() {
        return [
            // Main plant - Industrial gray
            new THREE.MeshPhongMaterial({
                color: 0x696969,
                shininess: 30
            }),
            // Cooling towers - Concrete
            new THREE.MeshPhongMaterial({
                color: 0xD3D3D3,
                shininess: 10
            }),
            // Reactor dome - Metallic
            new THREE.MeshPhongMaterial({
                color: 0x708090,
                shininess: 80,
                specular: 0x333333
            })
        ];
    }

    createRestaurantMaterials() {
        return [
            // Main restaurant - Bright red
            new THREE.MeshPhongMaterial({
                color: 0xFF1493,
                shininess: 40
            }),
            // Drive-thru window - Glass-like
            new THREE.MeshPhongMaterial({
                color: 0x87CEEB,
                transparent: true,
                opacity: 0.7,
                shininess: 100
            }),
            // Sign - Yellow with red
            new THREE.MeshPhongMaterial({
                color: 0xFFD700,
                emissive: 0x331100,
                shininess: 60
            })
        ];
    }

    createRoadMaterials() {
        return [
            // Road surface - Dark asphalt
            new THREE.MeshPhongMaterial({
                color: 0x2F2F2F,
                shininess: 5,
                specular: 0x111111
            }),
            // Road markings - Bright white/yellow
            new THREE.MeshPhongMaterial({
                color: 0xFFFFFF,
                shininess: 10,
                emissive: 0x111111
            })
        ];
    }

    createGenericBuildingMaterials(building) {
        const categoryColors = {
            residential: 0x8FBC8F,
            commercial: 0x4169E1,
            entertainment: 0xFF69B4,
            community: 0xDDA0DD,
            industrial: 0x696969,
            roads: 0x2F2F2F,
            decorations: 0x32CD32
        };

        const color = categoryColors[building.category] || 0x808080;

        return [
            new THREE.MeshPhongMaterial({
                color: color,
                shininess: 30
            }),
            new THREE.MeshPhongMaterial({
                color: color * 0.8,
                shininess: 20
            })
        ];
    }
    
    // Utility methods
    snapToGrid(position, gridSize = 1) {
        return new THREE.Vector3(
            Math.round(position.x / gridSize) * gridSize,
            0,
            Math.round(position.z / gridSize) * gridSize
        );
    }
    
    isValidPlacement(position, buildingType) {
        const building = this.buildingData[buildingType];
        if (!building) return false;
        
        // Check for overlaps with existing buildings
        for (const [id, existingBuilding] of this.placedBuildings) {
            const distance = position.distanceTo(existingBuilding.position);
            const minDistance = (building.size.width + existingBuilding.userData.size?.width || 2) / 2;
            
            if (distance < minDistance) {
                return false;
            }
        }
        
        // Check bounds (keep buildings within a reasonable area)
        const maxDistance = 40;
        if (Math.abs(position.x) > maxDistance || Math.abs(position.z) > maxDistance) {
            return false;
        }
        
        return true;
    }
    
    canAffordBuilding(building) {
        if (building.cost.type === 'free') return true;
        if (building.cost.type === 'money') return this.currencySystem.canAfford(building.cost.amount);
        if (building.cost.type === 'donuts') return this.currencySystem.canAffordDonuts(building.cost.amount);
        return false;
    }
    
    isBuildingUnlocked(building) {
        return this.currencySystem.getLevel() >= building.unlockLevel;
    }
    
    generateBuildingId() {
        return 'building_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // Income generation
    startIncomeGeneration(building) {
        const income = building.userData.income;
        if (!income) return;

        // Mark building as income generator for real-time updates
        building.userData.isIncomeGenerator = true;
        building.userData.lastIncomeUpdate = Date.now();
        building.userData.pendingIncome = building.userData.pendingIncome || 0;

        // Show initial income indicator
        this.showIncomeIndicator(building);
    }

    // Real-time update method for building system
    update(deltaTime) {
        // Update all placed buildings
        for (const [id, building] of this.placedBuildings) {
            this.updateBuilding(building, deltaTime);
        }

        // Update building animations
        this.updateBuildingAnimations(deltaTime);
    }

    updateBuilding(building, deltaTime) {
        // Update income generation in real-time
        if (building.userData.isIncomeGenerator && building.userData.income) {
            this.updateIncomeGeneration(building, deltaTime);
        }

        // Update building visual effects
        this.updateBuildingEffects(building, deltaTime);
    }

    updateIncomeGeneration(building, deltaTime) {
        const now = Date.now();
        const timeSinceLastUpdate = now - (building.userData.lastIncomeUpdate || now);

        // Calculate income based on real-time progression
        const incomePerSecond = building.userData.income.amount / building.userData.income.interval;
        const incomeGenerated = incomePerSecond * (timeSinceLastUpdate / 1000);

        if (incomeGenerated > 0) {
            building.userData.pendingIncome = (building.userData.pendingIncome || 0) + incomeGenerated;
            building.userData.lastIncomeUpdate = now;

            // Update income indicator in real-time
            this.updateIncomeIndicator(building);

            // Emit continuous income event
            this.emit('incomeGenerated', {
                building: building,
                amount: incomeGenerated,
                total: building.userData.pendingIncome
            });
        }
    }

    updateBuildingEffects(building, deltaTime) {
        // Add subtle breathing animation to buildings
        if (!building.userData.animationOffset) {
            building.userData.animationOffset = Math.random() * Math.PI * 2;
        }

        const time = Date.now() * 0.001 + building.userData.animationOffset;
        const breathe = Math.sin(time * 0.5) * 0.02 + 1;

        // Apply subtle scale animation
        if (building.userData.isIncomeGenerator && building.userData.pendingIncome > 0) {
            building.scale.setScalar(breathe);
        }

        // Add glow effect for buildings with pending income
        if (building.userData.pendingIncome > 10) {
            this.addBuildingGlow(building, time);
        }
    }

    addBuildingGlow(building, time) {
        // Create or update glow effect
        if (!building.userData.glowEffect) {
            const glowGeometry = new THREE.SphereGeometry(2, 16, 8);
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: 0xFFD700,
                transparent: true,
                opacity: 0.2
            });

            building.userData.glowEffect = new THREE.Mesh(glowGeometry, glowMaterial);
            building.userData.glowEffect.position.copy(building.position);
            building.userData.glowEffect.position.y += 1;

            this.engine3D.addBuilding(building.userData.glowEffect);
        }

        // Animate glow
        const pulse = Math.sin(time * 2) * 0.1 + 0.3;
        building.userData.glowEffect.material.opacity = pulse;
        building.userData.glowEffect.scale.setScalar(1 + pulse * 0.5);
    }

    updateBuildingAnimations(deltaTime) {
        // Update any ongoing building animations
        this.updatePlacementAnimations(deltaTime);
        this.updateUpgradeAnimations(deltaTime);
    }

    updatePlacementAnimations(deltaTime) {
        // Handle any ongoing placement animations
        if (!this.placementAnimations) {
            this.placementAnimations = new Map();
        }

        this.placementAnimations.forEach((animation, building) => {
            animation.progress += deltaTime * 2; // 0.5 second animation

            if (animation.progress >= 1) {
                // Animation complete
                building.scale.copy(animation.targetScale);
                this.placementAnimations.delete(building);
            } else {
                // Interpolate scale
                const eased = this.easeOutBounce(animation.progress);
                building.scale.lerpVectors(animation.startScale, animation.targetScale, eased);
            }
        });
    }

    updateUpgradeAnimations(deltaTime) {
        // Handle upgrade animations
        if (!this.upgradeAnimations) {
            this.upgradeAnimations = new Map();
        }

        this.upgradeAnimations.forEach((animation, building) => {
            animation.progress += deltaTime * 3; // Faster upgrade animation

            if (animation.progress >= 1) {
                // Animation complete
                building.scale.copy(animation.targetScale);
                this.upgradeAnimations.delete(building);
            } else {
                // Bounce effect
                const bounce = Math.sin(animation.progress * Math.PI * 4) * 0.1 + 1;
                building.scale.setScalar(animation.baseScale * bounce);
            }
        });
    }

    updateIncomeIndicator(building) {
        // Update the income indicator with current pending amount
        if (building.userData.incomeIndicator) {
            const pendingIncome = building.userData.pendingIncome || 0;

            // Change color based on income amount
            if (pendingIncome > 50) {
                building.userData.incomeIndicator.material.color.setHex(0xFF6B35); // Orange for high income
            } else if (pendingIncome > 20) {
                building.userData.incomeIndicator.material.color.setHex(0xFFD700); // Gold for medium income
            } else {
                building.userData.incomeIndicator.material.color.setHex(0x4CAF50); // Green for low income
            }

            // Scale based on income amount
            const baseScale = 1 + Math.min(pendingIncome / 100, 1);
            const time = Date.now() * 0.008;
            const pulse = 1 + Math.sin(time * 1.5) * 0.2;
            building.userData.incomeIndicator.scale.setScalar(baseScale * pulse);
        }
    }

    easeOutBounce(t) {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    }
    
    collectIncome(building) {
        const pendingIncome = building.userData.pendingIncome || 0;

        if (pendingIncome > 0) {
            this.currencySystem.addMoney(pendingIncome, `income_${building.userData.type}`);
            building.userData.pendingIncome = 0;

            this.hideIncomeIndicator(building);

            // Show collection effect
            this.showCollectionEffect(building, pendingIncome);

            this.emit('incomeCollected', {
                building: building,
                amount: pendingIncome
            });

            return pendingIncome;
        }

        return 0;
    }

    showCollectionEffect(building, amount) {
        // Create enhanced floating text with better visuals
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 512;
        canvas.height = 128;

        // Create gradient background
        const gradient = context.createLinearGradient(0, 0, 0, 128);
        gradient.addColorStop(0, 'rgba(255, 215, 0, 0.9)');
        gradient.addColorStop(1, 'rgba(255, 165, 0, 0.9)');

        // Draw background with rounded corners effect
        context.fillStyle = gradient;
        context.fillRect(0, 0, 512, 128);

        // Add border
        context.strokeStyle = '#FF6B35';
        context.lineWidth = 6;
        context.strokeRect(3, 3, 506, 122);

        // Draw text with shadow and glow
        context.shadowColor = 'rgba(0, 0, 0, 0.7)';
        context.shadowBlur = 8;
        context.shadowOffsetX = 3;
        context.shadowOffsetY = 3;

        context.fillStyle = '#FFFFFF';
        context.font = 'Bold 42px Arial';
        context.textAlign = 'center';
        context.fillText(`+$${amount}`, 256, 80);

        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({
            map: texture,
            transparent: true,
            opacity: 1
        });

        const textSprite = new THREE.Sprite(material);
        textSprite.scale.set(8, 2, 1);
        textSprite.position.copy(building.position);

        // Get building height more reliably
        const buildingHeight = this.getBuildingHeight(building);
        textSprite.position.y += buildingHeight + 3;

        this.engine3D.addBuilding(textSprite);

        // Enhanced animation with bounce, scale, and rotation effects
        let opacity = 1;
        let yOffset = 0;
        let scale = 1;
        let rotation = 0;
        const startTime = Date.now();
        const duration = 2500; // 2.5 seconds

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress < 1) {
                // Bounce up with easing
                yOffset += 0.12 * (1 - progress * 0.6);

                // Scale animation with bounce
                if (progress < 0.3) {
                    scale = 1 + Math.sin(progress * Math.PI * 10) * 0.2;
                } else {
                    scale = 1 + Math.sin(progress * Math.PI) * 0.4;
                }

                // Gentle rotation
                rotation += 0.02;

                // Fade out with easing
                opacity = 1 - Math.pow(progress, 1.5);

                // Apply transformations
                textSprite.position.y = building.position.y + buildingHeight + 3 + yOffset;
                textSprite.material.opacity = opacity;
                textSprite.scale.set(8 * scale, 2 * scale, 1);
                textSprite.material.rotation = rotation;

                requestAnimationFrame(animate);
            } else {
                this.engine3D.removeBuilding(textSprite);
                texture.dispose();
                material.dispose();
            }
        };

        animate();

        // Add enhanced particle effect
        this.createIncomeParticles(building, buildingHeight);

        // Add sound effect trigger
        this.emit('incomeCollectionEffect', { building, amount });
    }

    getBuildingHeight(building) {
        let height = 2; // Default height

        if (building.geometry && building.geometry.parameters) {
            height = building.geometry.parameters.height || 2;
        } else {
            // For complex buildings, calculate bounding box
            const box = new THREE.Box3().setFromObject(building);
            height = box.max.y - box.min.y;
        }

        return height;
    }

    createIncomeParticles(building, buildingHeight) {
        // Create enhanced sparkle particles for income collection
        const particleCount = 12;
        const particles = [];

        for (let i = 0; i < particleCount; i++) {
            const particleGeometry = new THREE.SphereGeometry(0.15, 8, 6);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: i % 2 === 0 ? 0xFFD700 : 0xFFA500,
                transparent: true,
                opacity: 0.9
            });

            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            particle.position.copy(building.position);
            particle.position.y += buildingHeight + 1;

            // Random offset in a circle
            const angle = (i / particleCount) * Math.PI * 2;
            const radius = 1 + Math.random() * 0.5;
            particle.position.x += Math.cos(angle) * radius;
            particle.position.z += Math.sin(angle) * radius;

            // Random velocity with upward bias
            particle.userData.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 0.15,
                Math.random() * 0.2 + 0.15,
                (Math.random() - 0.5) * 0.15
            );

            // Random rotation speed
            particle.userData.rotationSpeed = new THREE.Vector3(
                (Math.random() - 0.5) * 0.1,
                (Math.random() - 0.5) * 0.1,
                (Math.random() - 0.5) * 0.1
            );

            particles.push(particle);
            this.engine3D.addBuilding(particle);
        }

        // Animate particles with enhanced effects
        const animateParticles = () => {
            let activeParticles = 0;

            particles.forEach(particle => {
                if (particle.material.opacity > 0) {
                    activeParticles++;

                    // Update position
                    particle.position.add(particle.userData.velocity);

                    // Update rotation
                    particle.rotation.x += particle.userData.rotationSpeed.x;
                    particle.rotation.y += particle.userData.rotationSpeed.y;
                    particle.rotation.z += particle.userData.rotationSpeed.z;

                    // Apply gravity
                    particle.userData.velocity.y -= 0.008;

                    // Add air resistance
                    particle.userData.velocity.multiplyScalar(0.98);

                    // Fade out and scale down
                    particle.material.opacity -= 0.015;
                    const scale = particle.material.opacity;
                    particle.scale.setScalar(scale);
                }
            });

            if (activeParticles > 0) {
                requestAnimationFrame(animateParticles);
            } else {
                // Clean up particles
                particles.forEach(particle => {
                    this.engine3D.removeBuilding(particle);
                    particle.geometry.dispose();
                    particle.material.dispose();
                });
            }
        };

        animateParticles();
    }

    showIncomeIndicator(building) {
        // Add a visual indicator above the building
        if (!building.userData.incomeIndicator) {
            const geometry = new THREE.SphereGeometry(0.3, 8, 6);
            const material = new THREE.MeshBasicMaterial({
                color: 0xFFD700,
                transparent: true,
                opacity: 0.8
            });
            const indicator = new THREE.Mesh(geometry, material);

            indicator.position.set(0, building.geometry.parameters.height + 1.5, 0);
            building.add(indicator);
            building.userData.incomeIndicator = indicator;

            // Add a pulsing animation to make it more noticeable
            const animate = () => {
                if (building.userData.incomeIndicator) {
                    const time = Date.now() * 0.008;
                    indicator.position.y = building.geometry.parameters.height + 1.5 + Math.sin(time) * 0.3;
                    indicator.scale.setScalar(1 + Math.sin(time * 1.5) * 0.2);
                    indicator.material.opacity = 0.6 + Math.sin(time * 2) * 0.2;
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }
    }
    
    hideIncomeIndicator(building) {
        if (building.userData.incomeIndicator) {
            building.remove(building.userData.incomeIndicator);
            building.userData.incomeIndicator = null;
        }
    }
    
    // Building management
    selectBuilding(building) {
        if (this.placementMode || this.moveMode) return;

        // If building has pending income, collect it first
        if (building.userData.pendingIncome > 0) {
            this.collectIncome(building);
        } else {
            // Otherwise show building info
            this.emit('buildingSelected', building);
        }
    }
    
    upgradeBuilding(buildingId) {
        const building = this.placedBuildings.get(buildingId);
        if (!building) return false;
        
        const buildingData = this.buildingData[building.userData.type];
        const currentLevel = building.userData.level;
        const upgrade = buildingData.upgrades?.find(u => u.level === currentLevel + 1);
        
        if (!upgrade) return false;
        
        // Check if player can afford upgrade
        if (upgrade.cost.type === 'money' && !this.currencySystem.canAfford(upgrade.cost.amount)) {
            return false;
        }
        if (upgrade.cost.type === 'donuts' && !this.currencySystem.canAffordDonuts(upgrade.cost.amount)) {
            return false;
        }
        
        // Deduct cost
        if (upgrade.cost.type === 'money') {
            this.currencySystem.spendMoney(upgrade.cost.amount, `upgrade_${building.userData.type}`);
        } else if (upgrade.cost.type === 'donuts') {
            this.currencySystem.spendDonuts(upgrade.cost.amount, `upgrade_${building.userData.type}`);
        }
        
        // Apply upgrade
        building.userData.level = currentLevel + 1;
        building.userData.income = upgrade.income;
        
        // Visual upgrade (scale up slightly)
        building.scale.multiplyScalar(1.1);
        
        this.emit('buildingUpgraded', {
            building: building,
            newLevel: building.userData.level
        });
        
        return true;
    }
    
    removeBuilding(buildingId) {
        const building = this.placedBuildings.get(buildingId);
        if (!building) return false;
        
        // Stop income generation
        if (building.userData.incomeInterval) {
            clearInterval(building.userData.incomeInterval);
        }
        
        // Remove from scene
        this.engine3D.removeBuilding(building);
        this.placedBuildings.delete(buildingId);
        
        this.emit('buildingRemoved', building);
        return true;
    }
    
    // Category management
    setCategory(category) {
        if (this.categories[category]) {
            this.currentCategory = category;
            this.emit('categoryChanged', category);
        }
    }
    
    getBuildingsInCategory(category) {
        if (!this.buildingData) {
            console.log('No building data available yet');
            return [];
        }

        const buildings = Object.values(this.buildingData).filter(building =>
            building.category === category
        );

        console.log(`Found ${buildings.length} buildings in category ${category}:`, buildings.map(b => b.name));
        return buildings;
    }
    
    updateBuildingAvailability() {
        this.emit('buildingAvailabilityChanged');
    }

    showBuildingPlacedMessage(building) {
        // Create a success notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            font-weight: bold;
            animation: slideInRight 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.5em;">🎉</span>
                <div>
                    <div>${building.name} built!</div>
                    <div style="font-size: 0.8em; opacity: 0.9;">+${building.xpReward} XP</div>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }
    
    // Save/Load state
    getState() {
        const buildings = [];
        
        for (const [id, building] of this.placedBuildings) {
            buildings.push({
                id: id,
                type: building.userData.type,
                position: {
                    x: building.position.x,
                    y: building.position.y,
                    z: building.position.z
                },
                level: building.userData.level,
                lastCollection: building.userData.lastCollection,
                pendingIncome: building.userData.pendingIncome || 0
            });
        }
        
        return { buildings };
    }
    
    setState(state) {
        // Clear existing buildings
        for (const [id, building] of this.placedBuildings) {
            this.removeBuilding(id);
        }
        
        // Recreate buildings from state
        if (state.buildings) {
            state.buildings.forEach(buildingState => {
                const buildingData = this.buildingData[buildingState.type];
                if (buildingData) {
                    const position = new THREE.Vector3(
                        buildingState.position.x,
                        buildingState.position.y,
                        buildingState.position.z
                    );
                    
                    const building = this.createBuilding(buildingData, position);
                    building.userData.id = buildingState.id;
                    building.userData.level = buildingState.level;
                    building.userData.lastCollection = buildingState.lastCollection;
                    building.userData.pendingIncome = buildingState.pendingIncome;
                    
                    this.placedBuildings.set(buildingState.id, building);
                }
            });
        }
    }
    
    dispose() {
        // Clean up intervals
        for (const [id, building] of this.placedBuildings) {
            if (building.userData.incomeInterval) {
                clearInterval(building.userData.incomeInterval);
            }
        }
        
        this.removeAllListeners();
    }
}
