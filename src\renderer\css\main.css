/* Springfield Town Builder - Main Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    overflow: hidden;
    user-select: none;
}

.hidden {
    display: none !important;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: #8B4513;
}

.loading-donut {
    font-size: 80px;
    animation: spin 2s linear infinite;
    margin-bottom: 20px;
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.loading-bar {
    width: 300px;
    height: 20px;
    background: rgba(255,255,255,0.3);
    border-radius: 10px;
    overflow: hidden;
    margin: 20px auto;
    border: 2px solid #8B4513;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 8px;
}

/* Main Menu */
.main-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../assets/backgrounds/springfield-bg.jpg') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 900;
}

.menu-background {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    border: 3px solid #FFD700;
}

.menu-content {
    text-align: center;
}

.game-title {
    font-size: 3em;
    color: #FF6B35;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.title-donut {
    font-size: 60px;
    animation: bounce 2s infinite;
    display: inline-block;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-button {
    padding: 15px 30px;
    font-size: 1.2em;
    font-family: inherit;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: 3px solid #FF6B35;
    border-radius: 25px;
    color: #8B4513;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5);
    min-width: 200px;
}

.menu-button:hover {
    background: linear-gradient(135deg, #FFA500 0%, #FF6B35 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.menu-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

/* Save Slot Menu */
.save-slot-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 950;
}

.save-slots {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
    max-width: 800px;
}

.save-slot {
    background: linear-gradient(135deg, #E8F4FD 0%, #B3E5FC 100%);
    border: 3px solid #2196F3;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.save-slot:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3);
    border-color: #FF6B35;
}

.save-slot.empty {
    background: linear-gradient(135deg, #F5F5F5 0%, #E0E0E0 100%);
    border-color: #9E9E9E;
    color: #666;
}

.save-slot h4 {
    font-size: 1.3em;
    margin-bottom: 10px;
    color: #1976D2;
}

.save-slot.empty h4 {
    color: #666;
}

.save-slot-info {
    font-size: 0.9em;
    color: #555;
}

.save-slot-date {
    font-size: 0.8em;
    color: #888;
    margin-top: 5px;
}

/* Game Container */
.game-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #87CEEB;
}

.game-canvas {
    width: 100%;
    height: 100%;
    display: block;
    cursor: grab;
}

.game-canvas:active {
    cursor: grabbing;
}

/* Level Up Animation */
@keyframes levelUpPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* Floating Currency Text */
.floating-currency-text {
    animation: floatUp 2s ease-out forwards;
}

@keyframes floatUp {
    0% {
        transform: translate(-50%, -50%) translateY(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translateY(-100px);
        opacity: 0;
    }
}

/* Notification Animations */
@keyframes slideInRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Collect Animation */
@keyframes collectPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* Building Placement Animation */
@keyframes buildingPlace {
    0% {
        transform: scale(0.1) rotateY(0deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotateY(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotateY(360deg);
        opacity: 1;
    }
}

/* Character Spawn Animation */
@keyframes characterSpawn {
    0% {
        transform: scale(0) translateY(-20px);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) translateY(-10px);
        opacity: 0.7;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Glow Effect for Interactive Elements */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.6);
    }
}

/* Shimmer Effect for Premium Items */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Pulse Effect for Notifications */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse-effect {
    animation: pulse 2s infinite;
}

/* Wobble Effect for Hover */
@keyframes wobble {
    0% { transform: translateX(0%); }
    15% { transform: translateX(-25%) rotate(-5deg); }
    30% { transform: translateX(20%) rotate(3deg); }
    45% { transform: translateX(-15%) rotate(-3deg); }
    60% { transform: translateX(10%) rotate(2deg); }
    75% { transform: translateX(-5%) rotate(-1deg); }
    100% { transform: translateX(0%); }
}

.wobble-effect:hover {
    animation: wobble 0.8s ease-in-out;
}

/* Sparkle Effect for Special Items */
@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}

.sparkle-effect::before {
    content: '✨';
    position: absolute;
    top: -5px;
    right: -5px;
    animation: sparkle 1.5s infinite;
}

/* Enhanced Button Hover Effects */
.enhanced-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.enhanced-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.enhanced-button:hover::before {
    left: 100%;
}

/* Floating Animation for Currency */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.float-effect {
    animation: float 3s ease-in-out infinite;
}
