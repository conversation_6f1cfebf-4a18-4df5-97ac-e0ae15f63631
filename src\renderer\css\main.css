/* Springfield Town Builder - Main Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Sans MS', cursive, sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    overflow: hidden;
    user-select: none;
}

.hidden {
    display: none !important;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: #8B4513;
}

.loading-donut {
    font-size: 80px;
    animation: spin 2s linear infinite;
    margin-bottom: 20px;
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.loading-bar {
    width: 300px;
    height: 20px;
    background: rgba(255,255,255,0.3);
    border-radius: 10px;
    overflow: hidden;
    margin: 20px auto;
    border: 2px solid #8B4513;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 8px;
}

/* Main Menu */
.main-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../../assets/backgrounds/springfield-bg.jpg') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 900;
}

.menu-background {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    border: 3px solid #FFD700;
}

.menu-content {
    text-align: center;
}

.game-title {
    font-size: 3em;
    color: #FF6B35;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.title-donut {
    font-size: 60px;
    animation: bounce 2s infinite;
    display: inline-block;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-button {
    padding: 15px 30px;
    font-size: 1.2em;
    font-family: inherit;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: 3px solid #FF6B35;
    border-radius: 25px;
    color: #8B4513;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5);
    min-width: 200px;
}

.menu-button:hover {
    background: linear-gradient(135deg, #FFA500 0%, #FF6B35 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.menu-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

/* Save Slot Menu */
.save-slot-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 950;
}

.save-slots {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
    max-width: 800px;
}

.save-slot {
    background: linear-gradient(135deg, #E8F4FD 0%, #B3E5FC 100%);
    border: 3px solid #2196F3;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.save-slot:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3);
    border-color: #FF6B35;
}

.save-slot.empty {
    background: linear-gradient(135deg, #F5F5F5 0%, #E0E0E0 100%);
    border-color: #9E9E9E;
    color: #666;
}

.save-slot h4 {
    font-size: 1.3em;
    margin-bottom: 10px;
    color: #1976D2;
}

.save-slot.empty h4 {
    color: #666;
}

.save-slot-info {
    font-size: 0.9em;
    color: #555;
}

.save-slot-date {
    font-size: 0.8em;
    color: #888;
    margin-top: 5px;
}

/* Game Container */
.game-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #87CEEB;
}

.game-canvas {
    width: 100%;
    height: 100%;
    display: block;
    cursor: grab;
}

.game-canvas:active {
    cursor: grabbing;
}

/* Level Up Animation */
@keyframes levelUpPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* Floating Currency Text */
.floating-currency-text {
    animation: floatUp 2s ease-out forwards;
}

@keyframes floatUp {
    0% {
        transform: translate(-50%, -50%) translateY(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translateY(-100px);
        opacity: 0;
    }
}

/* Notification Animations */
@keyframes slideInRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Collect Animation */
@keyframes collectPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* Building Placement Animation */
@keyframes buildingPlace {
    0% {
        transform: scale(0.1) rotateY(0deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotateY(180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotateY(360deg);
        opacity: 1;
    }
}

/* Character Spawn Animation */
@keyframes characterSpawn {
    0% {
        transform: scale(0) translateY(-20px);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) translateY(-10px);
        opacity: 0.7;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Glow Effect for Interactive Elements */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.6);
    }
}

/* Shimmer Effect for Premium Items */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Pulse Effect for Notifications */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse-effect {
    animation: pulse 2s infinite;
}

/* Wobble Effect for Hover */
@keyframes wobble {
    0% { transform: translateX(0%); }
    15% { transform: translateX(-25%) rotate(-5deg); }
    30% { transform: translateX(20%) rotate(3deg); }
    45% { transform: translateX(-15%) rotate(-3deg); }
    60% { transform: translateX(10%) rotate(2deg); }
    75% { transform: translateX(-5%) rotate(-1deg); }
    100% { transform: translateX(0%); }
}

.wobble-effect:hover {
    animation: wobble 0.8s ease-in-out;
}

/* Sparkle Effect for Special Items */
@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}

.sparkle-effect::before {
    content: '✨';
    position: absolute;
    top: -5px;
    right: -5px;
    animation: sparkle 1.5s infinite;
}

/* Enhanced Button Hover Effects */
.enhanced-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.enhanced-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.enhanced-button:hover::before {
    left: 100%;
}

/* Floating Animation for Currency */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.float-effect {
    animation: float 3s ease-in-out infinite;
}

/* Real-time UI Animations */
@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    20%, 80% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(100%);
    }
}

@keyframes countUp {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        color: #FFD700;
    }
    100% {
        transform: scale(1);
    }
}

@keyframes progressFill {
    0% {
        width: 0%;
    }
    100% {
        width: var(--progress-width);
    }
}

@keyframes realTimeUpdate {
    0% {
        background-color: rgba(76, 175, 80, 0.1);
    }
    50% {
        background-color: rgba(76, 175, 80, 0.3);
    }
    100% {
        background-color: rgba(76, 175, 80, 0.1);
    }
}

/* Real-time status indicators */
.real-time-indicator {
    position: relative;
    overflow: hidden;
}

.real-time-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

.income-rate-display {
    animation: realTimeUpdate 3s ease-in-out infinite;
}

.currency-amount {
    transition: all 0.3s ease;
}

.currency-amount.updating {
    animation: countUp 0.5s ease-out;
}

/* Task progress bars */
.task-progress-bar {
    width: 100%;
    height: 6px;
    background: #E0E0E0;
    border-radius: 3px;
    overflow: hidden;
    margin: 5px 0;
}

.task-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    border-radius: 3px;
    transition: width 0.3s ease;
    animation: progressFill 0.5s ease-out;
}

/* Auto-save indicator */
.auto-save-indicator {
    animation: fadeInOut 2s ease-in-out;
}

/* Bonus event notifications */
.bonus-event-notification {
    animation: slideInRight 0.3s ease-out;
}

.bonus-event-notification.closing {
    animation: slideOutRight 0.3s ease-in;
}

/* Level up notification */
.level-up-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    text-align: center;
    animation: levelUpAppear 0.5s ease-out;
    transition: opacity 0.5s ease;
}

@keyframes levelUpAppear {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.level-up-content h3 {
    margin: 0 0 10px 0;
    font-size: 1.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.level-up-rewards {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 10px;
    font-size: 1.2em;
    font-weight: bold;
}

/* Floating currency text */
.floating-currency-text {
    animation: floatUp 2s ease-out forwards;
}

@keyframes floatUp {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, calc(-50% - 100px)) scale(1.2);
    }
}

/* Task message */
.task-message {
    animation: taskMessageAppear 0.3s ease-out;
}

@keyframes taskMessageAppear {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Real-time building glow */
.building-glow {
    animation: buildingGlow 2s ease-in-out infinite;
}

@keyframes buildingGlow {
    0%, 100% {
        opacity: 0.2;
        transform: scale(1);
    }
    50% {
        opacity: 0.4;
        transform: scale(1.1);
    }
}

/* Character speech bubbles */
.speech-bubble {
    animation: speechBubbleAppear 0.3s ease-out;
}

@keyframes speechBubbleAppear {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Active bonus indicators */
.active-bonus {
    animation: bonusIndicatorPulse 2s ease-in-out infinite;
}

@keyframes bonusIndicatorPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 5px rgba(255, 107, 53, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 15px rgba(255, 107, 53, 0.6);
    }
}

/* Demo button styling */
.demo-button {
    background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%) !important;
    border: 2px solid #7B1FA2 !important;
    color: white !important;
    font-weight: bold;
    position: relative;
    overflow: hidden;
}

.demo-button:hover {
    background: linear-gradient(135deg, #AD42C4 0%, #7E57C2 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(156, 39, 176, 0.4);
}

.demo-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 3s infinite;
}

/* Storyline button styling */
.storyline-button {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%) !important;
    border: 2px solid #E55A2B !important;
    color: white !important;
    font-weight: bold;
    position: relative;
    overflow: hidden;
}

.storyline-button:hover {
    background: linear-gradient(135deg, #FF7A45 0%, #FA9F2E 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.storyline-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2.5s infinite;
}

/* Quest button styling */
.quest-button {
    background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%) !important;
    border: 2px solid #3E8E41 !important;
    color: white !important;
    font-weight: bold;
}

.quest-button:hover {
    background: linear-gradient(135deg, #5CBF60 0%, #4FB052 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

/* Building button unlock states */
.building-button.locked {
    opacity: 0.6;
    background: linear-gradient(135deg, #9E9E9E 0%, #757575 100%) !important;
    border-color: #616161 !important;
    cursor: not-allowed;
}

.building-button.locked:hover {
    transform: none;
    box-shadow: none;
}

.building-button.unlocked {
    opacity: 1;
    cursor: pointer;
}

.unlock-requirement {
    font-size: 0.7em;
    color: #F44336;
    font-weight: bold;
    margin-top: 3px;
    display: flex;
    align-items: center;
    gap: 3px;
}

/* Road category button styling */
.category-btn[data-category="roads"] {
    background: linear-gradient(135deg, #607D8B 0%, #546E7A 100%);
    border-color: #455A64;
    color: white;
}

.category-btn[data-category="roads"]:hover {
    background: linear-gradient(135deg, #708D9B 0%, #64788A 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(96, 125, 139, 0.3);
}

.category-btn[data-category="roads"].active {
    background: linear-gradient(135deg, #708D9B 0%, #64788A 100%);
    box-shadow: 0 0 15px rgba(96, 125, 139, 0.5);
}

/* Time mode controls */
.time-mode-toggle {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.time-mode-btn {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #ddd;
    background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
    color: #666;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: bold;
    transition: all 0.3s ease;
}

.time-mode-btn:hover {
    background: linear-gradient(135deg, #e8e8e8 0%, #d0d0d0 100%);
    transform: translateY(-1px);
}

.time-mode-btn.active {
    background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
    border-color: #3E8E41;
    color: white;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.time-mode-btn.active:hover {
    background: linear-gradient(135deg, #5CBF60 0%, #4FB052 100%);
}

.system-time-info {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-top: 10px;
}

.time-info, .period-info {
    margin: 4px 0;
    font-weight: bold;
}

.time-info {
    color: #2E7D32;
    font-size: 1.1em;
}

.period-info {
    color: #388E3C;
    font-size: 0.9em;
}

.time-controls {
    margin-top: 10px;
}

/* Real-time status panel styling */
.real-time-status-panel {
    font-family: 'Comic Sans MS', cursive, sans-serif;
}

.real-time-status-panel .status-header {
    background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: grab;
}

.real-time-status-panel .status-header h3 {
    margin: 0;
    font-size: 1em;
}

.real-time-status-panel .toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2em;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.real-time-status-panel .toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.real-time-status-panel .status-content {
    padding: 15px;
    max-height: 60vh;
    overflow-y: auto;
}

.real-time-status-panel .status-section {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #E0E0E0;
}

.real-time-status-panel .status-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.real-time-status-panel .status-section h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 0.9em;
    font-weight: bold;
}

.real-time-status-panel .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    font-size: 0.8em;
}

.real-time-status-panel .status-item span:first-child {
    color: #666;
}

.real-time-status-panel .status-item span:last-child {
    color: #333;
    font-weight: bold;
}

/* Shimmer animation for demo button */
@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Quest Panel Styling */
.quest-panel {
    font-family: 'Comic Sans MS', cursive, sans-serif;
}

.quest-header {
    background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px 12px 0 0;
}

.quest-header h3 {
    margin: 0;
    font-size: 1.2em;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5em;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.quest-tabs {
    display: flex;
    background: #f5f5f5;
    border-bottom: 2px solid #ddd;
}

.quest-tab {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    cursor: pointer;
    font-family: inherit;
    font-size: 0.9em;
    font-weight: bold;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.quest-tab:hover {
    background: #e8f5e8;
    color: #4CAF50;
}

.quest-tab.active {
    background: white;
    color: #4CAF50;
    border-bottom-color: #4CAF50;
}

.quest-content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 20px;
}

.quest-tab-content {
    display: none;
}

.quest-tab-content.active {
    display: block;
}

/* Quest Items */
.quest-item {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.quest-item:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

.quest-item.quest-active {
    border-color: #2196F3;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.05) 0%, rgba(33, 150, 243, 0.1) 100%);
}

.quest-item.quest-completed {
    border-color: #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.1) 100%);
}

.quest-item.quest-available {
    border-color: #FF9800;
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.05) 0%, rgba(255, 152, 0, 0.1) 100%);
    cursor: pointer;
}

.quest-item .quest-header {
    background: none;
    color: inherit;
    padding: 0;
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.quest-icon {
    font-size: 1.5em;
    flex-shrink: 0;
}

.quest-info {
    flex: 1;
}

.quest-name {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1em;
}

.quest-description {
    margin: 0;
    color: #666;
    font-size: 0.85em;
    line-height: 1.4;
}

.quest-chapter {
    background: #4CAF50;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7em;
    font-weight: bold;
    flex-shrink: 0;
}

/* Quest Objectives */
.quest-objectives {
    margin: 10px 0;
}

.objective {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.85em;
}

.objective:last-child {
    border-bottom: none;
}

.objective.complete {
    color: #4CAF50;
    text-decoration: line-through;
}

.objective-progress {
    font-weight: bold;
    color: #2196F3;
}

.objective.complete .objective-progress {
    color: #4CAF50;
}

/* Quest Rewards */
.quest-rewards {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    margin-top: 10px;
    font-size: 0.8em;
    font-weight: bold;
    color: #495057;
    text-align: center;
}

/* Story Progress */
.story-info {
    text-align: center;
}

.chapter-info {
    background: linear-gradient(135deg, #673AB7 0%, #9C27B0 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.chapter-info h4 {
    margin: 0 0 15px 0;
    font-size: 1.2em;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.progress-text {
    margin: 0;
    font-size: 0.9em;
    opacity: 0.9;
}

.story-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: left;
}

.story-summary h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.story-summary p {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

/* Unlock Items */
.unlock-categories {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.unlock-category h4 {
    margin: 0 0 15px 0;
    color: #333;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 5px;
}

.unlock-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.unlock-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    transition: all 0.3s ease;
}

.unlock-item.unlocked {
    border-color: #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.1) 100%);
}

.unlock-item.locked {
    opacity: 0.6;
}

.unlock-icon {
    font-size: 1.5em;
    flex-shrink: 0;
}

.unlock-info {
    flex: 1;
}

.unlock-info h5 {
    margin: 0 0 5px 0;
    font-size: 0.9em;
    color: #333;
}

.unlock-info .requirement,
.unlock-info .cost {
    margin: 0;
    font-size: 0.75em;
    color: #666;
}

.unlock-info .cost {
    color: #FF9800;
    font-weight: bold;
}

.unlock-status {
    font-size: 1.2em;
    flex-shrink: 0;
}

/* Feature Unlocks */
.unlock-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feature-unlock {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: white;
    transition: all 0.3s ease;
}

.feature-unlock.unlocked {
    border-color: #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.1) 100%);
}

.feature-unlock.locked {
    opacity: 0.6;
}

.feature-info h5 {
    margin: 0 0 5px 0;
    font-size: 0.9em;
    color: #333;
}

.feature-info .requirement {
    margin: 0;
    font-size: 0.75em;
    color: #666;
}

.feature-status {
    font-size: 1.2em;
}

/* Completed Quests */
.completed-stats {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-item span:first-child {
    font-size: 0.8em;
    color: #666;
}

.stat-item span:last-child {
    font-size: 1.2em;
    font-weight: bold;
    color: #4CAF50;
}

/* No quests message */
.no-quests {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-quests p {
    margin: 10px 0;
}

.no-quests .hint {
    font-size: 0.9em;
    font-style: italic;
    color: #999;
}
