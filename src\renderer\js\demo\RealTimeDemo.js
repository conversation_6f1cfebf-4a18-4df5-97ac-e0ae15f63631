/**
 * Springfield Town Builder - Real-Time Demo
 * Demonstrates all real-time features and capabilities
 */

class RealTimeDemo {
    constructor(game) {
        this.game = game;
        this.isRunning = false;
        this.demoSteps = [];
        this.currentStep = 0;
        this.stepInterval = 3000; // 3 seconds between steps
        
        this.setupDemoSteps();
    }
    
    setupDemoSteps() {
        this.demoSteps = [
            {
                name: 'Welcome',
                description: 'Welcome to the Real-Time Springfield Demo!',
                action: () => {
                    notificationSystem.special(
                        'Real-Time Demo Started!',
                        'Watch as Springfield comes alive with real-time features',
                        { duration: 4000 }
                    );
                }
            },
            {
                name: 'Auto Building Placement',
                description: 'Automatically placing buildings with real-time income',
                action: () => {
                    this.autoBuildBuildings();
                }
            },
            {
                name: 'Character Spawning',
                description: 'Spawning characters with AI behaviors',
                action: () => {
                    this.spawnDemoCharacters();
                }
            },
            {
                name: 'Real-Time Tasks',
                description: 'Generating and auto-assigning tasks',
                action: () => {
                    this.generateDemoTasks();
                }
            },

            {
                name: 'System Time Sync',
                description: 'Demonstrating real-time system clock integration',
                action: () => {
                    this.demonstrateSystemTime();
                }
            },
            {
                name: 'Day/Night Cycle',
                description: 'Accelerating time to show day/night transitions',
                action: () => {
                    this.demonstrateDayNight();
                }
            },
            {
                name: 'Income Generation',
                description: 'Showing real-time passive income',
                action: () => {
                    this.demonstrateIncome();
                }
            },
            {
                name: 'Level Progress',
                description: 'Demonstrating XP gain and level progression',
                action: () => {
                    this.demonstrateLevelProgress();
                }
            },
            {
                name: 'Music System',
                description: 'Showcasing dynamic music and audio controls',
                action: () => {
                    this.demonstrateMusicSystem();
                }
            },
            {
                name: 'Bonus Events',
                description: 'Triggering special bonus events',
                action: () => {
                    this.triggerBonusEvents();
                }
            },
            {
                name: 'Visual Effects',
                description: 'Showcasing post-processing effects',
                action: () => {
                    this.demonstrateVisualEffects();
                }
            },
            {
                name: 'Performance Stats',
                description: 'Displaying real-time performance metrics',
                action: () => {
                    this.showPerformanceStats();
                }
            },
            {
                name: 'Demo Complete',
                description: 'Real-time demo completed!',
                action: () => {
                    this.completeDemoDemo();
                }
            }
        ];
    }
    
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.currentStep = 0;
        
        console.log('Starting Real-Time Demo...');
        this.runNextStep();
    }
    
    stop() {
        this.isRunning = false;
        
        if (this.stepTimeout) {
            clearTimeout(this.stepTimeout);
        }
        
        notificationSystem.info('Demo Stopped', 'Real-time demo has been stopped');
    }
    
    runNextStep() {
        if (!this.isRunning || this.currentStep >= this.demoSteps.length) {
            this.stop();
            return;
        }
        
        const step = this.demoSteps[this.currentStep];
        
        console.log(`Demo Step ${this.currentStep + 1}: ${step.name}`);
        
        // Show step notification
        notificationSystem.info(
            `Demo Step ${this.currentStep + 1}`,
            step.description,
            { duration: this.stepInterval - 500 }
        );
        
        // Execute step action
        try {
            step.action();
        } catch (error) {
            console.error('Demo step error:', error);
        }
        
        this.currentStep++;
        
        // Schedule next step
        this.stepTimeout = setTimeout(() => {
            this.runNextStep();
        }, this.stepInterval);
    }
    
    autoBuildBuildings() {
        const buildingTypes = ['simpsons_house', 'kwik_e_mart', 'moes_tavern', 'power_plant'];
        const positions = [
            { x: 5, z: 5 },
            { x: -5, z: 5 },
            { x: 5, z: -5 },
            { x: -5, z: -5 }
        ];
        
        buildingTypes.forEach((type, index) => {
            setTimeout(() => {
                if (this.game.buildingSystem && this.game.buildingSystem.buildingData[type]) {
                    const position = new THREE.Vector3(positions[index].x, 0, positions[index].z);
                    this.game.buildingSystem.placeBuilding(type, position);
                }
            }, index * 1000);
        });
    }
    
    spawnDemoCharacters() {
        const characterTypes = ['homer', 'marge', 'bart', 'lisa'];
        const positions = [
            { x: 3, z: 3 },
            { x: -3, z: 3 },
            { x: 3, z: -3 },
            { x: -3, z: -3 }
        ];
        
        characterTypes.forEach((type, index) => {
            setTimeout(() => {
                if (this.game.characterSystem && this.game.characterSystem.characterData[type]) {
                    const position = new THREE.Vector3(positions[index].x, 0, positions[index].z);
                    this.game.characterSystem.createCharacter(type, position);
                }
            }, index * 500);
        });
    }
    
    generateDemoTasks() {
        if (this.game.taskSystem) {
            // Generate several tasks
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    this.game.taskSystem.generateRandomTask();
                }, i * 300);
            }
        }
    }
    

    
    demonstrateSystemTime() {
        if (this.game.engine3D && this.game.engine3D.dayNightCycle) {
            // Switch to system time mode
            this.game.engine3D.setUseSystemTime(true);

            // Show current system time info
            const timeInfo = this.game.engine3D.getDetailedTimeInfo();

            if (window.notificationSystem) {
                notificationSystem.special(
                    'System Time Active!',
                    `Current time: ${timeInfo.timeString} - ${timeInfo.period} period`,
                    {
                        duration: 4000,
                        actions: [
                            {
                                text: 'View Effects Panel',
                                callback: () => {
                                    if (this.game.uiManager?.effectsPanel) {
                                        this.game.uiManager.effectsPanel.show();
                                    }
                                }
                            }
                        ]
                    }
                );
            }
        }
    }

    demonstrateDayNight() {
        if (this.game.engine3D && this.game.engine3D.dayNightCycle) {
            // Switch to game time mode for demo
            this.game.engine3D.setUseSystemTime(false);

            // Temporarily speed up time
            const originalSpeed = this.game.engine3D.dayNightCycle.timeSpeed;
            this.game.engine3D.dayNightCycle.timeSpeed = 50; // 50x speed

            // Reset after demo
            setTimeout(() => {
                this.game.engine3D.dayNightCycle.timeSpeed = originalSpeed;
                // Switch back to system time
                this.game.engine3D.setUseSystemTime(true);
            }, this.stepInterval - 500);
        }
    }
    
    demonstrateIncome() {
        // Temporarily boost income generation
        if (this.game.currencySystem) {
            const originalMultiplier = this.game.currencySystem.incomeMultiplier;
            this.game.currencySystem.incomeMultiplier = 5; // 5x income

            notificationSystem.special(
                'Income Boost!',
                '5x income multiplier activated for demo',
                { duration: 2000 }
            );

            // Reset after demo
            setTimeout(() => {
                this.game.currencySystem.incomeMultiplier = originalMultiplier;
            }, this.stepInterval - 500);
        }
    }

    demonstrateLevelProgress() {
        if (this.game.currencySystem) {
            // Add XP to demonstrate level progress
            const xpToAdd = 150; // Enough to show progress and potentially level up
            this.game.currencySystem.addXP(xpToAdd, 'demo_activity');

            notificationSystem.info(
                'XP Gained!',
                `+${xpToAdd} XP from completing activities`,
                {
                    duration: 3000,
                    actions: [
                        {
                            text: 'Check Progress',
                            callback: () => {
                                const level = this.game.currencySystem.getLevel();
                                const xp = this.game.currencySystem.getXP();
                                const nextLevelXP = this.game.currencySystem.getXPForNextLevel();

                                notificationSystem.info(
                                    'Level Progress',
                                    `Level ${level} - ${xp}/${nextLevelXP} XP`,
                                    { duration: 2000 }
                                );
                            }
                        }
                    ]
                }
            );
        }
    }

    demonstrateMusicSystem() {
        if (this.game.musicSystem) {
            // Start playing music
            const tracks = this.game.musicSystem.getAvailableTracks();
            if (tracks.length > 0) {
                this.game.musicSystem.playTrack(tracks[0].id);

                notificationSystem.special(
                    'Music System Active!',
                    `Now playing: ${tracks[0].name} - Check the Effects Panel for music controls`,
                    {
                        duration: 5000,
                        actions: [
                            {
                                text: 'Open Effects Panel',
                                callback: () => {
                                    if (this.game.uiManager?.effectsPanel) {
                                        this.game.uiManager.effectsPanel.show();
                                    }
                                }
                            },
                            {
                                text: 'Next Track',
                                callback: () => {
                                    this.game.musicSystem.nextTrack();
                                }
                            }
                        ]
                    }
                );
            } else {
                notificationSystem.info(
                    'Music System Ready',
                    'Music system is initialized but no tracks are available. Add music files to assets/sound/music/',
                    { duration: 4000 }
                );
            }
        } else {
            notificationSystem.warning(
                'Music System Unavailable',
                'Music system is not initialized',
                { duration: 3000 }
            );
        }
    }
    
    triggerBonusEvents() {
        if (this.game.currencySystem) {
            // Trigger multiple bonus events
            const bonuses = [
                { type: 'free_money', amount: 1000, name: 'Demo Money Bonus!' },
                { type: 'free_donuts', amount: 10, name: 'Demo Donut Bonus!' },
                { type: 'income_boost', multiplier: 3.0, duration: 5000, name: 'Demo Income Boost!' }
            ];
            
            bonuses.forEach((bonus, index) => {
                setTimeout(() => {
                    this.game.currencySystem.activateBonusEvent(bonus);
                }, index * 800);
            });
        }
    }
    
    demonstrateVisualEffects() {
        if (this.game.engine3D && this.game.engine3D.postProcessing) {
            // Cycle through different visual presets
            const presets = ['default', 'cinematic', 'vibrant', 'moody'];
            let presetIndex = 0;
            
            const changePreset = () => {
                const preset = presets[presetIndex % presets.length];
                this.game.engine3D.postProcessing.applyPreset(preset);
                
                notificationSystem.info(
                    'Visual Preset',
                    `Applied ${preset} visual preset`,
                    { duration: 1000 }
                );
                
                presetIndex++;
                
                if (presetIndex < presets.length) {
                    setTimeout(changePreset, 800);
                }
            };
            
            changePreset();
        }
    }
    
    showPerformanceStats() {
        // Show real-time status panel
        if (this.game.uiManager && this.game.uiManager.realTimeStatus) {
            this.game.uiManager.realTimeStatus.show();
            
            notificationSystem.info(
                'Performance Stats',
                'Real-time performance statistics are now visible',
                { duration: 2000 }
            );
        }
    }
    
    completeDemoDemo() {
        notificationSystem.special(
            'Demo Complete!',
            'All real-time features have been demonstrated. The game continues to run in real-time!',
            {
                duration: 5000,
                actions: [
                    {
                        text: 'Restart Demo',
                        callback: () => this.start()
                    }
                ]
            }
        );
        
        // Reset any demo modifications
        this.resetDemoState();
    }
    
    resetDemoState() {
        // Reset any temporary changes made during demo
        if (this.game.engine3D) {
            if (this.game.engine3D.dayNightCycle) {
                this.game.engine3D.dayNightCycle.timeSpeed = 1;
            }

            if (this.game.engine3D.postProcessing) {
                this.game.engine3D.postProcessing.applyPreset('default');
            }
        }

        if (this.game.currencySystem) {
            this.game.currencySystem.incomeMultiplier = 1.0;
        }
    }
    
    // Quick demo for testing
    quickDemo() {
        this.stepInterval = 1500; // Faster demo
        this.start();
    }
    
    // Skip to specific demo step
    skipToStep(stepIndex) {
        if (stepIndex >= 0 && stepIndex < this.demoSteps.length) {
            this.currentStep = stepIndex;
            this.runNextStep();
        }
    }
}

// Make demo available globally for console access
window.RealTimeDemo = RealTimeDemo;
