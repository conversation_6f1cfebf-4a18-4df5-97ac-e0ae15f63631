/**
 * Springfield Town Builder - Post Processing Effects
 * Handles visual effects like bloom, depth of field, and color grading
 */

class PostProcessing {
    constructor(renderer, scene, camera) {
        this.renderer = renderer;
        this.scene = scene;
        this.camera = camera;
        
        // Effect settings
        this.enabled = true;
        this.effects = {
            bloom: true,
            vignette: true,
            colorGrading: true,
            filmGrain: false,
            chromaticAberration: false
        };
        
        // Render targets
        this.renderTarget = null;
        this.bloomTarget = null;
        
        // Materials for effects
        this.materials = {};
        
        this.init();
    }
    
    init() {
        // Create render targets
        const size = this.renderer.getSize(new THREE.Vector2());
        
        this.renderTarget = new THREE.WebGLRenderTarget(size.x, size.y, {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
        });
        
        this.bloomTarget = new THREE.WebGLRenderTarget(size.x / 2, size.y / 2, {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
        });
        
        // Create effect materials
        this.createEffectMaterials();
        
        // Create full-screen quad
        this.quad = new THREE.Mesh(
            new THREE.PlaneGeometry(2, 2),
            this.materials.composite
        );
        this.quad.frustumCulled = false;
        
        // Create post-processing scene
        this.postScene = new THREE.Scene();
        this.postScene.add(this.quad);
        
        // Create orthographic camera for post-processing
        this.postCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
    }
    
    createEffectMaterials() {
        // Bloom effect material
        this.materials.bloom = new THREE.ShaderMaterial({
            uniforms: {
                tDiffuse: { value: null },
                bloomStrength: { value: 0.8 },
                bloomRadius: { value: 0.4 },
                bloomThreshold: { value: 0.85 }
            },
            vertexShader: `
                varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform sampler2D tDiffuse;
                uniform float bloomStrength;
                uniform float bloomRadius;
                uniform float bloomThreshold;
                varying vec2 vUv;
                
                void main() {
                    vec4 color = texture2D(tDiffuse, vUv);
                    
                    // Calculate luminance
                    float luminance = dot(color.rgb, vec3(0.299, 0.587, 0.114));
                    
                    // Apply bloom threshold
                    float bloom = smoothstep(bloomThreshold - 0.1, bloomThreshold + 0.1, luminance);
                    
                    // Simple bloom effect
                    vec3 bloomColor = color.rgb * bloom * bloomStrength;
                    
                    gl_FragColor = vec4(color.rgb + bloomColor, color.a);
                }
            `
        });
        
        // Composite material with multiple effects
        this.materials.composite = new THREE.ShaderMaterial({
            uniforms: {
                tDiffuse: { value: null },
                tBloom: { value: null },
                time: { value: 0 },
                vignetteStrength: { value: 0.3 },
                vignetteRadius: { value: 0.8 },
                saturation: { value: 1.1 },
                contrast: { value: 1.05 },
                brightness: { value: 1.02 },
                filmGrainStrength: { value: 0.1 },
                chromaticAberration: { value: 0.002 }
            },
            vertexShader: `
                varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform sampler2D tDiffuse;
                uniform sampler2D tBloom;
                uniform float time;
                uniform float vignetteStrength;
                uniform float vignetteRadius;
                uniform float saturation;
                uniform float contrast;
                uniform float brightness;
                uniform float filmGrainStrength;
                uniform float chromaticAberration;
                varying vec2 vUv;
                
                // Random function for film grain
                float random(vec2 st) {
                    return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
                }
                
                void main() {
                    vec2 uv = vUv;
                    
                    // Chromatic aberration
                    vec2 aberration = vec2(chromaticAberration);
                    vec3 color;
                    color.r = texture2D(tDiffuse, uv + aberration).r;
                    color.g = texture2D(tDiffuse, uv).g;
                    color.b = texture2D(tDiffuse, uv - aberration).b;
                    
                    // Add bloom
                    vec3 bloom = texture2D(tBloom, uv).rgb;
                    color += bloom;
                    
                    // Color grading
                    color = mix(vec3(dot(color, vec3(0.299, 0.587, 0.114))), color, saturation);
                    color = (color - 0.5) * contrast + 0.5;
                    color *= brightness;
                    
                    // Vignette effect
                    vec2 center = uv - 0.5;
                    float vignette = 1.0 - smoothstep(vignetteRadius, vignetteRadius + 0.3, length(center));
                    color *= mix(1.0 - vignetteStrength, 1.0, vignette);
                    
                    // Film grain
                    float grain = random(uv + time) * filmGrainStrength;
                    color += grain;
                    
                    gl_FragColor = vec4(color, 1.0);
                }
            `
        });
        
        // Copy material for simple passes
        this.materials.copy = new THREE.ShaderMaterial({
            uniforms: {
                tDiffuse: { value: null }
            },
            vertexShader: `
                varying vec2 vUv;
                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform sampler2D tDiffuse;
                varying vec2 vUv;
                void main() {
                    gl_FragColor = texture2D(tDiffuse, vUv);
                }
            `
        });
    }
    
    render() {
        if (!this.enabled) {
            this.renderer.render(this.scene, this.camera);
            return;
        }
        
        // Render scene to texture
        this.renderer.setRenderTarget(this.renderTarget);
        this.renderer.render(this.scene, this.camera);
        
        // Create bloom pass if enabled
        if (this.effects.bloom) {
            this.renderBloom();
        }
        
        // Composite final image
        this.renderComposite();
    }
    
    renderBloom() {
        // Render bloom to smaller target
        this.renderer.setRenderTarget(this.bloomTarget);
        this.materials.bloom.uniforms.tDiffuse.value = this.renderTarget.texture;
        this.quad.material = this.materials.bloom;
        this.renderer.render(this.postScene, this.postCamera);
    }
    
    renderComposite() {
        // Render final composite to screen
        this.renderer.setRenderTarget(null);
        this.materials.composite.uniforms.tDiffuse.value = this.renderTarget.texture;
        this.materials.composite.uniforms.tBloom.value = this.bloomTarget.texture;
        this.materials.composite.uniforms.time.value = Date.now() * 0.001;
        this.quad.material = this.materials.composite;
        this.renderer.render(this.postScene, this.postCamera);
    }
    
    setSize(width, height) {
        this.renderTarget.setSize(width, height);
        this.bloomTarget.setSize(width / 2, height / 2);
    }
    
    // Effect control methods
    setBloomStrength(strength) {
        this.materials.bloom.uniforms.bloomStrength.value = strength;
    }
    
    setVignetteStrength(strength) {
        this.materials.composite.uniforms.vignetteStrength.value = strength;
    }
    
    setSaturation(saturation) {
        this.materials.composite.uniforms.saturation.value = saturation;
    }
    
    setContrast(contrast) {
        this.materials.composite.uniforms.contrast.value = contrast;
    }
    
    setBrightness(brightness) {
        this.materials.composite.uniforms.brightness.value = brightness;
    }
    
    enableEffect(effectName) {
        this.effects[effectName] = true;
    }
    
    disableEffect(effectName) {
        this.effects[effectName] = false;
    }
    
    dispose() {
        this.renderTarget.dispose();
        this.bloomTarget.dispose();
        
        Object.values(this.materials).forEach(material => {
            material.dispose();
        });
    }
}
