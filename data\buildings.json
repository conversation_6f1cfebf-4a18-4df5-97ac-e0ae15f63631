{"buildings": {"simpson_house": {"id": "simpson_house", "name": "Simpson House", "description": "The iconic home of the <PERSON> family at 742 Evergreen Terrace", "category": "residential", "image": "assets/buildings/simpson_house.png", "model": "assets/models/buildings/simpson_house.glb", "size": {"width": 4, "height": 3, "depth": 4}, "unlockLevel": 1, "cost": {"type": "free"}, "buildTime": 0, "income": {"amount": 100, "interval": 3600}, "xpReward": 50, "maxLevel": 5, "upgrades": [{"level": 2, "cost": {"type": "money", "amount": 5000}, "income": {"amount": 150, "interval": 3600}, "description": "Add a garage and improve the yard"}, {"level": 3, "cost": {"type": "money", "amount": 10000}, "income": {"amount": 200, "interval": 3600}, "description": "Renovate the kitchen and living room"}], "characters": ["homer", "marge", "bart", "lisa", "maggie"]}, "kwik_e_mart": {"id": "kwik_e_mart", "name": "Kwik-E-Mart", "description": "Springfield's premier convenience store, managed by Apu", "category": "commercial", "image": "assets/buildings/kwik_e_mart.png", "model": "assets/models/buildings/kwik_e_mart.glb", "size": {"width": 3, "height": 2, "depth": 3}, "unlockLevel": 2, "cost": {"type": "money", "amount": 2500}, "buildTime": 300, "income": {"amount": 75, "interval": 1800}, "xpReward": 100, "maxLevel": 5, "upgrades": [{"level": 2, "cost": {"type": "money", "amount": 7500}, "income": {"amount": 100, "interval": 1800}, "description": "Add a Squishee machine and expand inventory"}], "characters": ["apu"]}, "moes_tavern": {"id": "moes_tavern", "name": "Moe's Tavern", "description": "Springfield's favorite dive bar, where everybody knows your shame", "category": "commercial", "image": "assets/buildings/moes_tavern.png", "model": "assets/models/buildings/moes_tavern.glb", "size": {"width": 3, "height": 2, "depth": 2}, "unlockLevel": 3, "cost": {"type": "money", "amount": 3500}, "buildTime": 600, "income": {"amount": 90, "interval": 2400}, "xpReward": 150, "maxLevel": 4, "upgrades": [{"level": 2, "cost": {"type": "money", "amount": 8000}, "income": {"amount": 120, "interval": 2400}, "description": "Add a pool table and jukebox"}], "characters": ["moe", "homer", "<PERSON>ey"]}, "springfield_elementary": {"id": "springfield_elementary", "name": "Springfield Elementary School", "description": "Where young minds go to... well, it's complicated", "category": "community", "image": "assets/buildings/springfield_elementary.png", "model": "assets/models/buildings/springfield_elementary.glb", "size": {"width": 5, "height": 3, "depth": 4}, "unlockLevel": 5, "cost": {"type": "money", "amount": 8000}, "buildTime": 1200, "income": {"amount": 120, "interval": 4800}, "xpReward": 250, "maxLevel": 3, "upgrades": [{"level": 2, "cost": {"type": "money", "amount": 15000}, "income": {"amount": 160, "interval": 4800}, "description": "Add a gymnasium and cafeteria"}], "characters": ["bart", "lisa", "principal_skinner", "mrs_k<PERSON><PERSON><PERSON>"]}, "nuclear_power_plant": {"id": "nuclear_power_plant", "name": "Springfield Nuclear Power Plant", "description": "Mr. <PERSON>' nuclear facility, surprisingly still operational", "category": "industrial", "image": "assets/buildings/nuclear_power_plant.png", "model": "assets/models/buildings/nuclear_power_plant.glb", "size": {"width": 6, "height": 4, "depth": 5}, "unlockLevel": 8, "cost": {"type": "money", "amount": 25000}, "buildTime": 2400, "income": {"amount": 300, "interval": 7200}, "xpReward": 500, "maxLevel": 5, "upgrades": [{"level": 2, "cost": {"type": "money", "amount": 50000}, "income": {"amount": 400, "interval": 7200}, "description": "Add cooling towers and safety equipment"}], "characters": ["homer", "mr_burns", "smithers", "lenny", "carl"]}, "krusty_burger": {"id": "krusty_burger", "name": "<PERSON><PERSON><PERSON>", "description": "Fast food that's... well, it's fast", "category": "commercial", "image": "assets/buildings/krusty_burger.png", "model": "assets/models/buildings/krusty_burger.glb", "size": {"width": 3, "height": 2, "depth": 3}, "unlockLevel": 6, "cost": {"type": "money", "amount": 5500}, "buildTime": 900, "income": {"amount": 110, "interval": 3000}, "xpReward": 200, "maxLevel": 4, "upgrades": [{"level": 2, "cost": {"type": "money", "amount": 12000}, "income": {"amount": 140, "interval": 3000}, "description": "Add a drive-thru and playground"}], "characters": ["krusty", "teenage_employee"]}, "androids_dungeon": {"id": "androids_dungeon", "name": "The Android's Dungeon", "description": "Comic Book Guy's temple to all things nerdy", "category": "commercial", "image": "assets/buildings/androids_dungeon.png", "model": "assets/models/buildings/androids_dungeon.glb", "size": {"width": 2, "height": 2, "depth": 3}, "unlockLevel": 7, "cost": {"type": "donuts", "amount": 40}, "buildTime": 1800, "income": {"amount": 85, "interval": 3600}, "xpReward": 300, "maxLevel": 3, "upgrades": [{"level": 2, "cost": {"type": "donuts", "amount": 20}, "income": {"amount": 110, "interval": 3600}, "description": "Add rare collectibles section"}], "characters": ["comic_book_guy"]}, "town_hall": {"id": "town_hall", "name": "Springfield Town Hall", "description": "The seat of Springfield's questionable government", "category": "community", "image": "assets/buildings/town_hall.png", "model": "assets/models/buildings/town_hall.glb", "size": {"width": 4, "height": 3, "depth": 4}, "unlockLevel": 10, "cost": {"type": "money", "amount": 15000}, "buildTime": 1800, "income": {"amount": 200, "interval": 6000}, "xpReward": 400, "maxLevel": 3, "upgrades": [{"level": 2, "cost": {"type": "money", "amount": 30000}, "income": {"amount": 250, "interval": 6000}, "description": "Add a statue and improve the facade"}], "characters": ["mayor_quimby", "marge"]}, "springfield_library": {"id": "springfield_library", "name": "Springfield Public Library", "description": "A quiet place for learning and reading", "category": "community", "image": "assets/buildings/springfield_library.png", "model": "assets/models/buildings/springfield_library.glb", "size": {"width": 3, "height": 2, "depth": 4}, "unlockLevel": 9, "cost": {"type": "money", "amount": 12000}, "buildTime": 1500, "income": {"amount": 80, "interval": 4800}, "xpReward": 350, "maxLevel": 3, "upgrades": [{"level": 2, "cost": {"type": "money", "amount": 20000}, "income": {"amount": 100, "interval": 4800}, "description": "Add computer section and study rooms"}], "characters": ["lisa", "martin", "database"]}, "flanders_house": {"id": "flanders_house", "name": "Flanders House", "description": "The perfectly maintained home of Ned Flanders", "category": "residential", "image": "assets/buildings/flanders_house.png", "model": "assets/models/buildings/flanders_house.glb", "size": {"width": 3, "height": 3, "depth": 3}, "unlockLevel": 4, "cost": {"type": "donuts", "amount": 35}, "buildTime": 600, "income": {"amount": 120, "interval": 4200}, "xpReward": 180, "maxLevel": 4, "upgrades": [{"level": 2, "cost": {"type": "donuts", "amount": 15}, "income": {"amount": 150, "interval": 4200}, "description": "Add a perfect garden and white picket fence"}], "characters": ["ned_flanders", "rod_flanders", "todd_flanders"]}, "road_straight": {"id": "road_straight", "name": "Straight Road", "description": "A straight section of road for connecting buildings", "category": "roads", "image": "assets/roads/road_straight.png", "model": "assets/models/roads/road_straight.glb", "size": {"width": 2, "height": 0.1, "depth": 2}, "unlockLevel": 1, "cost": {"type": "money", "amount": 25}, "buildTime": 0, "income": null, "xpReward": 2, "maxLevel": 1, "icon": "🛣️"}, "road_corner": {"id": "road_corner", "name": "Corner Road", "description": "A corner section of road for turns", "category": "roads", "image": "assets/roads/road_corner.png", "model": "assets/models/roads/road_corner.glb", "size": {"width": 2, "height": 0.1, "depth": 2}, "unlockLevel": 1, "cost": {"type": "money", "amount": 25}, "buildTime": 0, "income": null, "xpReward": 2, "maxLevel": 1, "icon": "🔄"}, "road_intersection": {"id": "road_intersection", "name": "Road Intersection", "description": "A four-way intersection for complex road networks", "category": "roads", "image": "assets/roads/road_intersection.png", "model": "assets/models/roads/road_intersection.glb", "size": {"width": 2, "height": 0.1, "depth": 2}, "unlockLevel": 2, "cost": {"type": "money", "amount": 50}, "buildTime": 0, "income": null, "xpReward": 5, "maxLevel": 1, "icon": "✚"}, "road_t_junction": {"id": "road_t_junction", "name": "T-Junction", "description": "A T-shaped road junction", "category": "roads", "image": "assets/roads/road_t_junction.png", "model": "assets/models/roads/road_t_junction.glb", "size": {"width": 2, "height": 0.1, "depth": 2}, "unlockLevel": 1, "cost": {"type": "money", "amount": 35}, "buildTime": 0, "income": null, "xpReward": 3, "maxLevel": 1, "icon": "⊥"}}}