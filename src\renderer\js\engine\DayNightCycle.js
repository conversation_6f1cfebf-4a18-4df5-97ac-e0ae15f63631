/**
 * Springfield Town Builder - Day/Night Cycle System
 * Handles dynamic lighting changes throughout the day
 */

class DayNightCycle extends EventEmitter {
    constructor(engine3D) {
        super();
        this.engine3D = engine3D;
        this.scene = engine3D.getScene();
        this.lights = engine3D.lights;
        
        // Time settings
        this.timeOfDay = 12; // Start at noon (0-24 hours)
        this.dayDuration = 300000; // 5 minutes real time = 24 hours game time
        this.timeSpeed = 1; // Speed multiplier
        this.isRunning = false;
        
        // Lighting configurations for different times
        this.lightingPresets = {
            dawn: {
                sunColor: 0xffa500,
                sunIntensity: 0.4,
                ambientColor: 0x404040,
                ambientIntensity: 0.2,
                hemisphereColor: 0xffa500,
                hemisphereIntensity: 0.3,
                fogColor: 0xffa500,
                skyColor: 0xff6b35
            },
            day: {
                sunColor: 0xffffff,
                sunIntensity: 0.8,
                ambientColor: 0x404040,
                ambientIntensity: 0.4,
                hemisphereColor: 0x87ceeb,
                hemisphereIntensity: 0.3,
                fogColor: 0x87ceeb,
                skyColor: 0x87ceeb
            },
            dusk: {
                sunColor: 0xff4500,
                sunIntensity: 0.3,
                ambientColor: 0x202020,
                ambientIntensity: 0.15,
                hemisphereColor: 0xff4500,
                hemisphereIntensity: 0.2,
                fogColor: 0xff4500,
                skyColor: 0x8b0000
            },
            night: {
                sunColor: 0x4169e1,
                sunIntensity: 0.1,
                ambientColor: 0x101010,
                ambientIntensity: 0.1,
                hemisphereColor: 0x000080,
                hemisphereIntensity: 0.1,
                fogColor: 0x191970,
                skyColor: 0x000080
            }
        };
        
        // Street lights for night time
        this.streetLights = [];
        this.streetLightIntensity = 0;
        
        this.init();
    }
    
    init() {
        // Create street light system
        this.createStreetLights();
        
        // Set initial lighting
        this.updateLighting();
    }
    
    createStreetLights() {
        // Find existing street lamps in the scene
        this.scene.traverse((object) => {
            if (object.userData && object.userData.isStreetLamp) {
                this.addStreetLight(object);
            }
        });
    }
    
    addStreetLight(lampObject) {
        // Create point light for street lamp
        const light = new THREE.PointLight(0xffa500, 0, 8);
        light.position.copy(lampObject.position);
        light.position.y += 4; // Position at lamp head
        light.castShadow = true;
        light.shadow.mapSize.width = 512;
        light.shadow.mapSize.height = 512;
        
        this.scene.add(light);
        this.streetLights.push({
            light: light,
            lamp: lampObject,
            originalIntensity: 1.5
        });
    }
    
    start() {
        this.isRunning = true;
        this.lastUpdateTime = Date.now();
        this.update();
    }
    
    stop() {
        this.isRunning = false;
    }
    
    update() {
        if (!this.isRunning) return;
        
        const currentTime = Date.now();
        const deltaTime = currentTime - this.lastUpdateTime;
        this.lastUpdateTime = currentTime;
        
        // Update time of day
        const timeIncrement = (deltaTime / this.dayDuration) * 24 * this.timeSpeed;
        this.timeOfDay = (this.timeOfDay + timeIncrement) % 24;
        
        // Update lighting
        this.updateLighting();
        
        // Update street lights
        this.updateStreetLights();
        
        // Emit time change event
        this.emit('timeChanged', {
            timeOfDay: this.timeOfDay,
            period: this.getCurrentPeriod()
        });
        
        // Continue update loop
        requestAnimationFrame(() => this.update());
    }
    
    getCurrentPeriod() {
        if (this.timeOfDay >= 5 && this.timeOfDay < 7) return 'dawn';
        if (this.timeOfDay >= 7 && this.timeOfDay < 18) return 'day';
        if (this.timeOfDay >= 18 && this.timeOfDay < 20) return 'dusk';
        return 'night';
    }
    
    updateLighting() {
        const period = this.getCurrentPeriod();
        const preset = this.lightingPresets[period];
        
        // Calculate transition factor for smooth changes
        let transitionFactor = 1;
        let nextPreset = null;
        
        // Determine if we're in a transition period
        if (period === 'dawn' && this.timeOfDay >= 6.5) {
            transitionFactor = (this.timeOfDay - 6.5) / 0.5;
            nextPreset = this.lightingPresets.day;
        } else if (period === 'day' && this.timeOfDay >= 17.5) {
            transitionFactor = (this.timeOfDay - 17.5) / 0.5;
            nextPreset = this.lightingPresets.dusk;
        } else if (period === 'dusk' && this.timeOfDay >= 19.5) {
            transitionFactor = (this.timeOfDay - 19.5) / 0.5;
            nextPreset = this.lightingPresets.night;
        } else if (period === 'night' && this.timeOfDay >= 4.5 && this.timeOfDay < 5) {
            transitionFactor = (this.timeOfDay - 4.5) / 0.5;
            nextPreset = this.lightingPresets.dawn;
        }
        
        // Apply lighting with smooth transitions
        if (nextPreset && transitionFactor > 0) {
            this.applyLightingTransition(preset, nextPreset, transitionFactor);
        } else {
            this.applyLighting(preset);
        }
    }
    
    applyLighting(preset) {
        if (!this.lights) return;
        
        // Update sun light
        this.lights.sun.color.setHex(preset.sunColor);
        this.lights.sun.intensity = preset.sunIntensity;
        
        // Update ambient light
        this.lights.ambient.color.setHex(preset.ambientColor);
        this.lights.ambient.intensity = preset.ambientIntensity;
        
        // Update hemisphere light
        this.lights.hemisphere.color.setHex(preset.hemisphereColor);
        this.lights.hemisphere.intensity = preset.hemisphereIntensity;
        
        // Update scene background and fog
        this.scene.background = new THREE.Color(preset.skyColor);
        if (this.scene.fog) {
            this.scene.fog.color.setHex(preset.fogColor);
        }
        
        // Update sun position based on time
        this.updateSunPosition();
    }
    
    applyLightingTransition(preset1, preset2, factor) {
        if (!this.lights) return;
        
        // Interpolate between two presets
        const sunColor = new THREE.Color(preset1.sunColor).lerp(new THREE.Color(preset2.sunColor), factor);
        const ambientColor = new THREE.Color(preset1.ambientColor).lerp(new THREE.Color(preset2.ambientColor), factor);
        const hemisphereColor = new THREE.Color(preset1.hemisphereColor).lerp(new THREE.Color(preset2.hemisphereColor), factor);
        const skyColor = new THREE.Color(preset1.skyColor).lerp(new THREE.Color(preset2.skyColor), factor);
        const fogColor = new THREE.Color(preset1.fogColor).lerp(new THREE.Color(preset2.fogColor), factor);
        
        const sunIntensity = THREE.MathUtils.lerp(preset1.sunIntensity, preset2.sunIntensity, factor);
        const ambientIntensity = THREE.MathUtils.lerp(preset1.ambientIntensity, preset2.ambientIntensity, factor);
        const hemisphereIntensity = THREE.MathUtils.lerp(preset1.hemisphereIntensity, preset2.hemisphereIntensity, factor);
        
        // Apply interpolated values
        this.lights.sun.color.copy(sunColor);
        this.lights.sun.intensity = sunIntensity;
        this.lights.ambient.color.copy(ambientColor);
        this.lights.ambient.intensity = ambientIntensity;
        this.lights.hemisphere.color.copy(hemisphereColor);
        this.lights.hemisphere.intensity = hemisphereIntensity;
        
        this.scene.background = skyColor;
        if (this.scene.fog) {
            this.scene.fog.color.copy(fogColor);
        }
        
        this.updateSunPosition();
    }
    
    updateSunPosition() {
        if (!this.lights.sun) return;
        
        // Calculate sun position based on time of day
        const sunAngle = (this.timeOfDay / 24) * Math.PI * 2 - Math.PI / 2; // Start at east
        const sunHeight = Math.sin((this.timeOfDay - 6) / 12 * Math.PI); // Peak at noon
        
        const sunDistance = 100;
        this.lights.sun.position.set(
            Math.cos(sunAngle) * sunDistance,
            Math.max(sunHeight * sunDistance, 10), // Minimum height
            Math.sin(sunAngle) * sunDistance
        );
    }
    
    updateStreetLights() {
        // Street lights should be brighter at night
        const period = this.getCurrentPeriod();
        let targetIntensity = 0;
        
        if (period === 'dusk') {
            targetIntensity = (this.timeOfDay - 18) / 2; // Fade in during dusk
        } else if (period === 'night') {
            targetIntensity = 1;
        } else if (period === 'dawn') {
            targetIntensity = 1 - (this.timeOfDay - 5) / 2; // Fade out during dawn
        }
        
        this.streetLights.forEach(streetLight => {
            streetLight.light.intensity = streetLight.originalIntensity * targetIntensity;
        });
    }
    
    // Public methods for time control
    setTimeOfDay(hours) {
        this.timeOfDay = Math.max(0, Math.min(24, hours));
        this.updateLighting();
        this.updateStreetLights();
    }
    
    setTimeSpeed(speed) {
        this.timeSpeed = Math.max(0, speed);
    }
    
    skipToTime(hours) {
        const targetTime = Math.max(0, Math.min(24, hours));
        const currentPeriod = this.getCurrentPeriod();
        
        this.setTimeOfDay(targetTime);
        
        const newPeriod = this.getCurrentPeriod();
        if (currentPeriod !== newPeriod) {
            this.emit('periodChanged', {
                from: currentPeriod,
                to: newPeriod,
                timeOfDay: this.timeOfDay
            });
        }
    }
    
    getTimeString() {
        const hours = Math.floor(this.timeOfDay);
        const minutes = Math.floor((this.timeOfDay - hours) * 60);
        const ampm = hours >= 12 ? 'PM' : 'AM';
        const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
        
        return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
    }
    

    
    dispose() {
        this.stop();
        
        // Remove street lights
        this.streetLights.forEach(streetLight => {
            this.scene.remove(streetLight.light);
        });
        
        this.removeAllListeners();
    }
}
