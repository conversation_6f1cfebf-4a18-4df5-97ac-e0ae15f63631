/**
 * Springfield Town Builder - Main Game Class
 * Coordinates all game systems and manages game state
 */

class Game extends EventEmitter {
    constructor() {
        super();
        
        // Game state
        this.isInitialized = false;
        this.isRunning = false;
        this.isPaused = false;
        this.gameState = null;
        
        // Core systems
        this.engine3D = null;
        this.cameraController = null;
        this.saveSystem = null;
        this.currencySystem = null;
        this.buildingSystem = null;
        this.characterSystem = null;
        this.taskSystem = null;
        this.multiplayerSystem = null;
        
        // UI managers
        this.uiManager = null;
        this.menuSystem = null;
        
        // Game settings
        this.settings = {
            soundEnabled: true,
            musicEnabled: true,
            notifications: true,
            autoSave: true
        };
        
        this.init();
    }
    
    async init() {
        console.log('Initializing Springfield Town Builder...');
        
        try {
            await this.initializeSystems();
            await this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('Game initialized successfully');
            
            this.emit('gameInitialized');
        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.emit('gameInitializationFailed', error);
        }
    }
    
    async initializeSystems() {
        // Initialize 3D engine
        const canvas = document.getElementById('game-canvas');
        this.engine3D = new Engine3D(canvas);
        
        // Initialize camera controller
        this.cameraController = new CameraController(this.engine3D.getCamera(), canvas);
        
        // Initialize core systems
        this.saveSystem = new SaveSystem();
        this.currencySystem = new CurrencySystem();
        this.questSystem = new QuestSystem(this);
        this.buildingSystem = new BuildingSystem(this.engine3D, this.currencySystem, this.questSystem);
        this.characterSystem = new CharacterSystem(this.engine3D, this.buildingSystem, this.currencySystem, this.questSystem);
        this.taskSystem = new TaskSystem(this.characterSystem, this.currencySystem);
        this.multiplayerSystem = new MultiplayerSystem();
        
        // Initialize UI managers
        this.uiManager = new UIManager(this);
        this.menuSystem = new MenuSystem(this);
        
        // Wait for systems to initialize
        await Promise.all([
            this.buildingSystem.init?.() || Promise.resolve(),
            this.characterSystem.init?.() || Promise.resolve(),
            this.taskSystem.init?.() || Promise.resolve(),
            this.multiplayerSystem.init?.() || Promise.resolve()
        ]);

        // Initialize UI components after systems are ready
        this.buildingMenu = new BuildingMenu(this.buildingSystem);
        this.taskPanel = new TaskPanel(this.characterSystem, this.taskSystem);

        this.buildingMenu.init();
        this.taskPanel.init();
    }
    
    setupEventListeners() {
        // System event listeners
        this.saveSystem.on('gameLoaded', (event) => this.onGameLoaded(event));
        this.saveSystem.on('gameSaved', (event) => this.onGameSaved(event));
        this.saveSystem.on('newGameCreated', (event) => this.onNewGameCreated(event));

        // Setup real-time notifications
        this.setupNotificationListeners();
        
        this.currencySystem.on('levelUp', (event) => this.onLevelUp(event));
        
        this.buildingSystem.on('buildingPlaced', (event) => this.onBuildingPlaced(event));
        this.buildingSystem.on('buildingSelected', (building) => this.onBuildingSelected(building));
        
        this.characterSystem.on('characterSelected', (character) => this.onCharacterSelected(character));
        this.characterSystem.on('taskCompleted', (event) => this.onTaskCompleted(event));
        
        // Engine update loop
        this.engine3D.on('update', (deltaTime) => this.update(deltaTime));

        // Day/Night cycle events
        if (this.engine3D.dayNightCycle) {
            this.engine3D.dayNightCycle.on('timeModeChanged', (data) => {
                const mode = data.useSystemTime ? 'System Time' : 'Game Time';
                console.log(`Time mode changed to: ${mode}`);

                if (window.notificationSystem) {
                    notificationSystem.info(
                        'Time Sync',
                        `Now using ${mode} for day/night cycle`,
                        { duration: 3000 }
                    );
                }
            });

            this.engine3D.dayNightCycle.on('dayChanged', (data) => {
                console.log('Day boundary crossed - new day started');

                if (window.notificationSystem) {
                    notificationSystem.special(
                        'New Day!',
                        'A new day has begun in Springfield',
                        { duration: 2000 }
                    );
                }
            });
        }

        // Window events
        window.addEventListener('beforeunload', () => this.onBeforeUnload());
    }
    
    // Game lifecycle methods
    async startGame(saveSlot = null) {
        if (!this.isInitialized) {
            console.error('Game not initialized');
            return false;
        }
        
        if (saveSlot !== null) {
            // Load existing game
            const saveData = await this.saveSystem.loadGame(saveSlot);
            if (!saveData) {
                console.error('Failed to load game from slot', saveSlot);
                return false;
            }
        } else {
            // Start new game with default state
            this.gameState = this.createDefaultGameState();
            this.applyGameState(this.gameState);
        }
        
        // Start game systems
        this.engine3D.start();
        this.isRunning = true;
        
        // Show game UI
        this.uiManager.showGameUI();

        // Initialize demo system
        if (window.RealTimeDemo && !this.realTimeDemo) {
            this.realTimeDemo = new RealTimeDemo(this);
        }

        console.log('Game started with real-time features');
        this.emit('gameStarted');
        return true;
    }
    
    pauseGame() {
        if (!this.isRunning) return;
        
        this.isPaused = true;
        this.emit('gamePaused');
    }
    
    resumeGame() {
        if (!this.isRunning || !this.isPaused) return;
        
        this.isPaused = false;
        this.emit('gameResumed');
    }
    
    stopGame() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        this.isPaused = false;
        
        // Stop game systems
        this.engine3D.stop();
        
        // Hide game UI
        this.uiManager.hideGameUI();
        
        console.log('Game stopped');
        this.emit('gameStopped');
    }
    
    async saveGame(slotNumber = null) {
        if (!this.isRunning) return false;
        
        const currentSlot = slotNumber || this.saveSystem.getCurrentSlot();
        if (currentSlot === null) {
            console.error('No save slot specified');
            return false;
        }
        
        const gameState = this.getGameState();
        return await this.saveSystem.saveGame(currentSlot, gameState);
    }
    
    async newGame(slotNumber, townName = 'Springfield') {
        console.log('Creating new game in slot', slotNumber, 'with town name:', townName);

        const defaultState = this.createDefaultGameState(townName);
        console.log('Default game state created:', defaultState);

        const success = await this.saveSystem.saveGame(slotNumber, defaultState);
        console.log('Save game result:', success);

        if (success) {
            this.gameState = defaultState;
            this.applyGameState(this.gameState);
            console.log('New game created successfully');
            return true;
        }

        console.error('Failed to create new game');
        return false;
    }
    
    // Game state management
    createDefaultGameState(townName = 'Springfield') {
        return {
            townName: townName,
            level: 1,
            money: 500, // Reduced starting money
            donuts: 10,  // Reduced starting donuts
            xp: 0,

            buildings: [], // Start with completely empty town
            characters: [], // No starting characters
            tasks: [],

            settings: { ...this.settings },

            stats: {
                timePlayed: 0,
                buildingsBuilt: 0,
                tasksCompleted: 0,
                donutsSpent: 0
            },

            friends: [],

            lastPlayed: new Date().toISOString()
        };
    }
    
    getGameState() {
        if (!this.isRunning) return null;
        
        return {
            townName: this.gameState?.townName || 'Springfield',
            level: this.currencySystem.getLevel(),
            money: this.currencySystem.getMoney(),
            donuts: this.currencySystem.getDonuts(),
            xp: this.currencySystem.getXP(),

            buildings: this.buildingSystem.getState().buildings,
            characters: this.characterSystem.getState().characters,
            tasks: this.taskSystem?.getState()?.tasks || [],
            quests: this.questSystem?.getState() || {},

            settings: { ...this.settings },

            stats: this.gameState?.stats || {
                timePlayed: 0,
                buildingsBuilt: 0,
                tasksCompleted: 0,
                donutsSpent: 0
            },

            friends: this.gameState?.friends || [],

            lastPlayed: new Date().toISOString()
        };
    }
    
    applyGameState(state) {
        this.gameState = state;

        // Apply currency state
        this.currencySystem.setState({
            money: state.money,
            donuts: state.donuts,
            xp: state.xp,
            level: state.level
        });

        // Apply building state
        this.buildingSystem.setState({ buildings: state.buildings });

        // Apply character state
        this.characterSystem.setState({ characters: state.characters });

        // Apply task state
        if (this.taskSystem && state.tasks) {
            this.taskSystem.setState({ tasks: state.tasks });
        }

        // Apply quest state
        if (this.questSystem && state.quests) {
            this.questSystem.setState(state.quests);
        }

        // Apply settings
        if (state.settings) {
            this.settings = { ...state.settings };
        }

        // Show welcome message for empty town
        if (state.buildings.length === 0 && state.characters.length === 0) {
            setTimeout(() => {
                this.showEmptyTownWelcome();
            }, 1000);
        }

        console.log('Game state applied');
    }

    showEmptyTownWelcome() {
        if (window.notificationSystem) {
            notificationSystem.special(
                'Welcome to Empty Springfield!',
                'D\'oh! Springfield is completely empty! Help Homer rebuild the town from scratch by following the quest storyline.',
                {
                    duration: 8000,
                    actions: [
                        {
                            text: 'Start Quest',
                            callback: () => {
                                if (this.uiManager?.questPanel) {
                                    this.uiManager.questPanel.show();
                                }
                            }
                        },
                        {
                            text: 'Story Demo',
                            callback: () => {
                                if (this.uiManager?.storylineDemo) {
                                    this.uiManager.storylineDemo.start();
                                }
                            }
                        }
                    ]
                }
            );
        } else {
            // Fallback if notification system isn't available
            const welcome = document.createElement('div');
            welcome.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 215, 0, 0.95);
                border: 3px solid #FF6B35;
                border-radius: 20px;
                padding: 30px;
                text-align: center;
                z-index: 1000;
                font-family: 'Comic Sans MS', cursive, sans-serif;
                color: #8B4513;
                max-width: 500px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            `;

            welcome.innerHTML = `
                <h2>🏜️ Welcome to Empty Springfield! 🏜️</h2>
                <p style="margin: 15px 0;"><strong>D'oh!</strong> Springfield is completely empty!</p>
                <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 10px; margin: 15px 0;">
                    <p><strong>🎯 Your Mission:</strong></p>
                    <p>• Follow the quest storyline to rebuild Springfield</p>
                    <p>• Start by clicking the 📜 Quests button</p>
                    <p>• Complete objectives to unlock buildings and characters</p>
                    <p>• Watch Springfield come alive!</p>
                </div>
                <button onclick="this.parentElement.remove()"
                        style="background: #4CAF50; color: white; border: none;
                               padding: 12px 25px; border-radius: 15px; cursor: pointer;
                               font-size: 1.1em; font-weight: bold; margin-top: 10px;">
                    Let's Rebuild Springfield!
                </button>
            `;

            document.body.appendChild(welcome);
        }
    }
    
    // Enhanced real-time game loop
    update(deltaTime) {
        if (!this.isRunning || this.isPaused) return;

        // Update camera controller
        this.cameraController.update();

        // Update all game systems in real-time
        this.updateGameSystems(deltaTime);

        // Update game statistics
        if (this.gameState?.stats) {
            this.gameState.stats.timePlayed += deltaTime;
        }

        // Update UI with real-time data
        this.uiManager?.update(deltaTime);

        // Auto-save periodically (every 30 seconds)
        this.handleAutoSave(deltaTime);
    }

    updateGameSystems(deltaTime) {
        // Update building system (income generation, animations)
        if (this.buildingSystem) {
            this.buildingSystem.update(deltaTime);
        }

        // Update character system (movement, tasks, AI)
        if (this.characterSystem) {
            this.characterSystem.update(deltaTime);
        }

        // Update task system (progress, completion)
        if (this.taskSystem) {
            this.taskSystem.update(deltaTime);
        }

        // Update currency system (passive income, bonuses)
        if (this.currencySystem) {
            this.currencySystem.update(deltaTime);
        }

        // Update multiplayer system (sync, events)
        if (this.multiplayerSystem) {
            this.multiplayerSystem.update(deltaTime);
        }
    }

    handleAutoSave(deltaTime) {
        if (!this.autoSaveTimer) {
            this.autoSaveTimer = 0;
        }

        this.autoSaveTimer += deltaTime;

        // Auto-save every 30 seconds
        if (this.autoSaveTimer >= 30) {
            this.autoSaveTimer = 0;
            this.autoSave();
        }
    }

    async autoSave() {
        if (this.saveSystem && this.currentSaveSlot !== null) {
            try {
                const gameState = this.getGameState();
                await this.saveSystem.saveGame(this.currentSaveSlot, gameState);
                console.log('Auto-saved game');

                // Show subtle auto-save indicator
                this.showAutoSaveIndicator();
            } catch (error) {
                console.error('Auto-save failed:', error);
            }
        }
    }

    showAutoSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            z-index: 1000;
            animation: fadeInOut 2s ease-in-out;
        `;
        indicator.textContent = '💾 Auto-saved';

        document.body.appendChild(indicator);

        setTimeout(() => {
            if (indicator.parentElement) {
                indicator.remove();
            }
        }, 2000);
    }
    
    // Event handlers
    onGameLoaded(event) {
        console.log('Game loaded from slot', event.slot);
        this.applyGameState(event.data);
        this.emit('gameStateChanged', event.data);
    }
    
    onGameSaved(event) {
        console.log('Game saved to slot', event.slot);
        this.emit('gameSaved', event);
    }
    
    onNewGameCreated(event) {
        console.log('New game created in slot', event.slot);
        this.applyGameState(event.data);
        this.emit('gameStateChanged', event.data);
    }

    setupNotificationListeners() {
        // Building system notifications
        if (this.buildingSystem) {
            this.buildingSystem.on('buildingPlaced', (data) => {
                if (window.notificationSystem) {
                    notificationSystem.buildingPlaced(data.building?.userData?.name || 'Building');
                }
            });

            this.buildingSystem.on('incomeCollected', (data) => {
                if (window.notificationSystem) {
                    notificationSystem.incomeCollected(data.amount, data.building?.userData?.name || 'Building');
                }
            });
        }

        // Character system notifications
        if (this.characterSystem) {
            this.characterSystem.on('characterSpawned', (data) => {
                if (window.notificationSystem) {
                    notificationSystem.characterSpawned(data.character?.userData?.name || 'Character');
                }
            });
        }

        // Task system notifications
        if (this.taskSystem) {
            this.taskSystem.on('taskCompleted', (data) => {
                if (window.notificationSystem) {
                    notificationSystem.taskCompleted(
                        data.task?.taskData?.name || 'Task',
                        data.rewards || { money: 0, xp: 0 }
                    );
                }
            });
        }

        // Currency system notifications
        if (this.currencySystem) {
            this.currencySystem.on('levelUp', (data) => {
                if (window.notificationSystem) {
                    notificationSystem.levelUp(data.newLevel, data.rewards);
                }
            });

            this.currencySystem.on('moneyChanged', (data) => {
                // Update UI with animation for significant changes
                if (Math.abs(data.change) >= 100) {
                    this.animateCurrencyChange('money', data.change);
                }
            });

            this.currencySystem.on('donutsChanged', (data) => {
                this.animateCurrencyChange('donuts', data.change);
            });
        }

        // Save system notifications
        this.saveSystem.on('gameSaved', () => {
            if (window.notificationSystem) {
                notificationSystem.success('Game Saved', 'Your progress has been saved', { duration: 2000 });
            }
        });

        this.saveSystem.on('gameLoaded', () => {
            if (window.notificationSystem) {
                notificationSystem.info('Game Loaded', 'Welcome back to Springfield!', { duration: 3000 });
            }
        });
    }

    animateCurrencyChange(type, amount) {
        const elementId = type === 'money' ? 'money-amount' : 'donut-amount';
        const element = document.getElementById(elementId);

        if (element) {
            // Add updating class for animation
            element.classList.add('updating');

            // Remove animation class after animation completes
            setTimeout(() => {
                element.classList.remove('updating');
            }, 500);
        }
    }

    onLevelUp(event) {
        console.log('Level up!', event);
        
        // Update statistics
        if (this.gameState?.stats) {
            // Level up might unlock new content
            this.checkUnlocks(event.newLevel);
        }
        
        this.emit('levelUp', event);
    }
    
    onBuildingPlaced(event) {
        console.log('Building placed:', event.type);
        
        // Update statistics
        if (this.gameState?.stats) {
            this.gameState.stats.buildingsBuilt++;
        }
        
        this.emit('buildingPlaced', event);
    }
    
    onBuildingSelected(building) {
        this.uiManager?.showBuildingInfo(building);
    }
    
    onCharacterSelected(character) {
        this.uiManager?.showCharacterInfo(character);
    }
    
    onTaskCompleted(event) {
        console.log('Task completed:', event.task.name);
        
        // Update statistics
        if (this.gameState?.stats) {
            this.gameState.stats.tasksCompleted++;
        }
        
        this.emit('taskCompleted', event);
    }
    
    onBeforeUnload() {
        // Auto-save before closing
        if (this.isRunning && this.settings.autoSave) {
            this.saveGame();
        }
    }
    
    // Utility methods
    checkUnlocks(level) {
        // Check for new building unlocks
        // Check for new character unlocks
        // This would be expanded based on game progression
    }
    
    // Public API methods
    getTownName() {
        return this.gameState?.townName || 'Springfield';
    }
    
    setTownName(name) {
        if (this.gameState) {
            this.gameState.townName = name;
        }
    }
    
    getSettings() {
        return { ...this.settings };
    }
    
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.emit('settingsChanged', this.settings);
    }
    
    // Cleanup
    dispose() {
        console.log('Disposing game...');
        
        this.stopGame();
        
        // Dispose systems
        this.engine3D?.dispose?.();
        this.cameraController?.dispose?.();
        this.saveSystem?.dispose?.();
        this.currencySystem?.dispose?.();
        this.buildingSystem?.dispose?.();
        this.characterSystem?.dispose?.();
        this.taskSystem?.dispose?.();
        this.multiplayerSystem?.dispose?.();
        this.uiManager?.dispose?.();
        this.menuSystem?.dispose?.();
        
        this.removeAllListeners();
    }
}

// Make Game available globally
window.Game = Game;
