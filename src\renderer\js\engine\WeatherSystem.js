/**
 * Springfield Town Builder - Weather System
 * Handles dynamic weather effects like rain, snow, and clouds
 */

class WeatherSystem extends EventEmitter {
    constructor(engine3D) {
        super();
        this.engine3D = engine3D;
        this.scene = engine3D.getScene();
        
        // Weather state
        this.currentWeather = 'clear';
        this.weatherIntensity = 0;
        this.transitionDuration = 5000; // 5 seconds
        
        // Weather systems
        this.rainSystem = null;
        this.snowSystem = null;
        this.cloudSystem = null;
        
        // Particle systems
        this.particles = {
            rain: [],
            snow: [],
            leaves: []
        };
        
        this.init();
    }
    
    init() {
        this.createCloudSystem();
        this.createRainSystem();
        this.createSnowSystem();
        
        // Start with clear weather
        this.setWeather('clear');
    }
    
    createCloudSystem() {
        this.cloudSystem = new THREE.Group();
        
        // Create several cloud layers
        for (let i = 0; i < 8; i++) {
            const cloud = this.createCloud();
            cloud.position.set(
                (Math.random() - 0.5) * 200,
                40 + Math.random() * 20,
                (Math.random() - 0.5) * 200
            );
            cloud.userData.speed = 0.02 + Math.random() * 0.03;
            cloud.userData.direction = Math.random() * Math.PI * 2;
            this.cloudSystem.add(cloud);
        }
        
        this.scene.add(this.cloudSystem);
    }
    
    createCloud() {
        const cloudGroup = new THREE.Group();
        
        // Create cloud using multiple spheres
        const cloudParts = 5 + Math.floor(Math.random() * 3);
        
        for (let i = 0; i < cloudParts; i++) {
            const geometry = new THREE.SphereGeometry(
                3 + Math.random() * 4,
                8,
                6
            );
            
            const material = new THREE.MeshLambertMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.7
            });
            
            const cloudPart = new THREE.Mesh(geometry, material);
            cloudPart.position.set(
                (Math.random() - 0.5) * 8,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 8
            );
            
            cloudGroup.add(cloudPart);
        }
        
        return cloudGroup;
    }
    
    createRainSystem() {
        this.rainSystem = new THREE.Group();
        
        // Create rain particles
        const rainCount = 1000;
        const rainGeometry = new THREE.BufferGeometry();
        const rainPositions = new Float32Array(rainCount * 3);
        const rainVelocities = new Float32Array(rainCount * 3);
        
        for (let i = 0; i < rainCount; i++) {
            const i3 = i * 3;
            
            // Random positions above the scene
            rainPositions[i3] = (Math.random() - 0.5) * 200;
            rainPositions[i3 + 1] = 50 + Math.random() * 50;
            rainPositions[i3 + 2] = (Math.random() - 0.5) * 200;
            
            // Downward velocity with slight randomness
            rainVelocities[i3] = (Math.random() - 0.5) * 0.1;
            rainVelocities[i3 + 1] = -0.5 - Math.random() * 0.3;
            rainVelocities[i3 + 2] = (Math.random() - 0.5) * 0.1;
        }
        
        rainGeometry.setAttribute('position', new THREE.BufferAttribute(rainPositions, 3));
        rainGeometry.setAttribute('velocity', new THREE.BufferAttribute(rainVelocities, 3));
        
        const rainMaterial = new THREE.PointsMaterial({
            color: 0x87ceeb,
            size: 0.1,
            transparent: true,
            opacity: 0.6
        });
        
        this.rainParticles = new THREE.Points(rainGeometry, rainMaterial);
        this.rainSystem.add(this.rainParticles);
        this.scene.add(this.rainSystem);
        
        // Initially hidden
        this.rainSystem.visible = false;
    }
    
    createSnowSystem() {
        this.snowSystem = new THREE.Group();
        
        // Create snow particles
        const snowCount = 500;
        const snowGeometry = new THREE.BufferGeometry();
        const snowPositions = new Float32Array(snowCount * 3);
        const snowVelocities = new Float32Array(snowCount * 3);
        
        for (let i = 0; i < snowCount; i++) {
            const i3 = i * 3;
            
            // Random positions above the scene
            snowPositions[i3] = (Math.random() - 0.5) * 200;
            snowPositions[i3 + 1] = 50 + Math.random() * 50;
            snowPositions[i3 + 2] = (Math.random() - 0.5) * 200;
            
            // Gentle downward velocity
            snowVelocities[i3] = (Math.random() - 0.5) * 0.05;
            snowVelocities[i3 + 1] = -0.1 - Math.random() * 0.1;
            snowVelocities[i3 + 2] = (Math.random() - 0.5) * 0.05;
        }
        
        snowGeometry.setAttribute('position', new THREE.BufferAttribute(snowPositions, 3));
        snowGeometry.setAttribute('velocity', new THREE.BufferAttribute(snowVelocities, 3));
        
        const snowMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 0.3,
            transparent: true,
            opacity: 0.8
        });
        
        this.snowParticles = new THREE.Points(snowGeometry, snowMaterial);
        this.snowSystem.add(this.snowParticles);
        this.scene.add(this.snowSystem);
        
        // Initially hidden
        this.snowSystem.visible = false;
    }
    
    setWeather(weatherType, intensity = 1.0) {
        if (this.currentWeather === weatherType) return;
        
        const previousWeather = this.currentWeather;
        this.currentWeather = weatherType;
        this.weatherIntensity = intensity;
        
        // Transition between weather types
        this.transitionWeather(previousWeather, weatherType, intensity);
        
        this.emit('weatherChanged', { 
            from: previousWeather, 
            to: weatherType, 
            intensity 
        });
    }
    
    transitionWeather(from, to, intensity) {
        // Hide all weather systems first
        this.rainSystem.visible = false;
        this.snowSystem.visible = false;
        
        // Show appropriate weather system
        switch (to) {
            case 'rain':
                this.rainSystem.visible = true;
                this.updateRainIntensity(intensity);
                this.updateLighting('rainy');
                break;
            case 'snow':
                this.snowSystem.visible = true;
                this.updateSnowIntensity(intensity);
                this.updateLighting('snowy');
                break;
            case 'clear':
            default:
                this.updateLighting('clear');
                break;
        }
        
        // Update cloud opacity based on weather
        this.updateCloudCover(to, intensity);
    }
    
    updateRainIntensity(intensity) {
        if (this.rainParticles) {
            this.rainParticles.material.opacity = 0.6 * intensity;
            this.rainParticles.material.size = 0.1 * intensity;
        }
    }
    
    updateSnowIntensity(intensity) {
        if (this.snowParticles) {
            this.snowParticles.material.opacity = 0.8 * intensity;
            this.snowParticles.material.size = 0.3 * intensity;
        }
    }
    
    updateCloudCover(weatherType, intensity) {
        let targetOpacity = 0.3;
        
        switch (weatherType) {
            case 'rain':
                targetOpacity = 0.8 * intensity;
                break;
            case 'snow':
                targetOpacity = 0.6 * intensity;
                break;
            case 'clear':
                targetOpacity = 0.2;
                break;
        }
        
        // Animate cloud opacity
        this.cloudSystem.children.forEach(cloud => {
            cloud.children.forEach(part => {
                part.material.opacity = targetOpacity;
            });
        });
    }
    
    updateLighting(weatherType) {
        const lights = this.engine3D.lights;
        if (!lights) return;
        
        switch (weatherType) {
            case 'rain':
                lights.sun.intensity = 0.4;
                lights.ambient.intensity = 0.3;
                lights.hemisphere.intensity = 0.2;
                break;
            case 'snow':
                lights.sun.intensity = 0.6;
                lights.ambient.intensity = 0.4;
                lights.hemisphere.intensity = 0.3;
                break;
            case 'clear':
            default:
                lights.sun.intensity = 0.8;
                lights.ambient.intensity = 0.4;
                lights.hemisphere.intensity = 0.3;
                break;
        }
    }
    
    update(deltaTime) {
        // Update cloud movement
        this.updateClouds(deltaTime);
        
        // Update particle systems
        if (this.currentWeather === 'rain' && this.rainSystem.visible) {
            this.updateRainParticles(deltaTime);
        }
        
        if (this.currentWeather === 'snow' && this.snowSystem.visible) {
            this.updateSnowParticles(deltaTime);
        }
    }
    
    updateClouds(deltaTime) {
        this.cloudSystem.children.forEach(cloud => {
            const speed = cloud.userData.speed;
            const direction = cloud.userData.direction;
            
            cloud.position.x += Math.cos(direction) * speed;
            cloud.position.z += Math.sin(direction) * speed;
            
            // Wrap around the scene
            if (cloud.position.x > 100) cloud.position.x = -100;
            if (cloud.position.x < -100) cloud.position.x = 100;
            if (cloud.position.z > 100) cloud.position.z = -100;
            if (cloud.position.z < -100) cloud.position.z = 100;
        });
    }
    
    updateRainParticles(deltaTime) {
        const positions = this.rainParticles.geometry.attributes.position.array;
        const velocities = this.rainParticles.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            // Update positions
            positions[i] += velocities[i];
            positions[i + 1] += velocities[i + 1];
            positions[i + 2] += velocities[i + 2];
            
            // Reset particles that fall below ground
            if (positions[i + 1] < 0) {
                positions[i] = (Math.random() - 0.5) * 200;
                positions[i + 1] = 50 + Math.random() * 50;
                positions[i + 2] = (Math.random() - 0.5) * 200;
            }
        }
        
        this.rainParticles.geometry.attributes.position.needsUpdate = true;
    }
    
    updateSnowParticles(deltaTime) {
        const positions = this.snowParticles.geometry.attributes.position.array;
        const velocities = this.snowParticles.geometry.attributes.velocity.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            // Update positions with gentle swaying
            positions[i] += velocities[i] + Math.sin(Date.now() * 0.001 + i) * 0.01;
            positions[i + 1] += velocities[i + 1];
            positions[i + 2] += velocities[i + 2];
            
            // Reset particles that fall below ground
            if (positions[i + 1] < 0) {
                positions[i] = (Math.random() - 0.5) * 200;
                positions[i + 1] = 50 + Math.random() * 50;
                positions[i + 2] = (Math.random() - 0.5) * 200;
            }
        }
        
        this.snowParticles.geometry.attributes.position.needsUpdate = true;
    }
    
    // Public methods for weather control
    startRain(intensity = 1.0) {
        this.setWeather('rain', intensity);
    }
    
    startSnow(intensity = 1.0) {
        this.setWeather('snow', intensity);
    }
    
    clearWeather() {
        this.setWeather('clear');
    }
    
    dispose() {
        // Clean up resources
        if (this.rainSystem) {
            this.scene.remove(this.rainSystem);
        }
        if (this.snowSystem) {
            this.scene.remove(this.snowSystem);
        }
        if (this.cloudSystem) {
            this.scene.remove(this.cloudSystem);
        }
        
        this.removeAllListeners();
    }
}
