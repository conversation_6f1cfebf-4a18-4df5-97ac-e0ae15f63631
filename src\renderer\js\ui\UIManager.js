/**
 * Springfield Town Builder - UI Manager
 * Manages all UI interactions and updates
 */

class UIManager extends EventEmitter {
    constructor(game) {
        super();
        this.game = game;
        
        // UI state
        this.isGameUIVisible = false;
        this.activePanels = new Set();
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupEffectsPanel();

        // Set up building panel with delay to ensure DOM is ready
        setTimeout(() => {
            this.setupBuildingPanel();
        }, 100);

        console.log('UI Manager initialized');
    }

    setupEffectsPanel() {
        // Initialize effects panel when game is available
        if (this.game && this.game.engine3D) {
            this.effectsPanel = new EffectsPanel(this.game.engine3D, this.game);

            // Add effects button to top UI bar
            this.addEffectsButton();
        }
    }

    addEffectsButton() {
        const topButtons = document.querySelector('.top-buttons');
        if (topButtons) {
            const effectsBtn = document.createElement('button');
            effectsBtn.className = 'ui-button';
            effectsBtn.innerHTML = '🎨 Effects';
            effectsBtn.onclick = () => this.toggleEffectsPanel();
            topButtons.appendChild(effectsBtn);
        }
    }

    toggleEffectsPanel() {
        if (this.effectsPanel) {
            this.effectsPanel.toggle();
        }
    }

    setupRealTimeStatus() {
        if (this.game) {
            this.realTimeStatus = new RealTimeStatus(this.game);

            // Add status button to top UI bar
            this.addStatusButton();

            // Add demo button
            this.addDemoButton();

            // Add quest panel
            this.setupQuestPanel();

            // Add storyline demo button
            this.addStorylineButton();

            // Setup building panel
            this.setupBuildingPanel();

            // Setup level progress bar
            this.setupLevelProgressBar();
        }
    }

    addStatusButton() {
        const topButtons = document.querySelector('.top-buttons');
        if (topButtons) {
            const statusBtn = document.createElement('button');
            statusBtn.className = 'ui-button';
            statusBtn.innerHTML = '📊 Status';
            statusBtn.onclick = () => this.toggleRealTimeStatus();
            topButtons.appendChild(statusBtn);
        }
    }

    toggleRealTimeStatus() {
        if (this.realTimeStatus) {
            this.realTimeStatus.toggle();
        }
    }

    addDemoButton() {
        const topButtons = document.querySelector('.top-buttons');
        if (topButtons) {
            const demoBtn = document.createElement('button');
            demoBtn.className = 'ui-button demo-button';
            demoBtn.innerHTML = '🎬 Demo';
            demoBtn.onclick = () => this.startRealTimeDemo();
            demoBtn.title = 'Start Real-Time Features Demo';
            topButtons.appendChild(demoBtn);
        }
    }

    startRealTimeDemo() {
        if (this.game && window.RealTimeDemo) {
            if (!this.realTimeDemo) {
                this.realTimeDemo = new RealTimeDemo(this.game);
            }

            this.realTimeDemo.start();

            // Show notification
            if (window.notificationSystem) {
                notificationSystem.info(
                    'Demo Started',
                    'Real-time features demonstration is now running',
                    { duration: 3000 }
                );
            }
        }
    }

    setupQuestPanel() {
        if (this.game && this.game.questSystem) {
            this.questPanel = new QuestPanel(this.game.questSystem);

            // Add quest button to top UI bar
            this.addQuestButton();
        }
    }

    addQuestButton() {
        const topButtons = document.querySelector('.top-buttons');
        if (topButtons) {
            const questBtn = document.createElement('button');
            questBtn.className = 'ui-button quest-button';
            questBtn.innerHTML = '📜 Quests';
            questBtn.onclick = () => this.toggleQuestPanel();
            questBtn.title = 'View Quests and Story Progress';
            topButtons.appendChild(questBtn);
        }
    }

    toggleQuestPanel() {
        if (this.questPanel) {
            this.questPanel.toggle();
        }
    }

    addStorylineButton() {
        const topButtons = document.querySelector('.top-buttons');
        if (topButtons) {
            const storyBtn = document.createElement('button');
            storyBtn.className = 'ui-button storyline-button';
            storyBtn.innerHTML = '📖 Story';
            storyBtn.onclick = () => this.startStorylineDemo();
            storyBtn.title = 'Experience the Springfield Storyline';
            topButtons.appendChild(storyBtn);
        }
    }

    startStorylineDemo() {
        if (this.game && window.StorylineDemo) {
            if (!this.storylineDemo) {
                this.storylineDemo = new StorylineDemo(this.game);
            }

            this.storylineDemo.start();

            // Show notification
            if (window.notificationSystem) {
                notificationSystem.info(
                    'Storyline Demo Started',
                    'Experience the complete Springfield story progression',
                    { duration: 3000 }
                );
            }
        }
    }
    
    setupEventListeners() {
        // Listen for game events to update UI
        this.game.on('gameStarted', () => this.onGameStarted());
        this.game.on('gameStopped', () => this.onGameStopped());
        this.game.on('buildingSelected', (building) => this.showBuildingInfo(building));
        this.game.on('characterSelected', (character) => this.showCharacterInfo(character));
        
        // Currency display updates
        this.game.currencySystem.on('moneyChanged', () => this.updateCurrencyDisplay());
        this.game.currencySystem.on('donutsChanged', () => this.updateCurrencyDisplay());
        this.game.currencySystem.on('xpChanged', () => this.updateCurrencyDisplay());
        this.game.currencySystem.on('levelUp', (event) => this.showLevelUpNotification(event));
    }
    
    showGameUI() {
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            gameContainer.classList.remove('hidden');
            this.isGameUIVisible = true;
        }
    }
    
    hideGameUI() {
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            gameContainer.classList.add('hidden');
            this.isGameUIVisible = false;
        }
        
        // Hide all panels
        this.hideAllPanels();
    }
    
    onGameStarted() {
        this.updateCurrencyDisplay();
        this.updateTownInfo();

        // Initialize effects panel if not already done
        if (!this.effectsPanel && this.game.engine3D) {
            this.setupEffectsPanel();
        }

        // Initialize real-time status panel
        if (!this.realTimeStatus) {
            this.setupRealTimeStatus();
        }

        // Ensure building panel is set up (fallback)
        this.ensureBuildingPanelSetup();
    }
    
    onGameStopped() {
        this.hideAllPanels();
    }
    
    updateCurrencyDisplay() {
        // Money
        const moneyElement = document.getElementById('money-amount');
        if (moneyElement) {
            moneyElement.textContent = this.formatNumber(this.game.currencySystem.getMoney());
        }
        
        // Donuts
        const donutElement = document.getElementById('donut-amount');
        if (donutElement) {
            donutElement.textContent = this.formatNumber(this.game.currencySystem.getDonuts());
        }
        
        // XP
        const xpElement = document.getElementById('xp-amount');
        if (xpElement) {
            xpElement.textContent = this.formatNumber(this.game.currencySystem.getXP());
        }
        
        // Level
        const levelElement = document.getElementById('town-level');
        if (levelElement) {
            levelElement.textContent = this.game.currencySystem.getLevel().toString();
        }
    }
    
    updateTownInfo() {
        const townNameElement = document.getElementById('town-name');
        if (townNameElement) {
            townNameElement.textContent = this.game.getTownName();
        }
    }
    
    showBuildingInfo(building) {
        const panel = document.getElementById('building-info-panel');
        if (!panel) return;

        // Store reference to current building
        this.currentBuilding = building;

        // Get building data from building system
        const buildingData = this.game.buildingSystem.buildingData[building.userData.type];

        // Populate building info
        const nameElement = document.getElementById('building-name');
        const imageElement = document.getElementById('building-image');
        const descriptionElement = document.getElementById('building-description');

        if (nameElement) nameElement.textContent = building.userData.name;
        if (descriptionElement) {
            const income = building.userData.income;
            const incomeText = income ? `Generates $${income.amount} every ${Math.floor(income.interval/60)} minutes` : 'Decorative building';
            descriptionElement.innerHTML = `
                <div>Level ${building.userData.level} building</div>
                <div style="font-size: 0.9em; color: #666; margin-top: 5px;">${incomeText}</div>
                <div style="font-size: 0.8em; color: #888; margin-top: 5px;">Pending: $${building.userData.pendingIncome || 0}</div>
            `;
        }

        // Set up action buttons
        this.setupBuildingButtons(building, buildingData);

        // Show panel
        panel.classList.remove('hidden');
        this.activePanels.add('building-info');

        // Set up close button
        const closeBtn = document.getElementById('close-building-panel');
        if (closeBtn) {
            closeBtn.onclick = () => this.hideBuildingInfo();
        }
    }

    setupBuildingButtons(building, buildingData) {
        const upgradeBtn = document.getElementById('upgrade-building-btn');
        const moveBtn = document.getElementById('move-building-btn');
        const storeBtn = document.getElementById('store-building-btn');

        // Upgrade button
        if (upgradeBtn) {
            const currentLevel = building.userData.level;
            const maxLevel = buildingData?.maxLevel || 5;
            const upgrade = buildingData?.upgrades?.find(u => u.level === currentLevel + 1);

            if (upgrade && currentLevel < maxLevel) {
                upgradeBtn.style.display = 'block';
                upgradeBtn.textContent = `Upgrade ($${upgrade.cost.amount})`;
                upgradeBtn.onclick = () => this.upgradeBuilding(building, upgrade);

                // Check if player can afford upgrade
                const canAfford = this.game.currencySystem.canAfford(upgrade.cost.amount);
                upgradeBtn.disabled = !canAfford;
                upgradeBtn.style.opacity = canAfford ? '1' : '0.5';
            } else {
                upgradeBtn.style.display = 'none';
            }
        }

        // Move button
        if (moveBtn) {
            moveBtn.onclick = () => this.startMoveBuilding(building);
        }

        // Store button (remove building)
        if (storeBtn) {
            storeBtn.onclick = () => this.storeBuilding(building);
        }
    }
    
    upgradeBuilding(building, upgrade) {
        const success = this.game.buildingSystem.upgradeBuilding(building.userData.id);

        if (success) {
            // Show success notification
            this.showUpgradeNotification(building, upgrade);

            // Refresh the building info panel
            this.showBuildingInfo(building);
        } else {
            alert('Cannot upgrade building. Not enough money or max level reached.');
        }
    }

    startMoveBuilding(building) {
        this.hideBuildingInfo();
        this.game.buildingSystem.startMoveMode(building);

        // Show instruction
        this.showMoveInstruction();
    }

    storeBuilding(building) {
        const confirmStore = confirm(`Are you sure you want to store ${building.userData.name}? You'll get 50% of the original cost back.`);

        if (confirmStore) {
            // Calculate refund (50% of original cost)
            const buildingData = this.game.buildingSystem.buildingData[building.userData.type];
            if (buildingData && buildingData.cost.type === 'money') {
                const refund = Math.floor(buildingData.cost.amount * 0.5);
                this.game.currencySystem.addMoney(refund, `store_${building.userData.type}`);
            }

            // Remove building
            this.game.buildingSystem.removeBuilding(building.userData.id);
            this.hideBuildingInfo();

            // Show notification
            this.showStoreNotification(building);
        }
    }

    showUpgradeNotification(building, upgrade) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            font-weight: bold;
            animation: slideInRight 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.5em;">⬆️</span>
                <div>
                    <div>${building.userData.name} upgraded!</div>
                    <div style="font-size: 0.8em; opacity: 0.9;">Now Level ${building.userData.level}</div>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    showMoveInstruction() {
        const instruction = document.createElement('div');
        instruction.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 193, 7, 0.95);
            border: 3px solid #FF6B35;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            color: #8B4513;
            font-weight: bold;
        `;

        instruction.innerHTML = `
            <div style="font-size: 1.2em; margin-bottom: 10px;">🏠 Move Building</div>
            <div>Click on a new location to move the building</div>
            <div style="font-size: 0.9em; margin-top: 10px;">Right-click to cancel</div>
        `;

        document.body.appendChild(instruction);

        // Remove after 4 seconds
        setTimeout(() => {
            if (instruction.parentElement) {
                instruction.remove();
            }
        }, 4000);
    }

    showStoreNotification(building) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            font-weight: bold;
            animation: slideInRight 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.5em;">📦</span>
                <div>
                    <div>${building.userData.name} stored!</div>
                    <div style="font-size: 0.8em; opacity: 0.9;">Refund received</div>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    hideBuildingInfo() {
        const panel = document.getElementById('building-info-panel');
        if (panel) {
            panel.classList.add('hidden');
            this.activePanels.delete('building-info');
        }
        this.currentBuilding = null;
    }
    
    showCharacterInfo(character) {
        const panel = document.getElementById('character-panel');
        if (!panel) return;
        
        // Populate character info
        const nameElement = document.getElementById('character-name');
        const statusElement = document.getElementById('character-status');
        
        if (nameElement) nameElement.textContent = character.userData.name;
        if (statusElement) {
            const status = character.userData.currentTask ? 
                `Busy: ${character.userData.currentTask.name}` : 
                'Available';
            statusElement.textContent = status;
        }
        
        // Show panel
        panel.classList.remove('hidden');
        this.activePanels.add('character-info');
        
        // Set up close button
        const closeBtn = document.getElementById('close-character-panel');
        if (closeBtn) {
            closeBtn.onclick = () => this.hideCharacterInfo();
        }
    }
    
    hideCharacterInfo() {
        const panel = document.getElementById('character-panel');
        if (panel) {
            panel.classList.add('hidden');
            this.activePanels.delete('character-info');
        }
    }
    
    showLevelUpNotification(event) {
        // Create level up notification
        const notification = document.createElement('div');
        notification.className = 'level-up-notification';
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border: 3px solid #FF6B35;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            z-index: 1000;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: levelUpPulse 0.5s ease-out;
        `;
        
        notification.innerHTML = `
            <h2 style="color: #8B4513; margin-bottom: 15px;">🎉 Level Up! 🎉</h2>
            <p style="color: #8B4513; font-size: 1.2em; margin-bottom: 15px;">
                Welcome to Level ${event.newLevel}!
            </p>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 20px;">
                <div style="color: #4CAF50; font-weight: bold;">
                    +${event.rewards.money} 💰
                </div>
                <div style="color: #FF6B35; font-weight: bold;">
                    +${event.rewards.donuts} 🍩
                </div>
            </div>
            <button onclick="this.parentElement.remove()" 
                    style="background: #4CAF50; color: white; border: none; 
                           padding: 10px 20px; border-radius: 15px; cursor: pointer;">
                Awesome!
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    hideAllPanels() {
        this.hideBuildingInfo();
        this.hideCharacterInfo();
        this.activePanels.clear();
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    update(deltaTime) {
        // Update any animated UI elements
    }
    
    updateBuildingMenu() {
        const buildingButtons = document.getElementById('building-buttons');
        if (!buildingButtons) return;

        buildingButtons.innerHTML = '';

        // Get available buildings from building system
        const buildings = this.game.buildingSystem.buildingData;

        // Check if building data is loaded
        if (!buildings || Object.keys(buildings).length === 0) {
            buildingButtons.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">Loading buildings...</div>';
            return;
        }

        Object.entries(buildings).forEach(([buildingType, buildingData]) => {
            // Filter by category
            if (this.currentCategory !== 'all' && buildingData.category !== this.currentCategory) {
                return;
            }

            const button = document.createElement('button');

            // Check if building is unlocked
            const isUnlocked = this.game.questSystem ?
                this.game.questSystem.isUnlocked('building', buildingType) : true;

            console.log(`Building ${buildingType}: unlocked=${isUnlocked}, category=${buildingData.category}`);

            button.className = `building-btn ${isUnlocked ? 'unlocked' : 'locked'}`;

            // Get cost display
            let costDisplay = '';
            if (buildingData.cost) {
                if (buildingData.cost.type === 'money') {
                    costDisplay = `$${buildingData.cost.amount}`;
                } else if (buildingData.cost.type === 'donuts') {
                    costDisplay = `${buildingData.cost.amount}🍩`;
                    button.classList.add('donut-cost');
                } else if (buildingData.cost.type === 'free') {
                    costDisplay = 'FREE';
                }
            }

            let unlockInfo = '';
            if (!isUnlocked && this.game.questSystem) {
                const requirements = this.game.questSystem.getUnlockRequirements('building', buildingType);
                if (requirements) {
                    const reqText = [];
                    if (requirements.level > 1) reqText.push(`Level ${requirements.level}`);
                    if (requirements.quest) reqText.push('Complete Quest');
                    unlockInfo = `<div class="unlock-requirement">🔒 ${reqText.join(', ')}</div>`;
                }
            }

            button.innerHTML = `
                <div class="building-icon">${buildingData.icon || '🏢'}</div>
                <div class="building-name">${buildingData.name}</div>
                <div class="building-cost">${costDisplay}</div>
                ${unlockInfo}
            `;

            // Add click handler with multiple methods for reliability
            if (isUnlocked) {
                // Method 1: onclick property
                button.onclick = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('=== BUILDING BUTTON CLICKED (onclick) ===');
                    console.log('Building type:', buildingType);
                    console.log('Building data:', buildingData);
                    this.handleBuildingSelection(buildingType);
                };

                // Method 2: addEventListener for backup
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('=== BUILDING BUTTON CLICKED (addEventListener) ===');
                    console.log('Building type:', buildingType);
                    this.handleBuildingSelection(buildingType);
                });

                // Method 3: Add data attribute for manual clicking
                button.setAttribute('data-building-type', buildingType);
                button.setAttribute('data-clickable', 'true');

                // Make sure button is properly styled as clickable
                button.style.cursor = 'pointer';
                button.style.pointerEvents = 'auto';

            } else {
                button.onclick = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('=== LOCKED BUILDING CLICKED ===');
                    console.log('Building type:', buildingType);
                    this.handleLockedBuildingClick(buildingType);
                };

                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.handleLockedBuildingClick(buildingType);
                });

                button.style.cursor = 'not-allowed';
                button.style.opacity = '0.6';
            }

            buildingButtons.appendChild(button);
        });
    }

    handleBuildingSelection(buildingType) {
        console.log('=== HANDLING BUILDING SELECTION ===');
        console.log('Building type:', buildingType);
        console.log('Building system available:', !!this.game.buildingSystem);
        console.log('Building data available:', !!this.game.buildingSystem?.buildingData);
        console.log('Specific building data:', this.game.buildingSystem?.buildingData?.[buildingType]);

        if (!this.game.buildingSystem) {
            console.error('❌ Building system not available!');
            alert('Building system error - please refresh the page');
            return;
        }

        if (!this.game.buildingSystem.buildingData || !this.game.buildingSystem.buildingData[buildingType]) {
            console.error('❌ Building data not available for:', buildingType);
            alert(`Building data for "${buildingType}" not found!`);
            return;
        }

        console.log('✅ Attempting to enter placement mode...');
        const result = this.game.buildingSystem.enterPlacementMode(buildingType);
        console.log('Placement mode result:', result);

        // Additional debugging for placement mode
        setTimeout(() => {
            console.log('=== PLACEMENT MODE STATUS CHECK ===');
            console.log('Placement mode active:', this.game.buildingSystem.placementMode);
            console.log('Selected building:', this.game.buildingSystem.selectedBuilding);
            console.log('Placement preview exists:', !!this.game.buildingSystem.placementPreview);

            if (this.game.buildingSystem.placementMode) {
                console.log('✅ Placement mode is active - move mouse over ground to see green preview');
                console.log('✅ Click on green ground to place the building');
            } else {
                console.log('❌ Placement mode failed to activate');
                console.log('Trying to force placement mode...');
                this.game.buildingSystem.placementMode = true;
                this.game.buildingSystem.selectedBuilding = buildingType;
            }
        }, 100);

        if (result) {
            console.log('✅ Placement mode entered successfully');
            // Close panel after successful selection
            const panel = document.getElementById('building-panel');
            if (panel) {
                panel.style.display = 'none';
                console.log('✅ Building panel closed');
            }
        } else {
            console.error('❌ Failed to enter placement mode');
            alert('Failed to enter building placement mode. Please try again.');
        }
    }

    handleLockedBuildingClick(buildingType) {
        console.log('=== LOCKED BUILDING CLICKED ===');
        console.log('Building type:', buildingType);

        // Show unlock requirements
        if (window.notificationSystem) {
            const requirements = this.game.questSystem?.getUnlockRequirements('building', buildingType);
            let message = 'This building is locked.';
            if (requirements) {
                const reqParts = [];
                if (requirements.level > 1) reqParts.push(`Reach level ${requirements.level}`);
                if (requirements.quest) reqParts.push(`Complete "${requirements.quest.replace(/_/g, ' ')}" quest`);
                if (reqParts.length > 0) {
                    message += ` Requirements: ${reqParts.join(', ')}.`;
                }
            }

            notificationSystem.warning(
                'Building Locked',
                message,
                { duration: 4000 }
            );
        } else {
            // Fallback alert if notification system not available
            alert('This building is locked. You need to reach a higher level or complete certain quests to unlock it.');
        }
    }

    setupBuildingPanel() {
        console.log('=== SETTING UP BUILDING PANEL ===');

        const buildMenuButton = document.getElementById('build-menu-toggle');
        const buildingPanel = document.getElementById('building-panel');
        const closePanelBtn = document.getElementById('close-building-panel');
        const buildingSearch = document.getElementById('building-search');

        console.log('Build menu button:', !!buildMenuButton);
        console.log('Building panel:', !!buildingPanel);
        console.log('Close panel button:', !!closePanelBtn);
        console.log('Building search:', !!buildingSearch);

        if (!buildMenuButton || !buildingPanel) {
            console.error('Required building panel elements not found!');
            return;
        }

        // Toggle panel visibility
        buildMenuButton.addEventListener('click', () => {
            console.log('=== BUILD MENU BUTTON CLICKED ===');
            const isVisible = buildingPanel.style.display !== 'none';
            buildingPanel.style.display = isVisible ? 'none' : 'block';
            console.log('Panel visibility changed to:', buildingPanel.style.display);

            if (!isVisible) {
                console.log('Updating building menu...');
                this.updateBuildingMenu();
                this.currentCategory = 'all';
                this.updateCategoryTabs();
            }
        });

        // Close panel
        closePanelBtn.addEventListener('click', () => {
            buildingPanel.style.display = 'none';
        });

        // Search functionality
        buildingSearch.addEventListener('input', (e) => {
            this.filterBuildings(e.target.value);
        });

        // Category tabs
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.currentCategory = e.target.dataset.category;
                this.updateCategoryTabs();
                this.updateBuildingMenu();
            });
        });

        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!buildMenuButton.contains(e.target) && !buildingPanel.contains(e.target)) {
                if (buildingPanel.style.display !== 'none') {
                    buildingPanel.style.display = 'none';
                }
            }
        });

        // Listen for building data to be loaded
        this.game.buildingSystem.on('buildingDataLoaded', () => {
            console.log('Building data loaded, updating building menu');
            this.updateBuildingMenu();
        });

        // Initialize
        this.currentCategory = 'all';

        // Only update if building data is already loaded
        if (this.game.buildingSystem.buildingData && Object.keys(this.game.buildingSystem.buildingData).length > 0) {
            this.updateBuildingMenu();
        }
    }

    updateCategoryTabs() {
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.category === this.currentCategory);
        });
    }

    filterBuildings(searchTerm) {
        const buildingButtons = document.querySelectorAll('.building-btn');
        const term = searchTerm.toLowerCase();

        buildingButtons.forEach(btn => {
            const buildingName = btn.querySelector('.building-name')?.textContent.toLowerCase() || '';
            const matches = buildingName.includes(term);
            btn.style.display = matches ? 'flex' : 'none';
        });
    }

    setupLevelProgressBar() {
        // Update level progress bar periodically
        setInterval(() => {
            this.updateLevelProgressBar();
        }, 1000);

        // Initial update
        this.updateLevelProgressBar();

        // Listen for level changes
        if (this.game.currencySystem) {
            this.game.currencySystem.on('levelUp', (data) => {
                this.onLevelUp(data);
            });
        }
    }

    updateLevelProgressBar() {
        const levelNumber = document.getElementById('level-number');
        const progressBarFill = document.getElementById('progress-bar-fill');
        const progressText = document.getElementById('progress-text');

        if (!levelNumber || !progressBarFill || !progressText) return;

        // Get level data from currency system
        const currencySystem = this.game.currencySystem;
        if (!currencySystem) return;

        const currentLevel = currencySystem.level || 1;
        const currentXP = currencySystem.xp || 0;
        const xpForNextLevel = currencySystem.getXPForLevel(currentLevel + 1);
        const xpForCurrentLevel = currencySystem.getXPForLevel(currentLevel);

        const xpInCurrentLevel = currentXP - xpForCurrentLevel;
        const xpNeededForNext = xpForNextLevel - xpForCurrentLevel;
        const progressPercent = Math.min(100, (xpInCurrentLevel / xpNeededForNext) * 100);

        // Update UI elements
        levelNumber.textContent = currentLevel;
        progressBarFill.style.width = `${progressPercent}%`;
        progressText.textContent = `${xpInCurrentLevel} / ${xpNeededForNext} XP`;
    }

    onLevelUp(data) {
        // Animate level up
        const levelNumber = document.getElementById('level-number');
        const progressBar = document.querySelector('.level-progress-bar');

        if (levelNumber && progressBar) {
            // Add celebration animation
            levelNumber.style.animation = 'levelUpPulse 1s ease';
            progressBar.style.animation = 'levelUpGlow 1s ease';

            // Reset animations after completion
            setTimeout(() => {
                levelNumber.style.animation = '';
                progressBar.style.animation = '';
            }, 1000);
        }

        // Show notification
        if (window.notificationSystem) {
            notificationSystem.special(
                '🎉 Level Up!',
                `Congratulations! You reached level ${data.newLevel}!`,
                {
                    duration: 4000,
                    actions: [
                        {
                            text: 'View Unlocks',
                            callback: () => {
                                // Could open a panel showing what was unlocked
                                console.log('Show unlocks for level', data.newLevel);
                            }
                        }
                    ]
                }
            );
        }
    }

    // Debug method for testing building selection
    debugBuildingSelection() {
        console.log('=== Building Selection Debug ===');

        // Check DOM elements
        const buildButton = document.getElementById('build-menu-toggle');
        const buildPanel = document.getElementById('building-panel');
        const buildingButtons = document.getElementById('building-buttons');

        console.log('Build button exists:', !!buildButton);
        console.log('Build panel exists:', !!buildPanel);
        console.log('Building buttons container exists:', !!buildingButtons);

        if (buildPanel) {
            console.log('Panel display style:', buildPanel.style.display);
        }

        // Check building data
        console.log('Building system available:', !!this.game.buildingSystem);
        console.log('Building data loaded:', !!this.game.buildingSystem?.buildingData);

        if (this.game.buildingSystem?.buildingData) {
            const buildings = Object.keys(this.game.buildingSystem.buildingData);
            console.log('Available buildings:', buildings);
            console.log('Building count:', buildings.length);
        }

        // Check building buttons
        const btns = document.querySelectorAll('.building-btn');
        console.log('Building buttons in DOM:', btns.length);

        // Test opening panel
        console.log('Testing panel open...');
        if (buildButton) {
            buildButton.click();
        }

        console.log('=== End Building Selection Debug ===');
    }

    ensureBuildingPanelSetup() {
        console.log('=== ENSURING BUILDING PANEL SETUP ===');

        const buildMenuButton = document.getElementById('build-menu-toggle');
        const buildingPanel = document.getElementById('building-panel');

        if (!buildMenuButton || !buildingPanel) {
            console.error('Building panel elements not found, retrying in 500ms...');
            setTimeout(() => this.ensureBuildingPanelSetup(), 500);
            return;
        }

        // Check if event listener is already attached
        if (!buildMenuButton.hasAttribute('data-listener-attached')) {
            console.log('Adding click listener to build menu button...');

            buildMenuButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('=== BUILD MENU BUTTON CLICKED (FALLBACK) ===');

                const isVisible = buildingPanel.style.display !== 'none';
                buildingPanel.style.display = isVisible ? 'none' : 'block';
                console.log('Panel visibility changed to:', buildingPanel.style.display);

                if (!isVisible) {
                    console.log('Updating building menu...');
                    this.updateBuildingMenu();
                    this.currentCategory = 'all';
                    this.updateCategoryTabs();
                }
            });

            buildMenuButton.setAttribute('data-listener-attached', 'true');
            console.log('Build menu button listener attached successfully');
        } else {
            console.log('Build menu button already has listener attached');
        }

        // Also set up close button
        const closePanelBtn = document.getElementById('close-building-panel');
        if (closePanelBtn && !closePanelBtn.hasAttribute('data-listener-attached')) {
            closePanelBtn.addEventListener('click', () => {
                console.log('Close panel button clicked');
                buildingPanel.style.display = 'none';
            });
            closePanelBtn.setAttribute('data-listener-attached', 'true');
        }

        console.log('Building panel setup ensured');
    }

    dispose() {
        this.hideAllPanels();
        this.removeAllListeners();
    }
}

// Make debug function available globally
window.debugBuildingSelection = function() {
    if (window.game && window.game.uiManager) {
        window.game.uiManager.debugBuildingSelection();
    } else {
        console.log('UI Manager not available. Game not loaded or UI not initialized.');
    }
};

// Manual fix for building menu
window.fixBuildingMenu = function() {
    console.log('=== MANUAL BUILDING MENU FIX ===');

    const buildButton = document.getElementById('build-menu-toggle');
    const buildPanel = document.getElementById('building-panel');

    if (!buildButton || !buildPanel) {
        console.error('Building menu elements not found!');
        return false;
    }

    // Remove existing listeners
    const newButton = buildButton.cloneNode(true);
    buildButton.parentNode.replaceChild(newButton, buildButton);

    // Add new listener
    newButton.addEventListener('click', function() {
        console.log('=== MANUAL BUILD BUTTON CLICKED ===');
        const isVisible = buildPanel.style.display !== 'none';
        buildPanel.style.display = isVisible ? 'none' : 'block';
        console.log('Panel visibility:', buildPanel.style.display);

        if (!isVisible && window.game && window.game.uiManager) {
            window.game.uiManager.updateBuildingMenu();
            window.game.uiManager.currentCategory = 'all';
            window.game.uiManager.updateCategoryTabs();
        }
    });

    console.log('Building menu manually fixed!');
    return true;
};

// Force open building panel
window.openBuildingPanel = function() {
    const panel = document.getElementById('building-panel');
    if (panel) {
        panel.style.display = 'block';
        if (window.game && window.game.uiManager) {
            window.game.uiManager.updateBuildingMenu();
        }
        console.log('Building panel opened manually');
        return true;
    }
    console.error('Building panel not found');
    return false;
};

// Complete diagnostic function
window.diagnoseBuildingIssue = function() {
    console.log('=== COMPLETE BUILDING SYSTEM DIAGNOSIS ===');

    // Check game object
    console.log('1. Game object exists:', !!window.game);
    if (!window.game) {
        console.error('❌ Game object not found! Game not loaded properly.');
        return;
    }

    // Check building system
    console.log('2. Building system exists:', !!window.game.buildingSystem);
    if (!window.game.buildingSystem) {
        console.error('❌ Building system not found! System not initialized.');
        return;
    }

    // Check building data
    const buildingData = window.game.buildingSystem.buildingData;
    console.log('3. Building data exists:', !!buildingData);
    console.log('4. Building data type:', typeof buildingData);
    console.log('5. Building count:', Object.keys(buildingData || {}).length);

    if (!buildingData || Object.keys(buildingData).length === 0) {
        console.error('❌ No building data! Forcing fallback creation...');
        window.game.buildingSystem.createFallbackBuildingData();
        console.log('✅ Fallback data created. New count:', Object.keys(window.game.buildingSystem.buildingData).length);
    }

    // Check UI elements
    console.log('6. Build button exists:', !!document.getElementById('build-menu-toggle'));
    console.log('7. Building panel exists:', !!document.getElementById('building-panel'));
    console.log('8. Building buttons container exists:', !!document.getElementById('building-buttons'));

    // Check UI Manager
    console.log('9. UI Manager exists:', !!window.game.uiManager);

    // Test building menu update
    if (window.game.uiManager) {
        console.log('10. Testing building menu update...');
        try {
            window.game.uiManager.updateBuildingMenu();
            console.log('✅ Building menu updated successfully');
        } catch (e) {
            console.error('❌ Building menu update failed:', e);
        }
    }

    // Test build button
    const buildButton = document.getElementById('build-menu-toggle');
    if (buildButton) {
        console.log('11. Testing build button click...');
        buildButton.click();
        console.log('✅ Build button clicked');
    }

    // Check if panel opened
    const panel = document.getElementById('building-panel');
    if (panel) {
        console.log('12. Panel display after click:', panel.style.display);
        if (panel.style.display === 'none' || !panel.style.display) {
            console.log('🔧 Forcing panel open...');
            panel.style.display = 'block';
        }
    }

    // Check building buttons
    const buildingButtons = document.querySelectorAll('.building-btn');
    console.log('13. Building buttons found:', buildingButtons.length);

    console.log('=== DIAGNOSIS COMPLETE ===');

    // Provide fix recommendations
    if (Object.keys(window.game.buildingSystem.buildingData || {}).length === 0) {
        console.log('🔧 RECOMMENDED FIX: Run forceBuildingDataFix()');
    } else if (buildingButtons.length === 0) {
        console.log('🔧 RECOMMENDED FIX: Run forceBuildingMenuFix()');
    } else {
        console.log('✅ System appears to be working. Try clicking a building in the panel.');
    }
};

// Force building data fix
window.forceBuildingDataFix = function() {
    console.log('=== FORCING BUILDING DATA FIX ===');

    if (!window.game || !window.game.buildingSystem) {
        console.error('❌ Game or building system not available');
        return false;
    }

    // Force create fallback data
    window.game.buildingSystem.createFallbackBuildingData();
    console.log('✅ Fallback building data created');

    // Force emit building data loaded event
    window.game.buildingSystem.emit('buildingDataLoaded');
    console.log('✅ Building data loaded event emitted');

    // Force update UI
    if (window.game.uiManager) {
        window.game.uiManager.updateBuildingMenu();
        console.log('✅ Building menu updated');
    }

    console.log('✅ Building data fix complete');
    return true;
};

// Force complete building menu fix
window.forceBuildingMenuFix = function() {
    console.log('=== FORCING COMPLETE BUILDING MENU FIX ===');

    // Fix building data first
    forceBuildingDataFix();

    // Fix building menu button
    fixBuildingMenu();

    // Force open panel
    openBuildingPanel();

    // Fix building button clicks
    fixBuildingButtonClicks();

    console.log('✅ Complete building menu fix applied');
    return true;
};

// Fix building button clicks specifically
window.fixBuildingButtonClicks = function() {
    console.log('=== FIXING BUILDING BUTTON CLICKS ===');

    const buildingButtons = document.querySelectorAll('.building-btn');
    console.log('Found building buttons:', buildingButtons.length);

    if (buildingButtons.length === 0) {
        console.log('No building buttons found, updating menu first...');
        if (window.game && window.game.uiManager) {
            window.game.uiManager.updateBuildingMenu();
        }

        // Try again after update
        setTimeout(() => {
            const newButtons = document.querySelectorAll('.building-btn');
            console.log('Building buttons after update:', newButtons.length);
            fixBuildingButtonClicks();
        }, 500);
        return;
    }

    buildingButtons.forEach((button, index) => {
        const buildingType = button.getAttribute('data-building-type');
        if (!buildingType) {
            // Try to extract from button content
            const nameElement = button.querySelector('.building-name');
            if (nameElement) {
                const name = nameElement.textContent;
                console.log(`Button ${index} name:`, name);

                // Map common names to building types
                const nameMap = {
                    'Simpson House': 'simpson_house',
                    'Tree': 'tree',
                    'Kwik-E-Mart': 'kwik_e_mart',
                    'Moe\'s Tavern': 'moes_tavern',
                    'Krusty Burger': 'krusty_burger',
                    'Springfield Elementary': 'springfield_elementary',
                    'The Android\'s Dungeon': 'androids_dungeon',
                    'Flanders House': 'flanders_house',
                    'Park Bench': 'bench',
                    'Fountain': 'fountain',
                    'Straight Road': 'road_straight',
                    'Corner Road': 'road_corner',
                    'Road Intersection': 'road_intersection',
                    'T-Junction': 'road_t_junction'
                };

                const mappedType = nameMap[name];
                if (mappedType) {
                    button.setAttribute('data-building-type', mappedType);
                    console.log(`Mapped "${name}" to "${mappedType}"`);
                }
            }
        }

        const finalBuildingType = button.getAttribute('data-building-type');
        if (finalBuildingType) {
            // Remove existing listeners
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Add new click handler
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('=== MANUAL BUILDING BUTTON CLICKED ===');
                console.log('Building type:', finalBuildingType);

                if (window.game && window.game.buildingSystem) {
                    const result = window.game.buildingSystem.enterPlacementMode(finalBuildingType);
                    console.log('Placement mode result:', result);

                    if (result) {
                        // Close panel
                        const panel = document.getElementById('building-panel');
                        if (panel) panel.style.display = 'none';
                    }
                } else {
                    console.error('Game or building system not available');
                }
            });

            // Make sure it's clickable
            newButton.style.cursor = 'pointer';
            newButton.style.pointerEvents = 'auto';

            console.log(`✅ Fixed click handler for button ${index}: ${finalBuildingType}`);
        } else {
            console.warn(`❌ Could not determine building type for button ${index}`);
        }
    });

    console.log('✅ Building button clicks fixed');
    return true;
};

// Test complete building placement flow
window.testBuildingPlacement = function() {
    console.log('=== TESTING COMPLETE BUILDING PLACEMENT ===');

    // Step 1: Check building system
    if (!window.game || !window.game.buildingSystem) {
        console.error('❌ Game or building system not available');
        return false;
    }

    // Step 2: Check building data
    const buildingData = window.game.buildingSystem.buildingData;
    if (!buildingData || !buildingData.simpson_house) {
        console.error('❌ Simpson House data not available');
        return false;
    }

    console.log('✅ Building system and data available');

    // Step 3: Test placement mode entry
    console.log('🔧 Testing placement mode entry...');
    const result = window.game.buildingSystem.enterPlacementMode('simpson_house');
    console.log('Placement mode result:', result);

    if (!result) {
        console.error('❌ Failed to enter placement mode');
        return false;
    }

    // Step 4: Check placement mode status
    setTimeout(() => {
        console.log('🔧 Checking placement mode status...');
        console.log('Placement mode:', window.game.buildingSystem.placementMode);
        console.log('Selected building:', window.game.buildingSystem.selectedBuilding);
        console.log('Preview exists:', !!window.game.buildingSystem.placementPreview);

        if (window.game.buildingSystem.placementMode) {
            console.log('✅ PLACEMENT MODE ACTIVE!');
            console.log('📋 Next steps:');
            console.log('   1. Move mouse over the green ground');
            console.log('   2. You should see a green wireframe preview');
            console.log('   3. Click on the ground to place the building');
            console.log('   4. Simpson House should appear!');
        } else {
            console.log('❌ Placement mode not active');
            console.log('🔧 Trying manual placement at origin...');

            // Try manual placement
            const position = new THREE.Vector3(0, 0, 0);
            const manualResult = window.game.buildingSystem.placeBuildingAt(position);
            console.log('Manual placement result:', manualResult);
        }
    }, 200);

    return true;
};
