/**
 * Springfield Town Builder - UI Manager
 * Manages all UI interactions and updates
 */

class UIManager extends EventEmitter {
    constructor(game) {
        super();
        this.game = game;
        
        // UI state
        this.isGameUIVisible = false;
        this.activePanels = new Set();
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupEffectsPanel();
        console.log('UI Manager initialized');
    }

    setupEffectsPanel() {
        // Initialize effects panel when game is available
        if (this.game && this.game.engine3D) {
            this.effectsPanel = new EffectsPanel(this.game.engine3D);

            // Add effects button to top UI bar
            this.addEffectsButton();
        }
    }

    addEffectsButton() {
        const topButtons = document.querySelector('.top-buttons');
        if (topButtons) {
            const effectsBtn = document.createElement('button');
            effectsBtn.className = 'ui-button';
            effectsBtn.innerHTML = '🎨 Effects';
            effectsBtn.onclick = () => this.toggleEffectsPanel();
            topButtons.appendChild(effectsBtn);
        }
    }

    toggleEffectsPanel() {
        if (this.effectsPanel) {
            this.effectsPanel.toggle();
        }
    }

    setupRealTimeStatus() {
        if (this.game) {
            this.realTimeStatus = new RealTimeStatus(this.game);

            // Add status button to top UI bar
            this.addStatusButton();

            // Add demo button
            this.addDemoButton();
        }
    }

    addStatusButton() {
        const topButtons = document.querySelector('.top-buttons');
        if (topButtons) {
            const statusBtn = document.createElement('button');
            statusBtn.className = 'ui-button';
            statusBtn.innerHTML = '📊 Status';
            statusBtn.onclick = () => this.toggleRealTimeStatus();
            topButtons.appendChild(statusBtn);
        }
    }

    toggleRealTimeStatus() {
        if (this.realTimeStatus) {
            this.realTimeStatus.toggle();
        }
    }

    addDemoButton() {
        const topButtons = document.querySelector('.top-buttons');
        if (topButtons) {
            const demoBtn = document.createElement('button');
            demoBtn.className = 'ui-button demo-button';
            demoBtn.innerHTML = '🎬 Demo';
            demoBtn.onclick = () => this.startRealTimeDemo();
            demoBtn.title = 'Start Real-Time Features Demo';
            topButtons.appendChild(demoBtn);
        }
    }

    startRealTimeDemo() {
        if (this.game && window.RealTimeDemo) {
            if (!this.realTimeDemo) {
                this.realTimeDemo = new RealTimeDemo(this.game);
            }

            this.realTimeDemo.start();

            // Show notification
            if (window.notificationSystem) {
                notificationSystem.info(
                    'Demo Started',
                    'Real-time features demonstration is now running',
                    { duration: 3000 }
                );
            }
        }
    }
    
    setupEventListeners() {
        // Listen for game events to update UI
        this.game.on('gameStarted', () => this.onGameStarted());
        this.game.on('gameStopped', () => this.onGameStopped());
        this.game.on('buildingSelected', (building) => this.showBuildingInfo(building));
        this.game.on('characterSelected', (character) => this.showCharacterInfo(character));
        
        // Currency display updates
        this.game.currencySystem.on('moneyChanged', () => this.updateCurrencyDisplay());
        this.game.currencySystem.on('donutsChanged', () => this.updateCurrencyDisplay());
        this.game.currencySystem.on('xpChanged', () => this.updateCurrencyDisplay());
        this.game.currencySystem.on('levelUp', (event) => this.showLevelUpNotification(event));
    }
    
    showGameUI() {
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            gameContainer.classList.remove('hidden');
            this.isGameUIVisible = true;
        }
    }
    
    hideGameUI() {
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            gameContainer.classList.add('hidden');
            this.isGameUIVisible = false;
        }
        
        // Hide all panels
        this.hideAllPanels();
    }
    
    onGameStarted() {
        this.updateCurrencyDisplay();
        this.updateTownInfo();

        // Initialize effects panel if not already done
        if (!this.effectsPanel && this.game.engine3D) {
            this.setupEffectsPanel();
        }

        // Initialize real-time status panel
        if (!this.realTimeStatus) {
            this.setupRealTimeStatus();
        }
    }
    
    onGameStopped() {
        this.hideAllPanels();
    }
    
    updateCurrencyDisplay() {
        // Money
        const moneyElement = document.getElementById('money-amount');
        if (moneyElement) {
            moneyElement.textContent = this.formatNumber(this.game.currencySystem.getMoney());
        }
        
        // Donuts
        const donutElement = document.getElementById('donut-amount');
        if (donutElement) {
            donutElement.textContent = this.formatNumber(this.game.currencySystem.getDonuts());
        }
        
        // XP
        const xpElement = document.getElementById('xp-amount');
        if (xpElement) {
            xpElement.textContent = this.formatNumber(this.game.currencySystem.getXP());
        }
        
        // Level
        const levelElement = document.getElementById('town-level');
        if (levelElement) {
            levelElement.textContent = this.game.currencySystem.getLevel().toString();
        }
    }
    
    updateTownInfo() {
        const townNameElement = document.getElementById('town-name');
        if (townNameElement) {
            townNameElement.textContent = this.game.getTownName();
        }
    }
    
    showBuildingInfo(building) {
        const panel = document.getElementById('building-info-panel');
        if (!panel) return;

        // Store reference to current building
        this.currentBuilding = building;

        // Get building data from building system
        const buildingData = this.game.buildingSystem.buildingData[building.userData.type];

        // Populate building info
        const nameElement = document.getElementById('building-name');
        const imageElement = document.getElementById('building-image');
        const descriptionElement = document.getElementById('building-description');

        if (nameElement) nameElement.textContent = building.userData.name;
        if (descriptionElement) {
            const income = building.userData.income;
            const incomeText = income ? `Generates $${income.amount} every ${Math.floor(income.interval/60)} minutes` : 'Decorative building';
            descriptionElement.innerHTML = `
                <div>Level ${building.userData.level} building</div>
                <div style="font-size: 0.9em; color: #666; margin-top: 5px;">${incomeText}</div>
                <div style="font-size: 0.8em; color: #888; margin-top: 5px;">Pending: $${building.userData.pendingIncome || 0}</div>
            `;
        }

        // Set up action buttons
        this.setupBuildingButtons(building, buildingData);

        // Show panel
        panel.classList.remove('hidden');
        this.activePanels.add('building-info');

        // Set up close button
        const closeBtn = document.getElementById('close-building-panel');
        if (closeBtn) {
            closeBtn.onclick = () => this.hideBuildingInfo();
        }
    }

    setupBuildingButtons(building, buildingData) {
        const upgradeBtn = document.getElementById('upgrade-building-btn');
        const moveBtn = document.getElementById('move-building-btn');
        const storeBtn = document.getElementById('store-building-btn');

        // Upgrade button
        if (upgradeBtn) {
            const currentLevel = building.userData.level;
            const maxLevel = buildingData?.maxLevel || 5;
            const upgrade = buildingData?.upgrades?.find(u => u.level === currentLevel + 1);

            if (upgrade && currentLevel < maxLevel) {
                upgradeBtn.style.display = 'block';
                upgradeBtn.textContent = `Upgrade ($${upgrade.cost.amount})`;
                upgradeBtn.onclick = () => this.upgradeBuilding(building, upgrade);

                // Check if player can afford upgrade
                const canAfford = this.game.currencySystem.canAfford(upgrade.cost.amount);
                upgradeBtn.disabled = !canAfford;
                upgradeBtn.style.opacity = canAfford ? '1' : '0.5';
            } else {
                upgradeBtn.style.display = 'none';
            }
        }

        // Move button
        if (moveBtn) {
            moveBtn.onclick = () => this.startMoveBuilding(building);
        }

        // Store button (remove building)
        if (storeBtn) {
            storeBtn.onclick = () => this.storeBuilding(building);
        }
    }
    
    upgradeBuilding(building, upgrade) {
        const success = this.game.buildingSystem.upgradeBuilding(building.userData.id);

        if (success) {
            // Show success notification
            this.showUpgradeNotification(building, upgrade);

            // Refresh the building info panel
            this.showBuildingInfo(building);
        } else {
            alert('Cannot upgrade building. Not enough money or max level reached.');
        }
    }

    startMoveBuilding(building) {
        this.hideBuildingInfo();
        this.game.buildingSystem.startMoveMode(building);

        // Show instruction
        this.showMoveInstruction();
    }

    storeBuilding(building) {
        const confirmStore = confirm(`Are you sure you want to store ${building.userData.name}? You'll get 50% of the original cost back.`);

        if (confirmStore) {
            // Calculate refund (50% of original cost)
            const buildingData = this.game.buildingSystem.buildingData[building.userData.type];
            if (buildingData && buildingData.cost.type === 'money') {
                const refund = Math.floor(buildingData.cost.amount * 0.5);
                this.game.currencySystem.addMoney(refund, `store_${building.userData.type}`);
            }

            // Remove building
            this.game.buildingSystem.removeBuilding(building.userData.id);
            this.hideBuildingInfo();

            // Show notification
            this.showStoreNotification(building);
        }
    }

    showUpgradeNotification(building, upgrade) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            font-weight: bold;
            animation: slideInRight 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.5em;">⬆️</span>
                <div>
                    <div>${building.userData.name} upgraded!</div>
                    <div style="font-size: 0.8em; opacity: 0.9;">Now Level ${building.userData.level}</div>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    showMoveInstruction() {
        const instruction = document.createElement('div');
        instruction.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 193, 7, 0.95);
            border: 3px solid #FF6B35;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            color: #8B4513;
            font-weight: bold;
        `;

        instruction.innerHTML = `
            <div style="font-size: 1.2em; margin-bottom: 10px;">🏠 Move Building</div>
            <div>Click on a new location to move the building</div>
            <div style="font-size: 0.9em; margin-top: 10px;">Right-click to cancel</div>
        `;

        document.body.appendChild(instruction);

        // Remove after 4 seconds
        setTimeout(() => {
            if (instruction.parentElement) {
                instruction.remove();
            }
        }, 4000);
    }

    showStoreNotification(building) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            font-weight: bold;
            animation: slideInRight 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.5em;">📦</span>
                <div>
                    <div>${building.userData.name} stored!</div>
                    <div style="font-size: 0.8em; opacity: 0.9;">Refund received</div>
                </div>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    hideBuildingInfo() {
        const panel = document.getElementById('building-info-panel');
        if (panel) {
            panel.classList.add('hidden');
            this.activePanels.delete('building-info');
        }
        this.currentBuilding = null;
    }
    
    showCharacterInfo(character) {
        const panel = document.getElementById('character-panel');
        if (!panel) return;
        
        // Populate character info
        const nameElement = document.getElementById('character-name');
        const statusElement = document.getElementById('character-status');
        
        if (nameElement) nameElement.textContent = character.userData.name;
        if (statusElement) {
            const status = character.userData.currentTask ? 
                `Busy: ${character.userData.currentTask.name}` : 
                'Available';
            statusElement.textContent = status;
        }
        
        // Show panel
        panel.classList.remove('hidden');
        this.activePanels.add('character-info');
        
        // Set up close button
        const closeBtn = document.getElementById('close-character-panel');
        if (closeBtn) {
            closeBtn.onclick = () => this.hideCharacterInfo();
        }
    }
    
    hideCharacterInfo() {
        const panel = document.getElementById('character-panel');
        if (panel) {
            panel.classList.add('hidden');
            this.activePanels.delete('character-info');
        }
    }
    
    showLevelUpNotification(event) {
        // Create level up notification
        const notification = document.createElement('div');
        notification.className = 'level-up-notification';
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border: 3px solid #FF6B35;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            z-index: 1000;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: levelUpPulse 0.5s ease-out;
        `;
        
        notification.innerHTML = `
            <h2 style="color: #8B4513; margin-bottom: 15px;">🎉 Level Up! 🎉</h2>
            <p style="color: #8B4513; font-size: 1.2em; margin-bottom: 15px;">
                Welcome to Level ${event.newLevel}!
            </p>
            <div style="display: flex; justify-content: center; gap: 20px; margin-bottom: 20px;">
                <div style="color: #4CAF50; font-weight: bold;">
                    +${event.rewards.money} 💰
                </div>
                <div style="color: #FF6B35; font-weight: bold;">
                    +${event.rewards.donuts} 🍩
                </div>
            </div>
            <button onclick="this.parentElement.remove()" 
                    style="background: #4CAF50; color: white; border: none; 
                           padding: 10px 20px; border-radius: 15px; cursor: pointer;">
                Awesome!
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    hideAllPanels() {
        this.hideBuildingInfo();
        this.hideCharacterInfo();
        this.activePanels.clear();
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    update(deltaTime) {
        // Update any animated UI elements
    }
    
    dispose() {
        this.hideAllPanels();
        this.removeAllListeners();
    }
}
