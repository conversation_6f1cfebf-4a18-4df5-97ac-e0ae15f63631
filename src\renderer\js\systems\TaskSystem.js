/**
 * Springfield Town Builder - Task System
 * Manages character tasks and quest progression
 */

class TaskSystem extends EventEmitter {
    constructor(characterSystem, currencySystem) {
        super();
        this.characterSystem = characterSystem;
        this.currencySystem = currencySystem;

        this.activeTasks = new Map();
        this.completedTasks = new Set();
        this.availableQuests = [];

        // Real-time tracking
        this.taskUpdateInterval = 1000; // Update every second
        this.lastTaskUpdate = Date.now();
        this.taskQueue = [];
        this.autoAssignTasks = true;
        this.availableTasks = [];

        // Statistics
        this.taskStats = {
            totalCompleted: 0,
            totalRewards: 0,
            averageCompletionTime: 0,
            taskHistory: []
        };

        this.init();
    }

    // Real-time update method
    update(deltaTime) {
        // Update active tasks in real-time
        this.updateActiveTasks(deltaTime);

        // Auto-assign tasks to idle characters
        if (this.autoAssignTasks) {
            this.autoAssignTasksToCharacters();
        }

        // Update task queue
        this.processTaskQueue(deltaTime);

        // Generate new tasks periodically
        this.generatePeriodicTasks(deltaTime);

        // Update task UI
        this.updateTaskUI();
    }

    updateActiveTasks(deltaTime) {
        const now = Date.now();

        for (const [taskId, task] of this.activeTasks) {
            // Update task progress
            if (task.startTime && task.duration) {
                const elapsed = now - task.startTime;
                const progress = Math.min(elapsed / (task.duration * 1000), 1);
                task.progress = progress;

                // Complete task if finished
                if (progress >= 1 && !task.completed) {
                    this.completeTaskById(taskId);
                }

                // Update task visual progress
                this.updateTaskProgress(task, progress);
            }
        }
    }

    autoAssignTasksToCharacters() {
        // Find idle characters
        const idleCharacters = this.getIdleCharacters();

        // Assign tasks to idle characters
        idleCharacters.forEach(character => {
            if (this.availableTasks.length > 0) {
                const suitableTask = this.findSuitableTaskForCharacter(character);
                if (suitableTask) {
                    this.assignTaskToCharacter(suitableTask, character);
                }
            }
        });
    }

    getIdleCharacters() {
        const idleCharacters = [];

        if (this.characterSystem && this.characterSystem.activeCharacters) {
            for (const [id, character] of this.characterSystem.activeCharacters) {
                if (!character.userData.currentTask && !character.userData.isWalking) {
                    idleCharacters.push(character);
                }
            }
        }

        return idleCharacters;
    }

    findSuitableTaskForCharacter(character) {
        // Find tasks that match character's personality or skills
        const personality = character.userData.personality;

        const suitableTasks = this.availableTasks.filter(task => {
            // Check if task matches character personality
            if (task.preferredPersonalities && task.preferredPersonalities.includes(personality)) {
                return true;
            }

            // Check if task has no specific requirements
            if (!task.preferredPersonalities || task.preferredPersonalities.length === 0) {
                return true;
            }

            return false;
        });

        // Return random suitable task
        if (suitableTasks.length > 0) {
            return suitableTasks[Math.floor(Math.random() * suitableTasks.length)];
        }

        // Fallback to any available task
        if (this.availableTasks.length > 0) {
            return this.availableTasks[Math.floor(Math.random() * this.availableTasks.length)];
        }

        return null;
    }

    assignTaskToCharacter(task, character) {
        const taskId = `${character.userData.id}_${task.id}_${Date.now()}`;

        const activeTask = {
            id: taskId,
            taskData: task,
            character: character,
            startTime: Date.now(),
            duration: task.duration || 10, // Default 10 seconds
            progress: 0,
            completed: false
        };

        this.activeTasks.set(taskId, activeTask);
        character.userData.currentTask = activeTask;

        // Remove task from available tasks
        const taskIndex = this.availableTasks.indexOf(task);
        if (taskIndex > -1) {
            this.availableTasks.splice(taskIndex, 1);
        }

        // Start task animation on character
        if (this.characterSystem.startTaskAnimation) {
            this.characterSystem.startTaskAnimation(character, task);
        }

        this.emit('taskAssigned', { task: activeTask, character });
    }

    completeTaskById(taskId) {
        const task = this.activeTasks.get(taskId);
        if (!task) return;

        task.completed = true;
        task.completionTime = Date.now();

        // Calculate rewards
        const rewards = this.calculateTaskRewards(task);

        // Give rewards
        if (rewards.money > 0) {
            this.currencySystem.addMoney(rewards.money, 'task_completion');
        }
        if (rewards.xp > 0) {
            this.currencySystem.addXP(rewards.xp, 'task_completion');
        }
        if (rewards.donuts > 0) {
            this.currencySystem.addDonuts(rewards.donuts, 'task_completion');
        }

        // Update statistics
        this.taskStats.totalCompleted++;
        this.taskStats.totalRewards += rewards.money;
        this.taskStats.taskHistory.push({
            taskId: taskId,
            completionTime: task.completionTime,
            duration: task.completionTime - task.startTime,
            rewards: rewards
        });

        // Keep only last 50 completed tasks in history
        if (this.taskStats.taskHistory.length > 50) {
            this.taskStats.taskHistory.shift();
        }

        // Clear character's current task
        if (task.character) {
            task.character.userData.currentTask = null;

            // Stop task animation
            if (this.characterSystem.stopTaskAnimation) {
                this.characterSystem.stopTaskAnimation(task.character);
            }
        }

        // Move to completed tasks
        this.completedTasks.add(taskId);
        this.activeTasks.delete(taskId);

        // Show completion effects
        this.showTaskCompletionEffects(task, rewards);

        this.emit('taskCompleted', { task, rewards });
    }

    calculateTaskRewards(task) {
        const baseRewards = task.taskData.rewards || { money: 50, xp: 10, donuts: 0 };

        // Apply multipliers based on completion time
        const completionTime = task.completionTime - task.startTime;
        const expectedTime = task.duration * 1000;
        const timeBonus = completionTime <= expectedTime ? 1.2 : 1.0;

        return {
            money: Math.floor(baseRewards.money * timeBonus),
            xp: Math.floor(baseRewards.xp * timeBonus),
            donuts: baseRewards.donuts
        };
    }

    showTaskCompletionEffects(task, rewards) {
        if (task.character) {
            // Show floating text above character
            const character = task.character;
            const rewardText = `+$${rewards.money} +${rewards.xp}XP`;

            if (this.characterSystem.showSpeechBubble) {
                this.characterSystem.showSpeechBubble(character, "Task complete!", 2000);
            }

            // Create floating reward text
            this.createFloatingRewardText(character, rewardText);
        }
    }

    createFloatingRewardText(character, text) {
        // Create 3D floating text above character
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;

        // Draw text
        context.fillStyle = '#4CAF50';
        context.font = 'Bold 20px Arial';
        context.textAlign = 'center';
        context.fillText(text, 128, 40);

        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({
            map: texture,
            transparent: true
        });

        const sprite = new THREE.Sprite(material);
        sprite.scale.set(3, 0.75, 1);
        sprite.position.copy(character.position);
        sprite.position.y += 3;

        // Add to scene
        if (this.characterSystem.game?.engine3D) {
            this.characterSystem.game.engine3D.addBuilding(sprite);

            // Animate floating text
            const startTime = Date.now();
            const duration = 2000;

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = elapsed / duration;

                if (progress < 1) {
                    sprite.position.y = character.position.y + 3 + progress * 2;
                    sprite.material.opacity = 1 - progress;
                    requestAnimationFrame(animate);
                } else {
                    this.characterSystem.game.engine3D.removeBuilding(sprite);
                    texture.dispose();
                    material.dispose();
                }
            };

            animate();
        }
    }

    processTaskQueue(deltaTime) {
        // Process queued tasks
        if (this.taskQueue.length > 0) {
            const now = Date.now();

            this.taskQueue = this.taskQueue.filter(queuedTask => {
                if (now >= queuedTask.executeTime) {
                    this.executeQueuedTask(queuedTask);
                    return false; // Remove from queue
                }
                return true; // Keep in queue
            });
        }
    }

    executeQueuedTask(queuedTask) {
        switch (queuedTask.type) {
            case 'auto_complete':
                this.completeTaskById(queuedTask.taskId);
                break;
            case 'generate_new_task':
                this.generateRandomTask();
                break;
            case 'bonus_reward':
                this.applyBonusReward(queuedTask.reward);
                break;
        }
    }

    generatePeriodicTasks(deltaTime) {
        // Generate new tasks periodically
        if (!this.lastTaskGeneration) {
            this.lastTaskGeneration = Date.now();
        }

        const timeSinceLastGeneration = Date.now() - this.lastTaskGeneration;

        // Generate new task every 30 seconds if we have less than 5 available tasks
        if (timeSinceLastGeneration > 30000 && this.availableTasks.length < 5) {
            this.generateRandomTask();
            this.lastTaskGeneration = Date.now();
        }
    }

    generateRandomTask() {
        const taskTypes = [
            {
                id: 'collect_income',
                name: 'Collect Building Income',
                description: 'Collect money from buildings',
                duration: 5,
                rewards: { money: 100, xp: 15, donuts: 0 },
                preferredPersonalities: ['hardworking', 'lazy']
            },
            {
                id: 'clean_town',
                name: 'Clean the Town',
                description: 'Keep Springfield tidy',
                duration: 8,
                rewards: { money: 75, xp: 20, donuts: 0 },
                preferredPersonalities: ['caring', 'hardworking']
            },
            {
                id: 'entertain_visitors',
                name: 'Entertain Visitors',
                description: 'Make visitors happy',
                duration: 6,
                rewards: { money: 120, xp: 18, donuts: 1 },
                preferredPersonalities: ['mischievous', 'sarcastic']
            },
            {
                id: 'study_books',
                name: 'Study at Library',
                description: 'Gain knowledge and XP',
                duration: 10,
                rewards: { money: 50, xp: 35, donuts: 0 },
                preferredPersonalities: ['intelligent']
            },
            {
                id: 'serve_customers',
                name: 'Serve Customers',
                description: 'Help customers at shops',
                duration: 7,
                rewards: { money: 90, xp: 12, donuts: 0 },
                preferredPersonalities: ['caring', 'hardworking']
            }
        ];

        const randomTask = taskTypes[Math.floor(Math.random() * taskTypes.length)];
        const task = {
            ...randomTask,
            id: `${randomTask.id}_${Date.now()}`,
            createdAt: Date.now()
        };

        this.availableTasks.push(task);
        this.emit('taskGenerated', task);
    }

    updateTaskProgress(task, progress) {
        // Update visual progress indicators
        const progressElement = document.getElementById(`task-progress-${task.id}`);
        if (progressElement) {
            progressElement.style.width = `${progress * 100}%`;
        }

        // Update character task animation intensity
        if (task.character && this.characterSystem.updateTaskAnimation) {
            this.characterSystem.updateTaskAnimation(task.character, task.taskData, progress);
        }
    }

    updateTaskUI() {
        // Update task panel with current tasks
        const taskPanel = document.getElementById('task-list');
        if (taskPanel) {
            taskPanel.innerHTML = '';

            // Show active tasks
            for (const [taskId, task] of this.activeTasks) {
                const taskElement = this.createTaskElement(task, 'active');
                taskPanel.appendChild(taskElement);
            }

            // Show available tasks
            this.availableTasks.forEach(task => {
                const taskElement = this.createTaskElement(task, 'available');
                taskPanel.appendChild(taskElement);
            });
        }

        // Update task statistics
        this.updateTaskStatistics();
    }

    createTaskElement(task, status) {
        const taskElement = document.createElement('div');
        taskElement.className = `task-item task-${status}`;

        const isActive = status === 'active';
        const taskData = isActive ? task.taskData : task;
        const progress = isActive ? task.progress || 0 : 0;

        taskElement.innerHTML = `
            <div class="task-header">
                <span class="task-name">${taskData.name}</span>
                <span class="task-status">${status}</span>
            </div>
            <div class="task-description">${taskData.description}</div>
            <div class="task-rewards">
                💰 ${taskData.rewards.money} | ⭐ ${taskData.rewards.xp}${taskData.rewards.donuts > 0 ? ` | 🍩 ${taskData.rewards.donuts}` : ''}
            </div>
            ${isActive ? `
                <div class="task-progress-bar">
                    <div class="task-progress-fill" id="task-progress-${task.id}" style="width: ${progress * 100}%"></div>
                </div>
                <div class="task-character">👤 ${task.character?.userData?.name || 'Unknown'}</div>
            ` : ''}
        `;

        // Add click handler for available tasks
        if (status === 'available') {
            taskElement.addEventListener('click', () => {
                this.manuallyAssignTask(task);
            });
            taskElement.style.cursor = 'pointer';
        }

        return taskElement;
    }

    manuallyAssignTask(task) {
        const idleCharacters = this.getIdleCharacters();

        if (idleCharacters.length > 0) {
            const character = idleCharacters[0]; // Assign to first idle character
            this.assignTaskToCharacter(task, character);
        } else {
            // Show message that no characters are available
            this.showTaskMessage('No idle characters available!');
        }
    }

    showTaskMessage(message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'task-message';
        messageElement.textContent = message;
        messageElement.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 107, 53, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            z-index: 1000;
        `;

        document.body.appendChild(messageElement);

        setTimeout(() => {
            messageElement.remove();
        }, 2000);
    }

    updateTaskStatistics() {
        const statsElement = document.getElementById('task-statistics');
        if (statsElement) {
            const avgTime = this.taskStats.taskHistory.length > 0
                ? this.taskStats.taskHistory.reduce((sum, task) => sum + task.duration, 0) / this.taskStats.taskHistory.length / 1000
                : 0;

            statsElement.innerHTML = `
                <h4>📋 Task Statistics</h4>
                <div class="stat-item">
                    <span>Completed:</span>
                    <span>${this.taskStats.totalCompleted}</span>
                </div>
                <div class="stat-item">
                    <span>Active:</span>
                    <span>${this.activeTasks.size}</span>
                </div>
                <div class="stat-item">
                    <span>Available:</span>
                    <span>${this.availableTasks.length}</span>
                </div>
                <div class="stat-item">
                    <span>Avg Time:</span>
                    <span>${avgTime.toFixed(1)}s</span>
                </div>
                <div class="stat-item">
                    <span>Total Rewards:</span>
                    <span>$${this.taskStats.totalRewards}</span>
                </div>
            `;
        }
    }

    // Initialize some starting tasks
    initializeStartingTasks() {
        // Generate a few initial tasks
        for (let i = 0; i < 3; i++) {
            this.generateRandomTask();
        }
    }

    init() {
        this.setupEventListeners();
        this.initializeStartingTasks();
        console.log('Task System initialized with real-time updates');
    }
    
    setupEventListeners() {
        this.characterSystem.on('taskCompleted', (event) => {
            this.onTaskCompleted(event);
        });
    }
    
    onTaskCompleted(event) {
        const taskId = `${event.character.userData.id}_${event.task.id}`;
        this.completedTasks.add(taskId);
        this.emit('taskCompleted', event);
    }
    
    getState() {
        return {
            tasks: Array.from(this.completedTasks),
            quests: this.availableQuests
        };
    }
    
    setState(state) {
        if (state.tasks) {
            this.completedTasks = new Set(state.tasks);
        }
        if (state.quests) {
            this.availableQuests = state.quests;
        }
    }
    
    dispose() {
        this.removeAllListeners();
    }
}
