/**
 * Springfield Town Builder - Real-Time Notification System
 * Handles all in-game notifications and alerts
 */

class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 4000; // 4 seconds
        
        this.createNotificationContainer();
        this.setupStyles();
    }
    
    createNotificationContainer() {
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'notification-container';
        
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            pointer-events: none;
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 400px;
            width: 100%;
        `;
        
        document.body.appendChild(this.container);
    }
    
    setupStyles() {
        // Add notification styles to document
        const style = document.createElement('style');
        style.textContent = `
            .notification {
                background: rgba(255, 255, 255, 0.95);
                border-left: 4px solid #4CAF50;
                border-radius: 8px;
                padding: 12px 16px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                backdrop-filter: blur(10px);
                pointer-events: auto;
                cursor: pointer;
                transition: all 0.3s ease;
                animation: notificationSlideIn 0.3s ease-out;
                font-family: 'Comic Sans MS', cursive, sans-serif;
                position: relative;
                overflow: hidden;
            }
            
            .notification:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
            }
            
            .notification.success {
                border-left-color: #4CAF50;
            }
            
            .notification.warning {
                border-left-color: #FF9800;
            }
            
            .notification.error {
                border-left-color: #F44336;
            }
            
            .notification.info {
                border-left-color: #2196F3;
            }
            
            .notification.special {
                border-left-color: #FFD700;
                background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
            }
            
            .notification-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;
            }
            
            .notification-title {
                font-weight: bold;
                font-size: 0.9em;
                color: #333;
            }
            
            .notification-close {
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background-color 0.2s ease;
            }
            
            .notification-close:hover {
                background-color: rgba(0, 0, 0, 0.1);
            }
            
            .notification-content {
                font-size: 0.85em;
                color: #555;
                line-height: 1.4;
            }
            
            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: linear-gradient(90deg, #4CAF50, #8BC34A);
                transition: width 0.1s linear;
            }
            
            .notification.closing {
                animation: notificationSlideOut 0.3s ease-in forwards;
            }
            
            @keyframes notificationSlideIn {
                0% {
                    opacity: 0;
                    transform: translateY(-20px) scale(0.95);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
            
            @keyframes notificationSlideOut {
                0% {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
                100% {
                    opacity: 0;
                    transform: translateY(-20px) scale(0.95);
                }
            }
        `;
        
        document.head.appendChild(style);
    }
    
    show(options) {
        const notification = this.createNotification(options);
        this.addNotification(notification);
        return notification.id;
    }
    
    createNotification(options) {
        const {
            title = 'Notification',
            message = '',
            type = 'info',
            duration = this.defaultDuration,
            icon = '',
            actions = [],
            persistent = false
        } = options;
        
        const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const notificationElement = document.createElement('div');
        notificationElement.className = `notification ${type}`;
        notificationElement.id = id;
        
        const iconHtml = icon ? `<span class="notification-icon">${icon}</span> ` : '';
        
        notificationElement.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">${iconHtml}${title}</div>
                <button class="notification-close" onclick="notificationSystem.dismiss('${id}')">×</button>
            </div>
            <div class="notification-content">${message}</div>
            ${!persistent ? '<div class="notification-progress"></div>' : ''}
        `;
        
        // Add action buttons if provided
        if (actions.length > 0) {
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'notification-actions';
            actionsContainer.style.cssText = `
                margin-top: 8px;
                display: flex;
                gap: 8px;
                justify-content: flex-end;
            `;
            
            actions.forEach(action => {
                const button = document.createElement('button');
                button.textContent = action.text;
                button.className = 'notification-action-btn';
                button.style.cssText = `
                    background: #4CAF50;
                    color: white;
                    border: none;
                    padding: 4px 12px;
                    border-radius: 4px;
                    font-size: 0.8em;
                    cursor: pointer;
                    transition: background-color 0.2s ease;
                `;
                
                button.addEventListener('click', () => {
                    if (action.callback) {
                        action.callback();
                    }
                    this.dismiss(id);
                });
                
                button.addEventListener('mouseenter', () => {
                    button.style.backgroundColor = '#45a049';
                });
                
                button.addEventListener('mouseleave', () => {
                    button.style.backgroundColor = '#4CAF50';
                });
                
                actionsContainer.appendChild(button);
            });
            
            notificationElement.appendChild(actionsContainer);
        }
        
        const notification = {
            id,
            element: notificationElement,
            type,
            duration,
            persistent,
            createdAt: Date.now(),
            progressBar: notificationElement.querySelector('.notification-progress')
        };
        
        // Set up auto-dismiss timer if not persistent
        if (!persistent && duration > 0) {
            this.setupProgressBar(notification);
            notification.timeoutId = setTimeout(() => {
                this.dismiss(id);
            }, duration);
        }
        
        // Click to dismiss
        notificationElement.addEventListener('click', (e) => {
            if (!e.target.classList.contains('notification-close') && 
                !e.target.classList.contains('notification-action-btn')) {
                this.dismiss(id);
            }
        });
        
        return notification;
    }
    
    setupProgressBar(notification) {
        if (!notification.progressBar) return;
        
        const startTime = Date.now();
        const updateProgress = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / notification.duration, 1);
            const remaining = 1 - progress;
            
            notification.progressBar.style.width = `${remaining * 100}%`;
            
            if (progress < 1 && this.notifications.find(n => n.id === notification.id)) {
                requestAnimationFrame(updateProgress);
            }
        };
        
        updateProgress();
    }
    
    addNotification(notification) {
        // Remove oldest notification if at max capacity
        if (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications[0];
            this.dismiss(oldest.id);
        }
        
        this.notifications.push(notification);
        this.container.appendChild(notification.element);
    }
    
    dismiss(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;
        
        // Clear timeout if exists
        if (notification.timeoutId) {
            clearTimeout(notification.timeoutId);
        }
        
        // Add closing animation
        notification.element.classList.add('closing');
        
        // Remove after animation
        setTimeout(() => {
            if (notification.element.parentElement) {
                notification.element.remove();
            }
            
            // Remove from notifications array
            const index = this.notifications.findIndex(n => n.id === id);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }
    
    dismissAll() {
        [...this.notifications].forEach(notification => {
            this.dismiss(notification.id);
        });
    }
    
    // Convenience methods for different notification types
    success(title, message, options = {}) {
        return this.show({
            title,
            message,
            type: 'success',
            icon: '✅',
            ...options
        });
    }
    
    warning(title, message, options = {}) {
        return this.show({
            title,
            message,
            type: 'warning',
            icon: '⚠️',
            ...options
        });
    }
    
    error(title, message, options = {}) {
        return this.show({
            title,
            message,
            type: 'error',
            icon: '❌',
            duration: 6000, // Longer duration for errors
            ...options
        });
    }
    
    info(title, message, options = {}) {
        return this.show({
            title,
            message,
            type: 'info',
            icon: 'ℹ️',
            ...options
        });
    }
    
    special(title, message, options = {}) {
        return this.show({
            title,
            message,
            type: 'special',
            icon: '🎉',
            duration: 5000,
            ...options
        });
    }
    
    // Game-specific notification methods
    levelUp(newLevel, rewards) {
        return this.special(
            'Level Up!',
            `Congratulations! You reached level ${newLevel}!`,
            {
                duration: 6000,
                actions: [
                    {
                        text: 'Collect Rewards',
                        callback: () => {
                            // Handle reward collection
                            console.log('Collecting level up rewards:', rewards);
                        }
                    }
                ]
            }
        );
    }
    
    incomeCollected(amount, building) {
        return this.success(
            'Income Collected',
            `Collected $${amount} from ${building}`,
            { duration: 2000 }
        );
    }
    
    taskCompleted(taskName, rewards) {
        return this.success(
            'Task Completed',
            `"${taskName}" completed! Earned $${rewards.money} and ${rewards.xp} XP`,
            { duration: 3000 }
        );
    }
    
    buildingPlaced(buildingName) {
        return this.info(
            'Building Placed',
            `${buildingName} has been built successfully!`,
            { duration: 2500 }
        );
    }
    
    characterSpawned(characterName) {
        return this.info(
            'New Character',
            `${characterName} has joined your town!`,
            { duration: 3000 }
        );
    }
    
    bonusEvent(eventName, description) {
        return this.special(
            eventName,
            description,
            { duration: 4000 }
        );
    }
}

// Create global notification system instance
const notificationSystem = new NotificationSystem();
