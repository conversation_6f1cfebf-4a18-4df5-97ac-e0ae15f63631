/**
 * Springfield Town Builder - Currency System
 * Handles money, donuts (premium currency), and XP management
 */

class CurrencySystem extends EventEmitter {
    constructor() {
        super();
        
        // Currency amounts
        this.money = 1000;
        this.donuts = 50;
        this.xp = 0;
        this.level = 1;
        
        // XP requirements for leveling up
        this.xpRequirements = this.generateXPRequirements();
        
        // Currency limits
        this.maxMoney = 999999999;
        this.maxDonuts = 999999;
        this.maxXP = 999999999;

        // Real-time tracking
        this.passiveIncomeBuildings = new Map();
        this.incomeRate = 0; // Money per second
        this.lastIncomeUpdate = Date.now();
        this.incomeMultiplier = 1.0;
        this.bonusEvents = [];

        // Statistics
        this.stats = {
            totalEarned: 0,
            totalSpent: 0,
            buildingsOwned: 0,
            charactersHired: 0,
            tasksCompleted: 0,
            playTime: 0,
            incomeHistory: [],
            spendingHistory: []
        };

        this.init();
    }

    // Real-time update method
    update(deltaTime) {
        // Update passive income in real-time
        this.updatePassiveIncome(deltaTime);

        // Update bonus events
        this.updateBonusEvents(deltaTime);

        // Update statistics
        this.updateStatistics(deltaTime);

        // Update income rate calculation
        this.updateIncomeRate();
    }

    updatePassiveIncome(deltaTime) {
        const now = Date.now();
        const timeSinceLastUpdate = now - this.lastIncomeUpdate;

        if (timeSinceLastUpdate > 0) {
            // Calculate total passive income from all buildings
            let totalPassiveIncome = 0;

            for (const [buildingId, incomeData] of this.passiveIncomeBuildings) {
                const incomePerSecond = incomeData.amount / incomeData.interval;
                const incomeGenerated = incomePerSecond * (timeSinceLastUpdate / 1000);
                totalPassiveIncome += incomeGenerated;
            }

            // Apply income multiplier
            totalPassiveIncome *= this.incomeMultiplier;

            if (totalPassiveIncome > 0) {
                this.addMoney(totalPassiveIncome, 'passive_income');
                this.stats.totalEarned += totalPassiveIncome;

                // Add to income history
                this.stats.incomeHistory.push({
                    amount: totalPassiveIncome,
                    timestamp: now,
                    source: 'passive_income'
                });

                // Keep only last 100 entries
                if (this.stats.incomeHistory.length > 100) {
                    this.stats.incomeHistory.shift();
                }
            }

            this.lastIncomeUpdate = now;
        }
    }

    updateBonusEvents(deltaTime) {
        const now = Date.now();

        // Update active bonus events
        this.bonusEvents = this.bonusEvents.filter(event => {
            if (now > event.endTime) {
                // Bonus event expired
                this.onBonusEventExpired(event);
                return false;
            }
            return true;
        });

        // Randomly trigger bonus events
        if (Math.random() < 0.0001) { // Very rare chance per frame
            this.triggerRandomBonusEvent();
        }
    }

    updateStatistics(deltaTime) {
        this.stats.playTime += deltaTime;

        // Update UI with real-time statistics
        this.updateStatisticsUI();
    }

    updateIncomeRate() {
        // Calculate current income rate (money per second)
        let totalRate = 0;

        for (const [buildingId, incomeData] of this.passiveIncomeBuildings) {
            const ratePerSecond = incomeData.amount / incomeData.interval;
            totalRate += ratePerSecond;
        }

        this.incomeRate = totalRate * this.incomeMultiplier;

        // Update income rate display
        this.updateIncomeRateUI();
    }

    // Building income management
    registerBuildingIncome(buildingId, incomeData) {
        this.passiveIncomeBuildings.set(buildingId, incomeData);
        this.stats.buildingsOwned = this.passiveIncomeBuildings.size;
        this.updateIncomeRate();
    }

    unregisterBuildingIncome(buildingId) {
        this.passiveIncomeBuildings.delete(buildingId);
        this.stats.buildingsOwned = this.passiveIncomeBuildings.size;
        this.updateIncomeRate();
    }

    // Bonus events system
    triggerRandomBonusEvent() {
        const bonusTypes = [
            { type: 'income_boost', multiplier: 2.0, duration: 30000, name: 'Double Income!' },
            { type: 'income_boost', multiplier: 1.5, duration: 60000, name: 'Income Boost!' },
            { type: 'free_money', amount: 1000, name: 'Money Rain!' },
            { type: 'free_donuts', amount: 5, name: 'Donut Delivery!' }
        ];

        const bonus = bonusTypes[Math.floor(Math.random() * bonusTypes.length)];
        this.activateBonusEvent(bonus);
    }

    activateBonusEvent(bonusData) {
        const now = Date.now();

        switch (bonusData.type) {
            case 'income_boost':
                const event = {
                    type: bonusData.type,
                    multiplier: bonusData.multiplier,
                    endTime: now + bonusData.duration,
                    name: bonusData.name
                };

                this.bonusEvents.push(event);
                this.recalculateIncomeMultiplier();
                this.showBonusEventNotification(bonusData.name, bonusData.duration);
                break;

            case 'free_money':
                this.addMoney(bonusData.amount, 'bonus_event');
                this.showBonusEventNotification(bonusData.name);
                break;

            case 'free_donuts':
                this.addDonuts(bonusData.amount, 'bonus_event');
                this.showBonusEventNotification(bonusData.name);
                break;
        }
    }

    onBonusEventExpired(event) {
        if (event.type === 'income_boost') {
            this.recalculateIncomeMultiplier();
            this.showBonusEventExpiredNotification(event.name);
        }
    }

    recalculateIncomeMultiplier() {
        this.incomeMultiplier = 1.0;

        for (const event of this.bonusEvents) {
            if (event.type === 'income_boost') {
                this.incomeMultiplier *= event.multiplier;
            }
        }
    }

    showBonusEventNotification(eventName, duration = null) {
        const notification = document.createElement('div');
        notification.className = 'bonus-event-notification';
        notification.innerHTML = `
            <div class="bonus-event-content">
                <h3>🎁 ${eventName}</h3>
                ${duration ? `<p>Duration: ${Math.round(duration / 1000)}s</p>` : ''}
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            font-family: 'Comic Sans MS', cursive, sans-serif;
            font-weight: bold;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    showBonusEventExpiredNotification(eventName) {
        this.createFloatingText(`${eventName} ended`, '#FF6B35');
    }
    
    init() {
        console.log('Currency System initialized');
        this.updateUI();
    }
    
    generateXPRequirements() {
        const requirements = [0]; // Level 1 starts at 0 XP
        
        for (let level = 2; level <= 100; level++) {
            // Exponential growth with some balancing
            const baseXP = 100;
            const multiplier = Math.pow(1.15, level - 1);
            const requirement = Math.floor(baseXP * multiplier);
            requirements.push(requirement);
        }
        
        return requirements;
    }
    
    // Money operations
    addMoney(amount, source = 'unknown') {
        if (amount <= 0) return false;
        
        const oldAmount = this.money;
        this.money = Math.min(this.money + amount, this.maxMoney);
        const actualAmount = this.money - oldAmount;
        
        if (actualAmount > 0) {
            this.emit('moneyChanged', {
                oldAmount,
                newAmount: this.money,
                change: actualAmount,
                source
            });
            
            this.updateUI();
            this.showCurrencyGain('money', actualAmount);
            return true;
        }
        
        return false;
    }
    
    spendMoney(amount, purpose = 'unknown') {
        if (amount <= 0 || this.money < amount) return false;
        
        const oldAmount = this.money;
        this.money -= amount;
        
        this.emit('moneyChanged', {
            oldAmount,
            newAmount: this.money,
            change: -amount,
            purpose
        });
        
        this.updateUI();
        return true;
    }
    
    getMoney() {
        return this.money;
    }
    
    canAfford(amount) {
        return this.money >= amount;
    }
    
    // Donut operations (premium currency)
    addDonuts(amount, source = 'unknown') {
        if (amount <= 0) return false;
        
        const oldAmount = this.donuts;
        this.donuts = Math.min(this.donuts + amount, this.maxDonuts);
        const actualAmount = this.donuts - oldAmount;
        
        if (actualAmount > 0) {
            this.emit('donutsChanged', {
                oldAmount,
                newAmount: this.donuts,
                change: actualAmount,
                source
            });
            
            this.updateUI();
            this.showCurrencyGain('donuts', actualAmount);
            return true;
        }
        
        return false;
    }
    
    spendDonuts(amount, purpose = 'unknown') {
        if (amount <= 0 || this.donuts < amount) return false;
        
        const oldAmount = this.donuts;
        this.donuts -= amount;
        
        this.emit('donutsChanged', {
            oldAmount,
            newAmount: this.donuts,
            change: -amount,
            purpose
        });
        
        this.updateUI();
        return true;
    }
    
    getDonuts() {
        return this.donuts;
    }
    
    canAffordDonuts(amount) {
        return this.donuts >= amount;
    }
    
    // XP and leveling operations
    addXP(amount, source = 'unknown') {
        if (amount <= 0) return false;
        
        const oldXP = this.xp;
        const oldLevel = this.level;
        
        this.xp = Math.min(this.xp + amount, this.maxXP);
        const actualAmount = this.xp - oldXP;
        
        if (actualAmount > 0) {
            // Check for level up
            const newLevel = this.calculateLevel(this.xp);
            
            this.emit('xpChanged', {
                oldXP,
                newXP: this.xp,
                change: actualAmount,
                source
            });
            
            if (newLevel > oldLevel) {
                this.level = newLevel;
                this.onLevelUp(oldLevel, newLevel);
            }
            
            this.updateUI();
            this.showCurrencyGain('xp', actualAmount);
            return true;
        }
        
        return false;
    }
    
    calculateLevel(xp) {
        for (let level = this.xpRequirements.length - 1; level >= 1; level--) {
            if (xp >= this.xpRequirements[level]) {
                return level;
            }
        }
        return 1;
    }
    
    onLevelUp(oldLevel, newLevel) {
        // Level up rewards
        const levelDifference = newLevel - oldLevel;
        const donutReward = levelDifference * 2; // 2 donuts per level
        const moneyReward = levelDifference * 500; // 500 money per level
        
        this.addDonuts(donutReward, 'level_up');
        this.addMoney(moneyReward, 'level_up');
        
        this.emit('levelUp', {
            oldLevel,
            newLevel,
            rewards: {
                donuts: donutReward,
                money: moneyReward
            }
        });
        
        this.showLevelUpNotification(newLevel, {
            donuts: donutReward,
            money: moneyReward
        });
        
        console.log(`Level up! Now level ${newLevel}`);
    }
    
    getXP() {
        return this.xp;
    }
    
    getLevel() {
        return this.level;
    }
    
    getXPForNextLevel() {
        if (this.level >= this.xpRequirements.length - 1) {
            return this.maxXP; // Max level reached
        }
        return this.xpRequirements[this.level + 1];
    }
    
    getXPProgress() {
        const currentLevelXP = this.xpRequirements[this.level];
        const nextLevelXP = this.getXPForNextLevel();
        const progress = (this.xp - currentLevelXP) / (nextLevelXP - currentLevelXP);
        return Math.max(0, Math.min(1, progress));
    }
    
    // Enhanced UI updates with real-time data
    updateUI() {
        // Update money display
        const moneyElement = document.getElementById('money-amount');
        if (moneyElement) {
            moneyElement.textContent = this.formatNumber(this.money);
        }

        // Update donut display
        const donutElement = document.getElementById('donut-amount');
        if (donutElement) {
            donutElement.textContent = this.formatNumber(this.donuts);
        }

        // Update XP display
        const xpElement = document.getElementById('xp-amount');
        if (xpElement) {
            xpElement.textContent = this.formatNumber(this.xp);
        }

        // Update level display
        const levelElement = document.getElementById('town-level');
        if (levelElement) {
            levelElement.textContent = this.level.toString();
        }

        // Update real-time displays
        this.updateIncomeRateUI();
        this.updateStatisticsUI();
        this.updateBonusEventsUI();
    }

    updateIncomeRateUI() {
        // Update income rate display
        const incomeRateElement = document.getElementById('income-rate');
        if (incomeRateElement) {
            incomeRateElement.textContent = `$${this.formatNumber(this.incomeRate * 60)}/min`;
        }

        // Create income rate element if it doesn't exist
        if (!incomeRateElement && this.incomeRate > 0) {
            this.createIncomeRateDisplay();
        }
    }

    createIncomeRateDisplay() {
        const topBar = document.querySelector('.top-bar');
        if (topBar) {
            const incomeRateDiv = document.createElement('div');
            incomeRateDiv.className = 'income-rate-display';
            incomeRateDiv.innerHTML = `
                <span class="income-rate-label">Income:</span>
                <span id="income-rate" class="income-rate-value">$0/min</span>
            `;

            incomeRateDiv.style.cssText = `
                display: flex;
                align-items: center;
                gap: 5px;
                background: rgba(76, 175, 80, 0.1);
                padding: 5px 10px;
                border-radius: 15px;
                border: 2px solid #4CAF50;
                font-size: 0.9em;
                font-weight: bold;
                color: #4CAF50;
            `;

            topBar.appendChild(incomeRateDiv);
        }
    }

    updateStatisticsUI() {
        // Update statistics panel if it exists
        const statsPanel = document.getElementById('statistics-panel');
        if (statsPanel) {
            statsPanel.innerHTML = `
                <h4>📊 Statistics</h4>
                <div class="stat-item">
                    <span>Total Earned:</span>
                    <span>$${this.formatNumber(this.stats.totalEarned)}</span>
                </div>
                <div class="stat-item">
                    <span>Total Spent:</span>
                    <span>$${this.formatNumber(this.stats.totalSpent)}</span>
                </div>
                <div class="stat-item">
                    <span>Buildings:</span>
                    <span>${this.stats.buildingsOwned}</span>
                </div>
                <div class="stat-item">
                    <span>Characters:</span>
                    <span>${this.stats.charactersHired}</span>
                </div>
                <div class="stat-item">
                    <span>Tasks Done:</span>
                    <span>${this.stats.tasksCompleted}</span>
                </div>
                <div class="stat-item">
                    <span>Play Time:</span>
                    <span>${this.formatTime(this.stats.playTime)}</span>
                </div>
            `;
        }
    }

    updateBonusEventsUI() {
        // Update active bonus events display
        const bonusContainer = document.getElementById('active-bonuses');
        if (bonusContainer) {
            bonusContainer.innerHTML = '';

            this.bonusEvents.forEach(event => {
                const bonusElement = document.createElement('div');
                bonusElement.className = 'active-bonus';

                const timeLeft = Math.max(0, event.endTime - Date.now());
                const secondsLeft = Math.ceil(timeLeft / 1000);

                bonusElement.innerHTML = `
                    <span class="bonus-name">${event.name}</span>
                    <span class="bonus-timer">${secondsLeft}s</span>
                `;

                bonusElement.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    background: rgba(255, 107, 53, 0.2);
                    padding: 5px 10px;
                    border-radius: 5px;
                    margin-bottom: 5px;
                    border: 1px solid #FF6B35;
                    font-size: 0.8em;
                `;

                bonusContainer.appendChild(bonusElement);
            });
        } else if (this.bonusEvents.length > 0) {
            this.createBonusEventsDisplay();
        }
    }

    createBonusEventsDisplay() {
        const topBar = document.querySelector('.top-bar');
        if (topBar) {
            const bonusContainer = document.createElement('div');
            bonusContainer.id = 'active-bonuses';
            bonusContainer.className = 'active-bonuses-container';

            bonusContainer.style.cssText = `
                position: fixed;
                top: 60px;
                right: 20px;
                max-width: 200px;
                z-index: 999;
            `;

            document.body.appendChild(bonusContainer);
        }
    }

    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    showCurrencyGain(type, amount) {
        // Create floating text animation for currency gains
        const icons = {
            money: '💰',
            donuts: '🍩',
            xp: '⭐'
        };
        
        const colors = {
            money: '#4CAF50',
            donuts: '#FF6B35',
            xp: '#FFD700'
        };
        
        this.createFloatingText(
            `+${this.formatNumber(amount)} ${icons[type]}`,
            colors[type]
        );
    }
    
    showLevelUpNotification(newLevel, rewards) {
        // Show level up notification
        const notification = document.createElement('div');
        notification.className = 'level-up-notification';
        notification.innerHTML = `
            <div class="level-up-content">
                <h3>🎉 Level Up! 🎉</h3>
                <p>You reached level ${newLevel}!</p>
                <div class="level-up-rewards">
                    <div>+${rewards.money} 💰</div>
                    <div>+${rewards.donuts} 🍩</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 500);
        }, 3000);
    }
    
    createFloatingText(text, color) {
        const floatingText = document.createElement('div');
        floatingText.className = 'floating-currency-text';
        floatingText.textContent = text;
        floatingText.style.color = color;
        floatingText.style.position = 'fixed';
        floatingText.style.top = '50%';
        floatingText.style.left = '50%';
        floatingText.style.transform = 'translate(-50%, -50%)';
        floatingText.style.fontSize = '1.5em';
        floatingText.style.fontWeight = 'bold';
        floatingText.style.pointerEvents = 'none';
        floatingText.style.zIndex = '1000';
        floatingText.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
        
        document.body.appendChild(floatingText);
        
        // Animate upward and fade out
        let opacity = 1;
        let y = 0;
        
        const animate = () => {
            y -= 2;
            opacity -= 0.02;
            
            floatingText.style.transform = `translate(-50%, calc(-50% + ${y}px))`;
            floatingText.style.opacity = opacity;
            
            if (opacity > 0) {
                requestAnimationFrame(animate);
            } else {
                document.body.removeChild(floatingText);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    // Save/Load state
    getState() {
        return {
            money: this.money,
            donuts: this.donuts,
            xp: this.xp,
            level: this.level
        };
    }
    
    setState(state) {
        this.money = state.money || 1000;
        this.donuts = state.donuts || 50;
        this.xp = state.xp || 0;
        this.level = state.level || 1;
        
        this.updateUI();
    }
    
    dispose() {
        this.removeAllListeners();
    }
}
