/**
 * Springfield Town Builder - Effects Panel
 * Controls for visual effects and time of day
 */

class EffectsPanel {
    constructor(engine3D, game = null) {
        this.engine3D = engine3D;
        this.game = game;
        this.isVisible = false;

        this.createPanel();
        this.setupEventListeners();
    }
    
    createPanel() {
        // Create main panel
        this.panel = document.createElement('div');
        this.panel.className = 'effects-panel hidden';
        this.panel.innerHTML = `
            <div class="effects-header">
                <h3>🎨 Visual Effects</h3>
                <button class="close-btn" id="close-effects">×</button>
            </div>
            
            <div class="effects-content">
                <!-- Time Controls -->
                <div class="effects-section">
                    <h4>⏰ Time of Day</h4>
                    <div class="time-display" id="time-display">12:00 PM</div>
                    <div class="control-group">
                        <label>Time Mode:</label>
                        <div class="time-mode-toggle">
                            <button id="system-time-btn" class="time-mode-btn active">🌍 System Time</button>
                            <button id="game-time-btn" class="time-mode-btn">🎮 Game Time</button>
                        </div>
                    </div>
                    <div id="game-time-controls" class="time-controls" style="display: none;">
                        <div class="control-group">
                            <label>Time:</label>
                            <input type="range" id="time-slider" min="0" max="24" step="0.5" value="12">
                            <div class="time-presets">
                                <button class="preset-btn" data-time="6">Dawn</button>
                                <button class="preset-btn" data-time="12">Noon</button>
                                <button class="preset-btn" data-time="18">Dusk</button>
                                <button class="preset-btn" data-time="0">Night</button>
                            </div>
                        </div>
                        <div class="control-group">
                            <label>Speed:</label>
                            <input type="range" id="time-speed" min="0" max="5" step="0.1" value="1">
                            <span id="speed-display">1x</span>
                        </div>
                    </div>
                    <div id="system-time-info" class="system-time-info">
                        <div class="time-info">
                            <span id="current-date">Loading...</span>
                        </div>
                        <div class="period-info">
                            <span id="current-period">Loading...</span>
                        </div>
                    </div>
                </div>
                

                <!-- Post-Processing Effects -->
                <div class="effects-section">
                    <h4>✨ Visual Effects</h4>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="post-processing" checked>
                            Enable Post-Processing
                        </label>
                    </div>
                    <div class="control-group">
                        <label>Bloom:</label>
                        <input type="range" id="bloom-strength" min="0" max="2" step="0.1" value="0.6">
                        <span id="bloom-display">60%</span>
                    </div>
                    <div class="control-group">
                        <label>Vignette:</label>
                        <input type="range" id="vignette-strength" min="0" max="1" step="0.05" value="0.2">
                        <span id="vignette-display">20%</span>
                    </div>
                    <div class="control-group">
                        <label>Saturation:</label>
                        <input type="range" id="saturation" min="0.5" max="2" step="0.05" value="1.15">
                        <span id="saturation-display">115%</span>
                    </div>
                </div>
                
                <!-- Camera Effects -->
                <div class="effects-section">
                    <h4>📷 Camera</h4>
                    <div class="camera-buttons">
                        <button class="effect-btn" id="shake-camera">📳 Shake</button>
                        <button class="effect-btn" id="auto-rotate">🔄 Auto Rotate</button>
                        <button class="effect-btn" id="reset-camera">🎯 Reset View</button>
                    </div>
                </div>

                <!-- Music Controls -->
                <div class="effects-section">
                    <h4>🎵 Music</h4>
                    <div class="music-controls">
                        <div class="music-playback">
                            <button id="music-play-pause" class="music-btn">▶️ Play</button>
                            <button id="music-stop" class="music-btn">⏹️ Stop</button>
                            <button id="music-next" class="music-btn">⏭️ Next</button>
                        </div>
                        <div class="music-info">
                            <div id="current-track-name">No track playing</div>
                            <div id="current-track-time">--:-- / --:--</div>
                        </div>
                        <div class="control-group">
                            <label>Music Volume:</label>
                            <input type="range" id="music-volume" min="0" max="1" step="0.1" value="0.8">
                            <span id="music-volume-display">80%</span>
                        </div>
                        <div class="music-settings">
                            <label><input type="checkbox" id="music-autoplay" checked> Auto-play</label>
                            <label><input type="checkbox" id="music-time-based" checked> Time-based</label>
                            <label><input type="checkbox" id="music-repeat" checked> Repeat</label>
                        </div>
                    </div>
                </div>
                
                <!-- Presets -->
                <div class="effects-section">
                    <h4>🎭 Presets</h4>
                    <div class="preset-buttons">
                        <button class="preset-btn" data-preset="default">Default</button>
                        <button class="preset-btn" data-preset="cinematic">Cinematic</button>
                        <button class="preset-btn" data-preset="vibrant">Vibrant</button>
                        <button class="preset-btn" data-preset="moody">Moody</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.panel);
    }
    
    setupEventListeners() {
        // Close button
        document.getElementById('close-effects').addEventListener('click', () => {
            this.hide();
        });
        
        // Time mode controls
        const systemTimeBtn = document.getElementById('system-time-btn');
        const gameTimeBtn = document.getElementById('game-time-btn');
        const gameTimeControls = document.getElementById('game-time-controls');
        const systemTimeInfo = document.getElementById('system-time-info');
        const timeDisplay = document.getElementById('time-display');

        // Time mode toggle
        systemTimeBtn.addEventListener('click', () => {
            this.setTimeMode(true);
        });

        gameTimeBtn.addEventListener('click', () => {
            this.setTimeMode(false);
        });

        // Game time controls (only active in game time mode)
        const timeSlider = document.getElementById('time-slider');
        const timeSpeed = document.getElementById('time-speed');
        const speedDisplay = document.getElementById('speed-display');

        timeSlider.addEventListener('input', (e) => {
            if (!this.engine3D.isUsingSystemTime()) {
                const time = parseFloat(e.target.value);
                this.engine3D.setTimeOfDay(time);
                this.updateTimeDisplay();
            }
        });

        timeSpeed.addEventListener('input', (e) => {
            const speed = parseFloat(e.target.value);
            this.engine3D.setTimeSpeed(speed);
            speedDisplay.textContent = `${speed}x`;
        });

        // Time presets
        document.querySelectorAll('[data-time]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (!this.engine3D.isUsingSystemTime()) {
                    const time = parseFloat(e.target.dataset.time);
                    this.engine3D.skipToTime(time);
                    timeSlider.value = time;
                    this.updateTimeDisplay();
                }
            });
        });
        

        
        // Post-processing controls
        document.getElementById('post-processing').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.engine3D.enablePostProcessing();
            } else {
                this.engine3D.disablePostProcessing();
            }
        });
        
        this.setupSliderControl('bloom-strength', 'bloom-display', (value) => {
            this.engine3D.setBloomStrength(value);
        }, '%', 100);
        
        this.setupSliderControl('vignette-strength', 'vignette-display', (value) => {
            this.engine3D.postProcessing?.setVignetteStrength(value);
        }, '%', 100);
        
        this.setupSliderControl('saturation', 'saturation-display', (value) => {
            this.engine3D.postProcessing?.setSaturation(value);
        }, '%', 100);
        
        // Camera effects
        document.getElementById('shake-camera').addEventListener('click', () => {
            this.engine3D.shakeCamera(2, 1000);
        });
        
        document.getElementById('auto-rotate').addEventListener('click', (e) => {
            const controller = this.engine3D.cameraController;
            if (controller.autoRotate) {
                controller.disableAutoRotate();
                e.target.textContent = '🔄 Auto Rotate';
            } else {
                controller.enableAutoRotate(0.5);
                e.target.textContent = '⏸️ Stop Rotate';
            }
        });
        
        document.getElementById('reset-camera').addEventListener('click', () => {
            this.engine3D.cameraController.reset();
        });
        
        // Presets
        document.querySelectorAll('[data-preset]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.applyPreset(e.target.dataset.preset);
            });
        });

        // Music controls
        this.setupMusicControls();
        
        // Update time display periodically
        setInterval(() => {
            this.updateTimeDisplay();
        }, 1000);
    }
    
    setupSliderControl(sliderId, displayId, callback, suffix = '', multiplier = 1) {
        const slider = document.getElementById(sliderId);
        const display = document.getElementById(displayId);
        
        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            display.textContent = `${Math.round(value * multiplier)}${suffix}`;
            callback(value);
        });
    }
    
    setTimeMode(useSystemTime) {
        const systemTimeBtn = document.getElementById('system-time-btn');
        const gameTimeBtn = document.getElementById('game-time-btn');
        const gameTimeControls = document.getElementById('game-time-controls');
        const systemTimeInfo = document.getElementById('system-time-info');

        // Update button states
        systemTimeBtn.classList.toggle('active', useSystemTime);
        gameTimeBtn.classList.toggle('active', !useSystemTime);

        // Show/hide appropriate controls
        gameTimeControls.style.display = useSystemTime ? 'none' : 'block';
        systemTimeInfo.style.display = useSystemTime ? 'block' : 'none';

        // Update engine
        this.engine3D.setUseSystemTime(useSystemTime);

        // Update display
        this.updateTimeDisplay();

        // Show notification
        if (window.notificationSystem) {
            const mode = useSystemTime ? 'System Time' : 'Game Time';
            notificationSystem.info(
                'Time Mode Changed',
                `Switched to ${mode} mode`,
                { duration: 2000 }
            );
        }
    }

    updateTimeDisplay() {
        const timeDisplay = document.getElementById('time-display');
        const currentDate = document.getElementById('current-date');
        const currentPeriod = document.getElementById('current-period');

        if (timeDisplay && this.engine3D) {
            const timeInfo = this.engine3D.getDetailedTimeInfo();

            // Update main time display
            timeDisplay.textContent = timeInfo.timeString;

            // Update system time info if in system time mode
            if (timeInfo.isSystemTime && currentDate && currentPeriod) {
                currentDate.textContent = `${timeInfo.systemDay}, ${timeInfo.systemDate}`;
                currentPeriod.textContent = `${timeInfo.period.charAt(0).toUpperCase() + timeInfo.period.slice(1)} Time`;
            }
        }
    }
    
    applyPreset(presetName) {
        const presets = {
            default: {
                bloom: 0.6,
                vignette: 0.2,
                saturation: 1.15,
                time: 12
            },
            cinematic: {
                bloom: 1.2,
                vignette: 0.4,
                saturation: 1.3,
                time: 18
            },
            vibrant: {
                bloom: 0.8,
                vignette: 0.1,
                saturation: 1.5,
                time: 12
            },
            moody: {
                bloom: 1.0,
                vignette: 0.5,
                saturation: 0.8,
                time: 20
            }
        };
        
        const preset = presets[presetName];
        if (!preset) return;
        
        // Apply preset values
        this.engine3D.setBloomStrength(preset.bloom);
        this.engine3D.postProcessing?.setVignetteStrength(preset.vignette);
        this.engine3D.postProcessing?.setSaturation(preset.saturation);
        this.engine3D.setWeather(preset.weather);
        this.engine3D.skipToTime(preset.time);
        
        // Update UI controls
        document.getElementById('bloom-strength').value = preset.bloom;
        document.getElementById('vignette-strength').value = preset.vignette;
        document.getElementById('saturation').value = preset.saturation;
        document.getElementById('time-slider').value = preset.time;
        
        // Update displays
        document.getElementById('bloom-display').textContent = `${Math.round(preset.bloom * 100)}%`;
        document.getElementById('vignette-display').textContent = `${Math.round(preset.vignette * 100)}%`;
        document.getElementById('saturation-display').textContent = `${Math.round(preset.saturation * 100)}%`;
        
        // Update weather button
        document.querySelectorAll('.weather-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.weather === preset.weather);
        });
        
        this.updateTimeDisplay();
    }
    
    show() {
        this.isVisible = true;
        this.panel.classList.remove('hidden');
        this.updateTimeDisplay();
    }
    
    hide() {
        this.isVisible = false;
        this.panel.classList.add('hidden');
    }
    
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    setupMusicControls() {
        const musicSystem = this.game?.musicSystem;
        if (!musicSystem) {
            console.log('Music system not available');
            return;
        }

        // Play/Pause button
        const playPauseBtn = document.getElementById('music-play-pause');
        playPauseBtn.addEventListener('click', () => {
            const trackInfo = musicSystem.getCurrentTrackInfo();
            if (trackInfo && trackInfo.isPlaying && !trackInfo.isPaused) {
                musicSystem.pauseTrack();
                playPauseBtn.textContent = '▶️ Play';
            } else if (trackInfo && trackInfo.isPaused) {
                musicSystem.resumeTrack();
                playPauseBtn.textContent = '⏸️ Pause';
            } else {
                // Start playing first available track
                const tracks = musicSystem.getAvailableTracks();
                if (tracks.length > 0) {
                    musicSystem.playTrack(tracks[0].id);
                    playPauseBtn.textContent = '⏸️ Pause';
                }
            }
        });

        // Stop button
        document.getElementById('music-stop').addEventListener('click', () => {
            musicSystem.stopTrack();
            playPauseBtn.textContent = '▶️ Play';
        });

        // Next track button
        document.getElementById('music-next').addEventListener('click', () => {
            musicSystem.nextTrack();
        });

        // Volume control
        const volumeSlider = document.getElementById('music-volume');
        const volumeDisplay = document.getElementById('music-volume-display');

        volumeSlider.addEventListener('input', (e) => {
            const volume = parseFloat(e.target.value);
            musicSystem.setMusicVolume(volume);
            volumeDisplay.textContent = `${Math.round(volume * 100)}%`;
        });

        // Settings checkboxes
        document.getElementById('music-autoplay').addEventListener('change', (e) => {
            musicSystem.autoPlay = e.target.checked;
        });

        document.getElementById('music-time-based').addEventListener('change', (e) => {
            musicSystem.toggleTimeBasedMusic();
        });

        document.getElementById('music-repeat').addEventListener('change', (e) => {
            musicSystem.repeat = e.target.checked;
        });

        // Music system event listeners
        musicSystem.on('trackStarted', (data) => {
            this.updateMusicDisplay(data.track);
            playPauseBtn.textContent = '⏸️ Pause';
        });

        musicSystem.on('trackStopped', () => {
            this.updateMusicDisplay(null);
            playPauseBtn.textContent = '▶️ Play';
        });

        musicSystem.on('trackPaused', () => {
            playPauseBtn.textContent = '▶️ Play';
        });

        musicSystem.on('trackResumed', () => {
            playPauseBtn.textContent = '⏸️ Pause';
        });

        // Update display periodically
        setInterval(() => {
            const trackInfo = musicSystem.getCurrentTrackInfo();
            if (trackInfo && trackInfo.isPlaying) {
                this.updateMusicTime(trackInfo);
            }
        }, 1000);

        // Initial display update
        this.updateMusicDisplay(null);
    }

    updateMusicDisplay(track) {
        const trackNameElement = document.getElementById('current-track-name');

        if (track) {
            trackNameElement.textContent = track.name;
        } else {
            trackNameElement.textContent = 'No track playing';
        }
    }

    updateMusicTime(trackInfo) {
        const timeElement = document.getElementById('current-track-time');

        if (trackInfo && trackInfo.duration) {
            const current = this.formatTime(trackInfo.currentTime);
            const total = this.formatTime(trackInfo.duration);
            timeElement.textContent = `${current} / ${total}`;
        } else {
            timeElement.textContent = '--:-- / --:--';
        }
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
}
