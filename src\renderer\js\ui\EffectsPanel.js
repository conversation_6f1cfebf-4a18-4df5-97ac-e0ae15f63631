/**
 * Springfield Town Builder - Effects Panel
 * Controls for visual effects and time of day
 */

class EffectsPanel {
    constructor(engine3D) {
        this.engine3D = engine3D;
        this.isVisible = false;
        
        this.createPanel();
        this.setupEventListeners();
    }
    
    createPanel() {
        // Create main panel
        this.panel = document.createElement('div');
        this.panel.className = 'effects-panel hidden';
        this.panel.innerHTML = `
            <div class="effects-header">
                <h3>🎨 Visual Effects</h3>
                <button class="close-btn" id="close-effects">×</button>
            </div>
            
            <div class="effects-content">
                <!-- Time Controls -->
                <div class="effects-section">
                    <h4>⏰ Time of Day</h4>
                    <div class="time-display" id="time-display">12:00 PM</div>
                    <div class="control-group">
                        <label>Time:</label>
                        <input type="range" id="time-slider" min="0" max="24" step="0.5" value="12">
                        <div class="time-presets">
                            <button class="preset-btn" data-time="6">Dawn</button>
                            <button class="preset-btn" data-time="12">Noon</button>
                            <button class="preset-btn" data-time="18">Dusk</button>
                            <button class="preset-btn" data-time="0">Night</button>
                        </div>
                    </div>
                    <div class="control-group">
                        <label>Speed:</label>
                        <input type="range" id="time-speed" min="0" max="5" step="0.1" value="1">
                        <span id="speed-display">1x</span>
                    </div>
                </div>
                

                <!-- Post-Processing Effects -->
                <div class="effects-section">
                    <h4>✨ Visual Effects</h4>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="post-processing" checked>
                            Enable Post-Processing
                        </label>
                    </div>
                    <div class="control-group">
                        <label>Bloom:</label>
                        <input type="range" id="bloom-strength" min="0" max="2" step="0.1" value="0.6">
                        <span id="bloom-display">60%</span>
                    </div>
                    <div class="control-group">
                        <label>Vignette:</label>
                        <input type="range" id="vignette-strength" min="0" max="1" step="0.05" value="0.2">
                        <span id="vignette-display">20%</span>
                    </div>
                    <div class="control-group">
                        <label>Saturation:</label>
                        <input type="range" id="saturation" min="0.5" max="2" step="0.05" value="1.15">
                        <span id="saturation-display">115%</span>
                    </div>
                </div>
                
                <!-- Camera Effects -->
                <div class="effects-section">
                    <h4>📷 Camera</h4>
                    <div class="camera-buttons">
                        <button class="effect-btn" id="shake-camera">📳 Shake</button>
                        <button class="effect-btn" id="auto-rotate">🔄 Auto Rotate</button>
                        <button class="effect-btn" id="reset-camera">🎯 Reset View</button>
                    </div>
                </div>
                
                <!-- Presets -->
                <div class="effects-section">
                    <h4>🎭 Presets</h4>
                    <div class="preset-buttons">
                        <button class="preset-btn" data-preset="default">Default</button>
                        <button class="preset-btn" data-preset="cinematic">Cinematic</button>
                        <button class="preset-btn" data-preset="vibrant">Vibrant</button>
                        <button class="preset-btn" data-preset="moody">Moody</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.panel);
    }
    
    setupEventListeners() {
        // Close button
        document.getElementById('close-effects').addEventListener('click', () => {
            this.hide();
        });
        
        // Time controls
        const timeSlider = document.getElementById('time-slider');
        const timeDisplay = document.getElementById('time-display');
        const timeSpeed = document.getElementById('time-speed');
        const speedDisplay = document.getElementById('speed-display');
        
        timeSlider.addEventListener('input', (e) => {
            const time = parseFloat(e.target.value);
            this.engine3D.setTimeOfDay(time);
            this.updateTimeDisplay();
        });
        
        timeSpeed.addEventListener('input', (e) => {
            const speed = parseFloat(e.target.value);
            this.engine3D.setTimeSpeed(speed);
            speedDisplay.textContent = `${speed}x`;
        });
        
        // Time presets
        document.querySelectorAll('[data-time]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const time = parseFloat(e.target.dataset.time);
                this.engine3D.skipToTime(time);
                timeSlider.value = time;
                this.updateTimeDisplay();
            });
        });
        

        
        // Post-processing controls
        document.getElementById('post-processing').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.engine3D.enablePostProcessing();
            } else {
                this.engine3D.disablePostProcessing();
            }
        });
        
        this.setupSliderControl('bloom-strength', 'bloom-display', (value) => {
            this.engine3D.setBloomStrength(value);
        }, '%', 100);
        
        this.setupSliderControl('vignette-strength', 'vignette-display', (value) => {
            this.engine3D.postProcessing?.setVignetteStrength(value);
        }, '%', 100);
        
        this.setupSliderControl('saturation', 'saturation-display', (value) => {
            this.engine3D.postProcessing?.setSaturation(value);
        }, '%', 100);
        
        // Camera effects
        document.getElementById('shake-camera').addEventListener('click', () => {
            this.engine3D.shakeCamera(2, 1000);
        });
        
        document.getElementById('auto-rotate').addEventListener('click', (e) => {
            const controller = this.engine3D.cameraController;
            if (controller.autoRotate) {
                controller.disableAutoRotate();
                e.target.textContent = '🔄 Auto Rotate';
            } else {
                controller.enableAutoRotate(0.5);
                e.target.textContent = '⏸️ Stop Rotate';
            }
        });
        
        document.getElementById('reset-camera').addEventListener('click', () => {
            this.engine3D.cameraController.reset();
        });
        
        // Presets
        document.querySelectorAll('[data-preset]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.applyPreset(e.target.dataset.preset);
            });
        });
        
        // Update time display periodically
        setInterval(() => {
            this.updateTimeDisplay();
        }, 1000);
    }
    
    setupSliderControl(sliderId, displayId, callback, suffix = '', multiplier = 1) {
        const slider = document.getElementById(sliderId);
        const display = document.getElementById(displayId);
        
        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            display.textContent = `${Math.round(value * multiplier)}${suffix}`;
            callback(value);
        });
    }
    
    updateTimeDisplay() {
        const timeString = this.engine3D.getTimeString();
        document.getElementById('time-display').textContent = timeString;
    }
    
    applyPreset(presetName) {
        const presets = {
            default: {
                bloom: 0.6,
                vignette: 0.2,
                saturation: 1.15,
                time: 12
            },
            cinematic: {
                bloom: 1.2,
                vignette: 0.4,
                saturation: 1.3,
                time: 18
            },
            vibrant: {
                bloom: 0.8,
                vignette: 0.1,
                saturation: 1.5,
                time: 12
            },
            moody: {
                bloom: 1.0,
                vignette: 0.5,
                saturation: 0.8,
                time: 20
            }
        };
        
        const preset = presets[presetName];
        if (!preset) return;
        
        // Apply preset values
        this.engine3D.setBloomStrength(preset.bloom);
        this.engine3D.postProcessing?.setVignetteStrength(preset.vignette);
        this.engine3D.postProcessing?.setSaturation(preset.saturation);
        this.engine3D.setWeather(preset.weather);
        this.engine3D.skipToTime(preset.time);
        
        // Update UI controls
        document.getElementById('bloom-strength').value = preset.bloom;
        document.getElementById('vignette-strength').value = preset.vignette;
        document.getElementById('saturation').value = preset.saturation;
        document.getElementById('time-slider').value = preset.time;
        
        // Update displays
        document.getElementById('bloom-display').textContent = `${Math.round(preset.bloom * 100)}%`;
        document.getElementById('vignette-display').textContent = `${Math.round(preset.vignette * 100)}%`;
        document.getElementById('saturation-display').textContent = `${Math.round(preset.saturation * 100)}%`;
        
        // Update weather button
        document.querySelectorAll('.weather-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.weather === preset.weather);
        });
        
        this.updateTimeDisplay();
    }
    
    show() {
        this.isVisible = true;
        this.panel.classList.remove('hidden');
        this.updateTimeDisplay();
    }
    
    hide() {
        this.isVisible = false;
        this.panel.classList.add('hidden');
    }
    
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
}
