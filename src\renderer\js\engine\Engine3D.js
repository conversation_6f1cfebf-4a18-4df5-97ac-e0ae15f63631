/**
 * Springfield Town Builder - 3D Engine
 * Handles Three.js setup and core 3D functionality
 */

class Engine3D extends EventEmitter {
    constructor(canvas) {
        super();
        this.canvas = canvas;
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.clock = new THREE.Clock();
        this.isRunning = false;
        
        // Raycasting for object selection
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        // Object groups
        this.buildings = new THREE.Group();
        this.characters = new THREE.Group();
        this.terrain = new THREE.Group();
        this.decorations = new THREE.Group();
        
        this.init();
    }
    
    init() {
        this.setupRenderer();
        this.setupScene();
        this.setupCamera();
        this.setupLighting();
        this.setupTerrain();
        this.setupEventListeners();
        
        console.log('3D Engine initialized');
    }
    
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,
            alpha: true
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        
        // Add fog for depth
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
        
        // Add object groups to scene
        this.scene.add(this.buildings);
        this.scene.add(this.characters);
        this.scene.add(this.terrain);
        this.scene.add(this.decorations);
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            45,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );

        // Position camera for top-down view
        this.camera.position.set(0, 50, 0);
        this.camera.lookAt(0, 0, 0);

        // Slightly angle the camera for better depth perception
        this.camera.position.set(0, 45, 5);
        this.camera.lookAt(0, 0, 0);
    }
    
    setupLighting() {
        // Enhanced lighting setup for better visual quality

        // Ambient light for overall illumination
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        this.scene.add(ambientLight);

        // Main directional light (sun) from above for top-down view
        const sunLight = new THREE.DirectionalLight(0xffffff, 0.8);
        sunLight.position.set(0, 100, 10);
        sunLight.castShadow = true;

        // Enhanced shadow properties for better quality
        sunLight.shadow.mapSize.width = 4096;
        sunLight.shadow.mapSize.height = 4096;
        sunLight.shadow.camera.near = 0.5;
        sunLight.shadow.camera.far = 200;
        sunLight.shadow.camera.left = -80;
        sunLight.shadow.camera.right = 80;
        sunLight.shadow.camera.top = 80;
        sunLight.shadow.camera.bottom = -80;
        sunLight.shadow.bias = -0.0001;

        this.scene.add(sunLight);

        // Warm fill light for atmosphere
        const fillLight = new THREE.DirectionalLight(0xffa500, 0.3);
        fillLight.position.set(20, 50, -20);
        this.scene.add(fillLight);

        // Cool rim light for depth
        const rimLight = new THREE.DirectionalLight(0x87ceeb, 0.2);
        rimLight.position.set(-20, 30, 20);
        this.scene.add(rimLight);

        // Hemisphere light for natural sky lighting
        const hemisphereLight = new THREE.HemisphereLight(0x87ceeb, 0x90ee90, 0.3);
        this.scene.add(hemisphereLight);

        // Store lights for potential day/night cycle
        this.lights = {
            sun: sunLight,
            fill: fillLight,
            rim: rimLight,
            hemisphere: hemisphereLight,
            ambient: ambientLight
        };
    }
    
    setupTerrain() {
        // Create enhanced ground with texture-like appearance
        const groundGeometry = new THREE.PlaneGeometry(120, 120, 30, 30);

        // Create a more realistic grass material
        const groundMaterial = new THREE.MeshPhongMaterial({
            color: 0x90EE90,
            shininess: 5,
            transparent: false
        });

        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        ground.name = 'ground';

        // Add subtle height variation to the ground
        const vertices = ground.geometry.attributes.position.array;
        for (let i = 2; i < vertices.length; i += 3) {
            vertices[i] += (Math.random() - 0.5) * 0.1; // Small height variations
        }
        ground.geometry.attributes.position.needsUpdate = true;
        ground.geometry.computeVertexNormals();

        this.terrain.add(ground);

        // Add enhanced terrain features
        this.addTerrainFeatures();
        this.addEnvironmentalDetails();
    }
    
    addTerrainFeatures() {
        // Enhanced road system with better materials
        const roadMaterial = new THREE.MeshPhongMaterial({
            color: 0x404040,
            shininess: 10
        });

        // Main road (horizontal) - wider and more detailed
        const mainRoadGeometry = new THREE.PlaneGeometry(3, 60);
        const mainRoad = new THREE.Mesh(mainRoadGeometry, roadMaterial);
        mainRoad.rotation.x = -Math.PI / 2;
        mainRoad.position.y = 0.01;
        this.terrain.add(mainRoad);

        // Cross road (vertical)
        const crossRoadGeometry = new THREE.PlaneGeometry(3, 60);
        const crossRoad = new THREE.Mesh(crossRoadGeometry, roadMaterial);
        crossRoad.rotation.x = -Math.PI / 2;
        crossRoad.rotation.z = Math.PI / 2;
        crossRoad.position.y = 0.01;
        this.terrain.add(crossRoad);

        // Add road markings
        this.addRoadMarkings();

        // Add sidewalks
        this.addSidewalks();
    }

    addRoadMarkings() {
        const markingMaterial = new THREE.MeshBasicMaterial({
            color: 0xFFFFFF,
            transparent: true,
            opacity: 0.8
        });

        // Center line for main road
        const centerLineGeometry = new THREE.PlaneGeometry(0.2, 50);
        const centerLine = new THREE.Mesh(centerLineGeometry, markingMaterial);
        centerLine.rotation.x = -Math.PI / 2;
        centerLine.position.y = 0.02;
        this.terrain.add(centerLine);

        // Center line for cross road
        const crossCenterLine = new THREE.Mesh(centerLineGeometry, markingMaterial);
        crossCenterLine.rotation.x = -Math.PI / 2;
        crossCenterLine.rotation.z = Math.PI / 2;
        crossCenterLine.position.y = 0.02;
        this.terrain.add(crossCenterLine);
    }

    addSidewalks() {
        const sidewalkMaterial = new THREE.MeshPhongMaterial({
            color: 0xC0C0C0,
            shininess: 15
        });

        // Sidewalks along main road
        for (let side = -1; side <= 1; side += 2) {
            const sidewalkGeometry = new THREE.PlaneGeometry(1, 60);
            const sidewalk = new THREE.Mesh(sidewalkGeometry, sidewalkMaterial);
            sidewalk.rotation.x = -Math.PI / 2;
            sidewalk.position.set(side * 2.5, 0.005, 0);
            this.terrain.add(sidewalk);
        }

        // Sidewalks along cross road
        for (let side = -1; side <= 1; side += 2) {
            const sidewalkGeometry = new THREE.PlaneGeometry(1, 60);
            const sidewalk = new THREE.Mesh(sidewalkGeometry, sidewalkMaterial);
            sidewalk.rotation.x = -Math.PI / 2;
            sidewalk.rotation.z = Math.PI / 2;
            sidewalk.position.set(0, 0.005, side * 2.5);
            this.terrain.add(sidewalk);
        }
    }

    addEnvironmentalDetails() {
        // Add trees around the area
        this.addTrees();

        // Add street lamps
        this.addStreetLamps();

        // Add some decorative elements
        this.addDecorations();
    }

    addTrees() {
        const treePositions = [
            { x: -15, z: -15 }, { x: 15, z: -15 },
            { x: -15, z: 15 }, { x: 15, z: 15 },
            { x: -25, z: 0 }, { x: 25, z: 0 },
            { x: 0, z: -25 }, { x: 0, z: 25 }
        ];

        treePositions.forEach(pos => {
            const tree = this.createTree();
            tree.position.set(pos.x, 0, pos.z);
            this.decorations.add(tree);
        });
    }

    createTree() {
        const treeGroup = new THREE.Group();

        // Tree trunk
        const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.4, 3);
        const trunkMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 1.5;
        trunk.castShadow = true;
        treeGroup.add(trunk);

        // Tree foliage
        const foliageGeometry = new THREE.SphereGeometry(2, 12, 8);
        const foliageMaterial = new THREE.MeshPhongMaterial({ color: 0x228B22 });
        const foliage = new THREE.Mesh(foliageGeometry, foliageMaterial);
        foliage.position.y = 4;
        foliage.castShadow = true;
        treeGroup.add(foliage);

        return treeGroup;
    }

    addStreetLamps() {
        const lampPositions = [
            { x: -4, z: -10 }, { x: 4, z: -10 },
            { x: -4, z: 10 }, { x: 4, z: 10 },
            { x: -10, z: -4 }, { x: -10, z: 4 },
            { x: 10, z: -4 }, { x: 10, z: 4 }
        ];

        lampPositions.forEach(pos => {
            const lamp = this.createStreetLamp();
            lamp.position.set(pos.x, 0, pos.z);
            this.decorations.add(lamp);
        });
    }

    createStreetLamp() {
        const lampGroup = new THREE.Group();

        // Lamp post
        const postGeometry = new THREE.CylinderGeometry(0.1, 0.1, 4);
        const postMaterial = new THREE.MeshPhongMaterial({ color: 0x404040 });
        const post = new THREE.Mesh(postGeometry, postMaterial);
        post.position.y = 2;
        post.castShadow = true;
        lampGroup.add(post);

        // Lamp head
        const headGeometry = new THREE.SphereGeometry(0.3, 8, 6);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFFFAA,
            emissive: 0x222200
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 4.2;
        lampGroup.add(head);

        return lampGroup;
    }

    addDecorations() {
        // Add some benches
        const benchPositions = [
            { x: -6, z: -6 }, { x: 6, z: 6 },
            { x: -6, z: 6 }, { x: 6, z: -6 }
        ];

        benchPositions.forEach(pos => {
            const bench = this.createBench();
            bench.position.set(pos.x, 0, pos.z);
            this.decorations.add(bench);
        });
    }

    createBench() {
        const benchGroup = new THREE.Group();

        // Bench seat
        const seatGeometry = new THREE.BoxGeometry(2, 0.2, 0.6);
        const benchMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 });
        const seat = new THREE.Mesh(seatGeometry, benchMaterial);
        seat.position.y = 0.5;
        seat.castShadow = true;
        benchGroup.add(seat);

        // Bench back
        const backGeometry = new THREE.BoxGeometry(2, 0.8, 0.1);
        const back = new THREE.Mesh(backGeometry, benchMaterial);
        back.position.set(0, 0.9, -0.25);
        back.castShadow = true;
        benchGroup.add(back);

        // Bench legs
        for (let i = 0; i < 4; i++) {
            const legGeometry = new THREE.BoxGeometry(0.1, 0.5, 0.1);
            const leg = new THREE.Mesh(legGeometry, benchMaterial);
            const x = (i % 2) * 1.8 - 0.9;
            const z = Math.floor(i / 2) * 0.4 - 0.2;
            leg.position.set(x, 0.25, z);
            leg.castShadow = true;
            benchGroup.add(leg);
        }

        return benchGroup;
    }
    
    setupEventListeners() {
        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Handle mouse events for object interaction
        this.canvas.addEventListener('click', (event) => this.onMouseClick(event));
        this.canvas.addEventListener('mousemove', (event) => this.onMouseMove(event));
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    onMouseClick(event) {
        this.updateMousePosition(event);
        
        // Perform raycasting
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        // Check for intersections with buildings and characters
        const buildingIntersects = this.raycaster.intersectObjects(this.buildings.children, true);
        const characterIntersects = this.raycaster.intersectObjects(this.characters.children, true);
        
        if (buildingIntersects.length > 0) {
            const building = this.findTopLevelObject(buildingIntersects[0].object);
            this.emit('buildingClicked', building);
        } else if (characterIntersects.length > 0) {
            const character = this.findTopLevelObject(characterIntersects[0].object);
            this.emit('characterClicked', character);
        } else {
            // Check ground for building placement
            const groundIntersects = this.raycaster.intersectObjects(this.terrain.children);
            if (groundIntersects.length > 0) {
                const position = groundIntersects[0].point;
                this.emit('groundClicked', position);
            }
        }
    }
    
    onMouseMove(event) {
        this.updateMousePosition(event);
        
        // Handle hover effects
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects([
            ...this.buildings.children,
            ...this.characters.children
        ], true);
        
        // Reset all hover states
        this.clearHoverEffects();
        
        if (intersects.length > 0) {
            const object = this.findTopLevelObject(intersects[0].object);
            this.setHoverEffect(object);
            this.canvas.style.cursor = 'pointer';
        } else {
            this.canvas.style.cursor = 'grab';
        }
    }
    
    updateMousePosition(event) {
        const rect = this.canvas.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }
    
    findTopLevelObject(object) {
        // Find the top-level object in the hierarchy
        while (object.parent && 
               object.parent !== this.buildings && 
               object.parent !== this.characters &&
               object.parent !== this.decorations) {
            object = object.parent;
        }
        return object;
    }
    
    clearHoverEffects() {
        // Remove hover effects from all objects
        this.buildings.children.forEach(building => {
            if (building.userData.originalMaterial) {
                building.traverse(child => {
                    if (child.isMesh) {
                        child.material = building.userData.originalMaterial;
                    }
                });
            }
        });
    }
    
    setHoverEffect(object) {
        // Add hover effect to object
        if (!object.userData.originalMaterial) {
            object.traverse(child => {
                if (child.isMesh) {
                    object.userData.originalMaterial = child.material;
                }
            });
        }
        
        object.traverse(child => {
            if (child.isMesh) {
                child.material = child.material.clone();
                child.material.emissive.setHex(0x444444);
            }
        });
    }
    
    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.animate();
        }
    }
    
    stop() {
        this.isRunning = false;
    }
    
    animate() {
        if (!this.isRunning) return;
        
        requestAnimationFrame(() => this.animate());
        
        const deltaTime = this.clock.getDelta();
        
        // Update animations and systems
        this.emit('update', deltaTime);
        
        // Render the scene
        this.renderer.render(this.scene, this.camera);
    }
    
    // Utility methods for adding objects
    addBuilding(building) {
        this.buildings.add(building);
    }
    
    removeBuilding(building) {
        this.buildings.remove(building);
    }
    
    addCharacter(character) {
        this.characters.add(character);
    }
    
    removeCharacter(character) {
        this.characters.remove(character);
    }
    
    addDecoration(decoration) {
        this.decorations.add(decoration);
    }
    
    removeDecoration(decoration) {
        this.decorations.remove(decoration);
    }
    
    getCamera() {
        return this.camera;
    }
    
    getScene() {
        return this.scene;
    }
    
    getRenderer() {
        return this.renderer;
    }
}
