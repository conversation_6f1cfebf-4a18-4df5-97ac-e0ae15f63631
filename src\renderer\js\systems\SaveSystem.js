/**
 * Springfield Town Builder - Save System
 * Handles save slots, game state persistence, and loading
 */

class SaveSystem extends EventEmitter {
    constructor() {
        super();
        this.currentSlot = null;
        this.saveData = null;
        this.autoSaveInterval = null;
        this.autoSaveEnabled = true;
        this.autoSaveDelay = 30000; // 30 seconds
        
        this.init();
    }
    
    init() {
        console.log('Save System initialized');
        this.setupAutoSave();
    }
    
    setupAutoSave() {
        if (this.autoSaveEnabled) {
            this.autoSaveInterval = setInterval(() => {
                if (this.currentSlot !== null) {
                    this.autoSave();
                }
            }, this.autoSaveDelay);
        }
    }
    
    async getSaveSlots() {
        try {
            console.log('Getting save slots...');
            console.log('electronAPI available:', !!window.electronAPI);

            let result;
            if (window.electronAPI) {
                console.log('Using Electron API for save slots');
                result = await window.electronAPI.invoke('get-save-slots');
            } else {
                console.log('Using localStorage for save slots');
                result = await this.getLocalSaveSlots();
            }

            console.log('Save slots result:', result);

            if (result.success) {
                return result.slots;
            } else {
                console.error('Failed to get save slots:', result.error);
                return [];
            }
        } catch (error) {
            console.error('Error getting save slots:', error);
            return [];
        }
    }
    
    async getLocalSaveSlots() {
        // Fallback for browser-based saves using localStorage
        const slots = [];
        
        for (let i = 1; i <= 5; i++) {
            const saveKey = `springfield_save_slot_${i}`;
            const saveData = localStorage.getItem(saveKey);
            
            if (saveData) {
                try {
                    const data = JSON.parse(saveData);
                    slots.push({
                        slot: i,
                        exists: true,
                        lastModified: new Date(data.lastSaved),
                        townName: data.townName || `Springfield ${i}`,
                        level: data.level || 1,
                        donuts: data.donuts || 0
                    });
                } catch (error) {
                    console.error(`Error parsing save slot ${i}:`, error);
                    slots.push({ slot: i, exists: false });
                }
            } else {
                slots.push({ slot: i, exists: false });
            }
        }
        
        return { success: true, slots };
    }
    
    async saveGame(slotNumber, gameState) {
        try {
            const saveData = this.createSaveData(gameState);
            
            const result = await window.electronAPI?.invoke('save-game', saveData, slotNumber) ||
                          await this.saveLocal(saveData, slotNumber);
            
            if (result.success) {
                this.currentSlot = slotNumber;
                this.saveData = saveData;
                this.emit('gameSaved', { slot: slotNumber, data: saveData });
                console.log(`Game saved to slot ${slotNumber}`);
                return true;
            } else {
                console.error('Failed to save game:', result.error);
                this.emit('saveError', result.error);
                return false;
            }
        } catch (error) {
            console.error('Error saving game:', error);
            this.emit('saveError', error.message);
            return false;
        }
    }
    
    async saveLocal(saveData, slotNumber) {
        try {
            const saveKey = `springfield_save_slot_${slotNumber}`;
            localStorage.setItem(saveKey, JSON.stringify(saveData));
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    async loadGame(slotNumber) {
        try {
            const result = await window.electronAPI?.invoke('load-game', slotNumber) ||
                          await this.loadLocal(slotNumber);
            
            if (result.success) {
                this.currentSlot = slotNumber;
                this.saveData = result.data;
                this.emit('gameLoaded', { slot: slotNumber, data: result.data });
                console.log(`Game loaded from slot ${slotNumber}`);
                return result.data;
            } else {
                console.error('Failed to load game:', result.error);
                this.emit('loadError', result.error);
                return null;
            }
        } catch (error) {
            console.error('Error loading game:', error);
            this.emit('loadError', error.message);
            return null;
        }
    }
    
    async loadLocal(slotNumber) {
        try {
            const saveKey = `springfield_save_slot_${slotNumber}`;
            const saveData = localStorage.getItem(saveKey);
            
            if (saveData) {
                const data = JSON.parse(saveData);
                return { success: true, data };
            } else {
                return { success: false, error: 'Save file not found' };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    createSaveData(gameState) {
        const saveData = {
            version: '1.0.0',
            lastSaved: new Date().toISOString(),
            townName: gameState.townName || 'Springfield',
            level: gameState.level || 1,
            
            // Currency
            money: gameState.money || 1000,
            donuts: gameState.donuts || 50,
            xp: gameState.xp || 0,
            
            // Buildings
            buildings: gameState.buildings || [],
            
            // Characters
            characters: gameState.characters || [],
            
            // Tasks
            tasks: gameState.tasks || [],
            
            // Settings
            settings: gameState.settings || {
                soundEnabled: true,
                musicEnabled: true,
                notifications: true
            },
            
            // Statistics
            stats: gameState.stats || {
                timePlayed: 0,
                buildingsBuilt: 0,
                tasksCompleted: 0,
                donutsSpent: 0
            },
            
            // Multiplayer
            friends: gameState.friends || [],
            
            // Progress
            unlockedBuildings: gameState.unlockedBuildings || [],
            unlockedCharacters: gameState.unlockedCharacters || [],
            completedQuests: gameState.completedQuests || []
        };
        
        return saveData;
    }
    
    async autoSave() {
        if (this.currentSlot !== null && window.game) {
            const gameState = window.game.getGameState();
            await this.saveGame(this.currentSlot, gameState);
            console.log('Auto-save completed');
        }
    }
    
    async quickSave() {
        if (this.currentSlot !== null && window.game) {
            const gameState = window.game.getGameState();
            return await this.saveGame(this.currentSlot, gameState);
        }
        return false;
    }
    
    async newGame(slotNumber, townName = 'Springfield') {
        const defaultGameState = this.createDefaultGameState(townName);
        const success = await this.saveGame(slotNumber, defaultGameState);
        
        if (success) {
            this.emit('newGameCreated', { slot: slotNumber, data: this.saveData });
            return this.saveData;
        }
        
        return null;
    }
    
    createDefaultGameState(townName) {
        return {
            townName: townName,
            level: 1,
            money: 500, // Reduced starting money for empty town challenge
            donuts: 10,  // Reduced starting donuts
            xp: 0,

            buildings: [], // Start with completely empty town
            characters: [], // No starting characters
            tasks: [],

            settings: {
                soundEnabled: true,
                musicEnabled: true,
                notifications: true
            },

            stats: {
                timePlayed: 0,
                buildingsBuilt: 0, // Start with 0 buildings built
                tasksCompleted: 0,
                donutsSpent: 0
            },

            friends: [],

            unlockedBuildings: ['simpson_house'], // Only Simpson House unlocked initially
            unlockedCharacters: ['homer'], // Homer available but not placed
            completedQuests: []
        };
    }
    
    getCurrentSlot() {
        return this.currentSlot;
    }
    
    getCurrentSaveData() {
        return this.saveData;
    }
    
    setAutoSaveEnabled(enabled) {
        this.autoSaveEnabled = enabled;
        
        if (enabled) {
            this.setupAutoSave();
        } else if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
    }
    
    setAutoSaveDelay(delay) {
        this.autoSaveDelay = delay;
        
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.setupAutoSave();
        }
    }
    
    dispose() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
        this.removeAllListeners();
    }
}
