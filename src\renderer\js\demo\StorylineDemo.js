/**
 * Springfield Town Builder - Storyline Demo
 * Demonstrates the quest system, character unlocks, and building progression
 */

class StorylineDemo {
    constructor(game) {
        this.game = game;
        this.isRunning = false;
        this.demoSteps = [];
        this.currentStep = 0;
        this.stepInterval = 4000; // 4 seconds between steps
        
        this.setupDemoSteps();
    }
    
    setupDemoSteps() {
        this.demoSteps = [
            {
                name: 'Welcome to Springfield Story',
                description: 'Experience the complete Springfield storyline with quests and unlocks',
                action: () => {
                    notificationSystem.special(
                        'Empty Land Story Demo!',
                        '<PERSON> <PERSON> transform empty green land into a thriving Springfield community from absolutely nothing!',
                        { duration: 6000 }
                    );

                    // Show quest panel
                    if (this.game.uiManager?.questPanel) {
                        this.game.uiManager.questPanel.show();
                    }
                }
            },
            {
                name: 'Starting Quest',
                description: '<PERSON> begins his journey with the first quest',
                action: () => {
                    // The quest system should already have started the first quest
                    // Let's check and show progress
                    this.showQuestProgress();
                }
            },
            {
                name: 'Collect Income',
                description: 'Demonstrating income collection to complete first objective',
                action: () => {
                    // Simulate collecting income from <PERSON> House
                    this.simulateIncomeCollection();
                }
            },
            {
                name: 'Build Simpson House',
                description: '<PERSON> builds his house to complete the first quest',
                action: () => {
                    // Build the Simpson House
                    this.buildSimpsonsHouse();
                }
            },
            {
                name: 'First Quest Complete',
                description: 'Completing the welcome quest and unlocking characters',
                action: () => {
                    // Force complete the first quest if not already done
                    this.completeFirstQuest();
                }
            },
            {
                name: 'Character Unlock',
                description: 'Homer and Marge Simpson are now available!',
                action: () => {
                    // Spawn Homer first (he should be unlocked)
                    this.spawnUnlockedCharacter('homer');

                    // Then spawn Marge if unlocked
                    setTimeout(() => {
                        this.spawnUnlockedCharacter('marge');
                    }, 1000);
                }
            },
            {
                name: 'Family Tasks',
                description: 'Completing family tasks to progress the story',
                action: () => {
                    // Generate and complete some family tasks
                    this.simulateFamilyTasks();
                }
            },
            {
                name: 'Building Roads',
                description: 'Creating street infrastructure for Springfield',
                action: () => {
                    // Build some roads to connect buildings
                    this.buildRoadNetwork();
                }
            },
            {
                name: 'Building Unlock',
                description: 'Unlocking the Kwik-E-Mart for business',
                action: () => {
                    // Show building unlock and place it
                    this.unlockAndPlaceBuilding('kwik_e_mart');
                }
            },
            {
                name: 'Business Character',
                description: 'Apu joins Springfield to run the store',
                action: () => {
                    // Spawn Apu near the Kwik-E-Mart
                    this.spawnUnlockedCharacter('apu');
                }
            },
            {
                name: 'Social Hub Quest',
                description: 'Building Moe\'s Tavern for community gathering',
                action: () => {
                    // Unlock and place Moe's Tavern
                    this.unlockAndPlaceBuilding('moes_tavern');
                }
            },
            {
                name: 'Community Growth',
                description: 'More characters join the growing community',
                action: () => {
                    // Spawn multiple characters
                    this.spawnUnlockedCharacter('moe');
                    setTimeout(() => {
                        this.spawnUnlockedCharacter('barney');
                    }, 1000);
                }
            },
            {
                name: 'Power Plant Quest',
                description: 'Springfield needs power - building the Nuclear Plant',
                action: () => {
                    // Unlock power plant
                    this.unlockAndPlaceBuilding('power_plant');
                }
            },
            {
                name: 'Mr. Burns Arrives',
                description: 'The wealthy Mr. Burns takes control of the power plant',
                action: () => {
                    // Spawn Mr. Burns
                    this.spawnUnlockedCharacter('mr_burns');
                }
            },
            {
                name: 'Advanced Features',
                description: 'Unlocking advanced game features',
                action: () => {
                    // Show feature unlocks
                    this.demonstrateFeatureUnlocks();
                }
            },
            {
                name: 'Story Complete',
                description: 'Springfield is thriving with a complete community!',
                action: () => {
                    this.completeStoryDemo();
                }
            }
        ];
    }
    
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.currentStep = 0;
        
        console.log('Starting Storyline Demo...');
        this.runNextStep();
    }
    
    stop() {
        this.isRunning = false;
        
        if (this.stepTimeout) {
            clearTimeout(this.stepTimeout);
        }
        
        notificationSystem.info('Demo Stopped', 'Storyline demo has been stopped');
    }
    
    runNextStep() {
        if (!this.isRunning || this.currentStep >= this.demoSteps.length) {
            this.stop();
            return;
        }
        
        const step = this.demoSteps[this.currentStep];
        
        console.log(`Storyline Demo Step ${this.currentStep + 1}: ${step.name}`);
        
        // Show step notification
        notificationSystem.info(
            `Story Step ${this.currentStep + 1}`,
            step.description,
            { duration: this.stepInterval - 500 }
        );
        
        // Execute step action
        try {
            step.action();
        } catch (error) {
            console.error('Storyline demo step error:', error);
        }
        
        this.currentStep++;
        
        // Schedule next step
        this.stepTimeout = setTimeout(() => {
            this.runNextStep();
        }, this.stepInterval);
    }
    
    showQuestProgress() {
        if (this.game.questSystem) {
            const activeQuests = Array.from(this.game.questSystem.activeQuests.values());
            
            if (activeQuests.length > 0) {
                const quest = activeQuests[0];
                notificationSystem.info(
                    'Active Quest',
                    `"${quest.name}" - ${quest.description}`,
                    { duration: 3000 }
                );
            }
        }
    }
    
    simulateIncomeCollection() {
        // Find Simpson House and simulate income collection
        if (this.game.buildingSystem) {
            for (const [id, building] of this.game.buildingSystem.placedBuildings) {
                if (building.userData.type === 'simpsons_house') {
                    // Add some pending income
                    building.userData.pendingIncome = 100;
                    
                    // Trigger collection
                    setTimeout(() => {
                        this.game.buildingSystem.collectIncome(building);
                    }, 1000);
                    break;
                }
            }
        }
    }
    
    buildSimpsonsHouse() {
        // Build the Simpson House at the center of town
        if (this.game.buildingSystem && this.game.questSystem) {
            // Check if Simpson House is unlocked
            if (this.game.questSystem.isUnlocked('building', 'simpson_house')) {
                const position = new THREE.Vector3(0, 0, 0); // Center of town

                // Force place the building
                const buildingData = this.game.buildingSystem.buildingData['simpson_house'];
                if (buildingData) {
                    const building = this.game.buildingSystem.createBuilding(buildingData, position);
                    this.game.buildingSystem.placedBuildings.set(building.userData.id, building);

                    notificationSystem.success(
                        'Simpson House Built!',
                        'Homer now has a place to call home!',
                        { duration: 3000 }
                    );

                    // This should trigger the quest objective completion
                    this.game.questSystem.updateQuestProgress('build_building', 'simpson_house');
                }
            }
        }
    }

    completeFirstQuest() {
        if (this.game.questSystem) {
            // Force complete the welcome quest
            const welcomeQuest = this.game.questSystem.activeQuests.get('welcome_to_springfield');
            if (welcomeQuest) {
                // Set all objectives as complete
                welcomeQuest.objectives.forEach(obj => {
                    if (typeof obj.target === 'number') {
                        obj.current = obj.target;
                    } else {
                        obj.current = 1;
                    }
                });

                // Complete the quest
                this.game.questSystem.completeQuest('welcome_to_springfield');
            }
        }
    }
    
    spawnUnlockedCharacter(characterType) {
        if (this.game.characterSystem && this.game.questSystem) {
            // Check if character is unlocked
            if (this.game.questSystem.isUnlocked('character', characterType)) {
                // Find a good spawn position
                const spawnPosition = this.findGoodSpawnPosition();
                const character = this.game.characterSystem.createCharacter(characterType, spawnPosition);
                
                if (character) {
                    notificationSystem.success(
                        'Character Unlocked!',
                        `${character.userData.name} has joined Springfield!`,
                        { duration: 3000 }
                    );
                }
            }
        }
    }
    
    unlockAndPlaceBuilding(buildingType) {
        if (this.game.buildingSystem && this.game.questSystem) {
            // Check if building is unlocked
            if (this.game.questSystem.isUnlocked('building', buildingType)) {
                // Find a good placement position
                const position = this.findGoodBuildingPosition();
                
                // Force place the building (bypass normal placement mode)
                const buildingData = this.game.buildingSystem.buildingData[buildingType];
                if (buildingData) {
                    const building = this.game.buildingSystem.createBuilding(buildingData, position);
                    this.game.buildingSystem.placedBuildings.set(building.userData.id, building);
                    
                    notificationSystem.success(
                        'Building Unlocked!',
                        `${buildingData.name} has been built!`,
                        { duration: 3000 }
                    );
                    
                    // Award XP
                    this.game.currencySystem.addXP(buildingData.xpReward || 50, `demo_${buildingType}`);
                }
            }
        }
    }
    
    simulateFamilyTasks() {
        // Generate some family-related tasks and complete them
        if (this.game.taskSystem) {
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    this.game.taskSystem.generateRandomTask();

                    // Auto-complete the task after a short delay
                    setTimeout(() => {
                        const activeTasks = Array.from(this.game.taskSystem.activeTasks.values());
                        if (activeTasks.length > 0) {
                            const task = activeTasks[0];
                            this.game.taskSystem.completeTaskById(task.id);
                        }
                    }, 1500);
                }, i * 1000);
            }
        }
    }

    buildRoadNetwork() {
        // Build a basic road network to connect buildings
        if (this.game.buildingSystem && this.game.questSystem) {
            const roadPositions = [
                { type: 'road_straight', position: new THREE.Vector3(2, 0, 0) },
                { type: 'road_straight', position: new THREE.Vector3(4, 0, 0) },
                { type: 'road_corner', position: new THREE.Vector3(6, 0, 0) },
                { type: 'road_straight', position: new THREE.Vector3(6, 0, 2) },
                { type: 'road_t_junction', position: new THREE.Vector3(0, 0, 2) }
            ];

            roadPositions.forEach((roadInfo, index) => {
                setTimeout(() => {
                    if (this.game.questSystem.isUnlocked('building', roadInfo.type)) {
                        const buildingData = this.game.buildingSystem.buildingData[roadInfo.type];
                        if (buildingData) {
                            const road = this.game.buildingSystem.createBuilding(buildingData, roadInfo.position);
                            this.game.buildingSystem.placedBuildings.set(road.userData.id, road);

                            // Update quest progress for road building
                            this.game.questSystem.updateQuestProgress('build_building', roadInfo.type);
                        }
                    }
                }, index * 500);
            });

            // Show notification after roads are built
            setTimeout(() => {
                notificationSystem.success(
                    'Road Network Complete!',
                    'Springfield now has proper streets connecting all buildings!',
                    { duration: 3000 }
                );
            }, roadPositions.length * 500 + 1000);
        }
    }
    
    demonstrateFeatureUnlocks() {
        if (this.game.questSystem) {
            const unlockedFeatures = Array.from(this.game.questSystem.unlockedFeatures);
            
            notificationSystem.special(
                'Features Unlocked!',
                `Available: ${unlockedFeatures.join(', ').replace(/_/g, ' ')}`,
                { duration: 4000 }
            );
        }
    }
    
    completeStoryDemo() {
        notificationSystem.special(
            'Springfield Story Complete!',
            'You\'ve experienced the full storyline progression system. Continue playing to discover more quests and unlock additional content!',
            {
                duration: 6000,
                actions: [
                    {
                        text: 'View Quests',
                        callback: () => {
                            if (this.game.uiManager?.questPanel) {
                                this.game.uiManager.questPanel.show();
                            }
                        }
                    },
                    {
                        text: 'Restart Demo',
                        callback: () => this.restart()
                    }
                ]
            }
        );
        
        // Show final statistics
        this.showFinalStatistics();
    }
    
    showFinalStatistics() {
        if (this.game.questSystem) {
            const stats = {
                completedQuests: this.game.questSystem.completedQuests.size,
                unlockedBuildings: this.game.questSystem.unlockedBuildings.size,
                unlockedCharacters: this.game.questSystem.unlockedCharacters.size,
                currentChapter: this.game.questSystem.currentStoryChapter,
                storyProgress: this.game.questSystem.storyProgress
            };
            
            setTimeout(() => {
                notificationSystem.info(
                    'Story Statistics',
                    `Quests: ${stats.completedQuests} | Buildings: ${stats.unlockedBuildings} | Characters: ${stats.unlockedCharacters} | Chapter: ${stats.currentChapter}`,
                    { duration: 5000 }
                );
            }, 2000);
        }
    }
    
    findGoodSpawnPosition() {
        // Find a position near existing buildings but not overlapping
        const basePositions = [
            new THREE.Vector3(2, 0, 2),
            new THREE.Vector3(-2, 0, 2),
            new THREE.Vector3(2, 0, -2),
            new THREE.Vector3(-2, 0, -2),
            new THREE.Vector3(4, 0, 0),
            new THREE.Vector3(-4, 0, 0),
            new THREE.Vector3(0, 0, 4),
            new THREE.Vector3(0, 0, -4)
        ];
        
        // Return a random position from the available ones
        return basePositions[Math.floor(Math.random() * basePositions.length)];
    }
    
    findGoodBuildingPosition() {
        // Find positions for buildings that don't overlap
        const buildingPositions = [
            new THREE.Vector3(6, 0, 0),
            new THREE.Vector3(-6, 0, 0),
            new THREE.Vector3(0, 0, 6),
            new THREE.Vector3(0, 0, -6),
            new THREE.Vector3(6, 0, 6),
            new THREE.Vector3(-6, 0, 6),
            new THREE.Vector3(6, 0, -6),
            new THREE.Vector3(-6, 0, -6)
        ];
        
        // Find the first available position
        for (const position of buildingPositions) {
            let occupied = false;
            
            if (this.game.buildingSystem) {
                for (const [id, building] of this.game.buildingSystem.placedBuildings) {
                    if (building.position.distanceTo(position) < 4) {
                        occupied = true;
                        break;
                    }
                }
            }
            
            if (!occupied) {
                return position;
            }
        }
        
        // Fallback to a random position
        return buildingPositions[Math.floor(Math.random() * buildingPositions.length)];
    }
    
    restart() {
        this.stop();
        
        // Reset quest system to initial state
        if (this.game.questSystem) {
            // Clear current progress (for demo purposes)
            this.game.questSystem.activeQuests.clear();
            this.game.questSystem.completedQuests.clear();
            this.game.questSystem.currentStoryChapter = 1;
            this.game.questSystem.storyProgress = 0;
            
            // Restart with initial quest
            this.game.questSystem.initializeStartingQuests();
        }
        
        // Restart demo
        setTimeout(() => {
            this.start();
        }, 1000);
    }
    
    // Quick demo for testing
    quickDemo() {
        this.stepInterval = 2000; // Faster demo
        this.start();
    }
    
    // Skip to specific demo step
    skipToStep(stepIndex) {
        if (stepIndex >= 0 && stepIndex < this.demoSteps.length) {
            this.currentStep = stepIndex;
            this.runNextStep();
        }
    }
}

// Make demo available globally for console access
window.StorylineDemo = StorylineDemo;
