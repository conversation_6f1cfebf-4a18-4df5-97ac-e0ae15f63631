/**
 * Springfield Town Builder - Main Entry Point
 * Initializes the game and handles the loading sequence
 */

class GameLoader {
    constructor() {
        this.loadingProgress = 0;
        this.loadingSteps = [
            'Loading Three.js...',
            'Initializing 3D Engine...',
            'Loading game data...',
            'Setting up systems...',
            'Preparing Springfield...',
            'Ready to play!'
        ];
        this.currentStep = 0;
        
        this.game = null;
        this.isLoaded = false;
    }
    
    async init() {
        console.log('Starting Springfield Town Builder...');
        
        try {
            await this.showLoadingScreen();
            await this.loadDependencies();
            await this.initializeGame();
            await this.setupUI();
            
            this.isLoaded = true;
            await this.showMainMenu();
            
            console.log('Game loaded successfully!');
        } catch (error) {
            console.error('Failed to load game:', error);
            this.showErrorScreen(error);
        }
    }
    
    async showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const gameContainer = document.getElementById('game-container');
        const mainMenu = document.getElementById('main-menu');
        
        // Show loading screen
        loadingScreen.classList.remove('hidden');
        gameContainer.classList.add('hidden');
        mainMenu.classList.add('hidden');
        
        this.updateLoadingProgress(0, this.loadingSteps[0]);
    }
    
    updateLoadingProgress(progress, text) {
        const progressBar = document.getElementById('loading-progress');
        const loadingText = document.getElementById('loading-text');
        
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
        
        if (loadingText) {
            loadingText.textContent = text;
        }
        
        this.loadingProgress = progress;
    }
    
    async loadDependencies() {
        this.updateLoadingProgress(10, this.loadingSteps[0]);
        
        // Check if Three.js is loaded
        if (typeof THREE === 'undefined') {
            throw new Error('Three.js not loaded. Please check your dependencies.');
        }
        
        this.updateLoadingProgress(20, this.loadingSteps[1]);
        
        // Add a small delay to show loading progress
        await this.delay(500);
    }
    
    async initializeGame() {
        this.updateLoadingProgress(30, this.loadingSteps[2]);

        // Create game instance
        this.game = new Game();

        // Wait for game initialization
        await new Promise((resolve, reject) => {
            this.game.once('gameInitialized', resolve);
            this.game.once('gameInitializationFailed', reject);
        });

        this.updateLoadingProgress(60, this.loadingSteps[3]);
        await this.delay(500);

        this.updateLoadingProgress(80, this.loadingSteps[4]);
        await this.delay(500);

        this.updateLoadingProgress(100, this.loadingSteps[5]);
        await this.delay(1000);

        // Debug: Test building menu population
        console.log('Testing building menu...');
        if (this.game.buildingMenu) {
            this.game.buildingMenu.populateBuildingButtons();
        }
    }
    
    async setupUI() {
        // Set up global game reference
        window.game = this.game;
        
        // Electron API is now provided by preload script
        console.log('Electron API available:', !!window.electronAPI);
        if (window.electronAPI) {
            console.log('Electron API methods:', Object.keys(window.electronAPI));
        }
        
        // Initialize UI event listeners
        this.setupMenuEventListeners();
        this.setupGameEventListeners();
    }
    
    setupMenuEventListeners() {
        // Main menu buttons
        const newGameBtn = document.getElementById('new-game-btn');
        const loadGameBtn = document.getElementById('load-game-btn');
        const multiplayerBtn = document.getElementById('multiplayer-btn');
        const settingsBtn = document.getElementById('settings-btn');
        const exitBtn = document.getElementById('exit-btn');
        
        if (newGameBtn) {
            newGameBtn.addEventListener('click', () => {
                console.log('New Game button clicked');
                console.log('Game object:', this.game);
                console.log('Game saveSystem:', this.game?.saveSystem);
                this.showSaveSlotMenu('new');
            });
        }
        
        if (loadGameBtn) {
            loadGameBtn.addEventListener('click', () => this.showSaveSlotMenu('load'));
        }
        
        if (multiplayerBtn) {
            multiplayerBtn.addEventListener('click', () => this.showMultiplayerMenu());
        }
        
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.showSettingsMenu());
        }
        
        if (exitBtn) {
            exitBtn.addEventListener('click', () => this.exitGame());
        }
        
        // Save slot menu
        const backToMenuBtn = document.getElementById('back-to-menu-btn');
        if (backToMenuBtn) {
            backToMenuBtn.addEventListener('click', () => this.showMainMenu());
        }
    }
    
    setupGameEventListeners() {
        // Top UI buttons
        const menuBtn = document.getElementById('menu-btn');
        const friendsBtn = document.getElementById('friends-btn');
        const storeBtn = document.getElementById('store-btn');
        
        if (menuBtn) {
            menuBtn.addEventListener('click', () => this.showMainMenu());
        }
        
        if (friendsBtn) {
            friendsBtn.addEventListener('click', () => this.showFriendsMenu());
        }
        
        if (storeBtn) {
            storeBtn.addEventListener('click', () => this.showStore());
        }
    }
    
    async showMainMenu() {
        const loadingScreen = document.getElementById('loading-screen');
        const gameContainer = document.getElementById('game-container');
        const mainMenu = document.getElementById('main-menu');
        const saveSlotMenu = document.getElementById('save-slot-menu');
        
        loadingScreen.classList.add('hidden');
        gameContainer.classList.add('hidden');
        mainMenu.classList.remove('hidden');
        saveSlotMenu.classList.add('hidden');
        
        // Stop game if running
        if (this.game && this.game.isRunning) {
            this.game.stopGame();
        }
    }
    
    async showSaveSlotMenu(mode) {
        console.log('Showing save slot menu for mode:', mode);

        const mainMenu = document.getElementById('main-menu');
        const saveSlotMenu = document.getElementById('save-slot-menu');
        const saveSlotsContainer = document.getElementById('save-slots');

        console.log('Menu elements found:', {
            mainMenu: !!mainMenu,
            saveSlotMenu: !!saveSlotMenu,
            saveSlotsContainer: !!saveSlotsContainer
        });

        mainMenu.classList.add('hidden');
        saveSlotMenu.classList.remove('hidden');

        // Load save slots
        console.log('Loading save slots...');
        const saveSlots = await this.game.saveSystem.getSaveSlots();
        console.log('Save slots loaded:', saveSlots);

        this.populateSaveSlots(saveSlotsContainer, saveSlots, mode);
    }
    
    populateSaveSlots(container, slots, mode) {
        console.log('Populating save slots:', slots, 'mode:', mode);
        container.innerHTML = '';

        slots.forEach(slot => {
            console.log('Creating slot element for:', slot);
            const slotElement = document.createElement('div');
            slotElement.className = `save-slot ${slot.exists ? '' : 'empty'}`;
            
            if (slot.exists) {
                slotElement.innerHTML = `
                    <h4>${slot.townName}</h4>
                    <div class="save-slot-info">
                        <div>Level ${slot.level}</div>
                        <div>🍩 ${slot.donuts}</div>
                    </div>
                    <div class="save-slot-date">
                        ${new Date(slot.lastModified).toLocaleDateString()}
                    </div>
                `;
            } else {
                slotElement.innerHTML = `
                    <h4>Empty Slot ${slot.slot}</h4>
                    <div class="save-slot-info">Click to create new game</div>
                `;
            }
            
            slotElement.addEventListener('click', () => {
                this.handleSaveSlotClick(slot, mode);
            });
            
            container.appendChild(slotElement);
        });
    }
    
    async handleSaveSlotClick(slot, mode) {
        console.log('Save slot clicked:', slot, 'mode:', mode);

        if (mode === 'new') {
            // Create new game - show custom dialog instead of prompt
            const townName = await this.showTownNameDialog(`Springfield ${slot.slot}`);
            console.log('Town name entered:', townName);

            if (townName) {
                console.log('Creating new game...');
                const success = await this.game.newGame(slot.slot, townName);
                console.log('New game creation result:', success);

                if (success) {
                    console.log('Starting game...');
                    await this.startGame(slot.slot);
                } else {
                    this.showAlert('Failed to create new game. Please try again.');
                }
            }
        } else if (mode === 'load' && slot.exists) {
            // Load existing game
            console.log('Loading existing game from slot', slot.slot);
            await this.startGame(slot.slot);
        }
    }
    
    async startGame(saveSlot) {
        const mainMenu = document.getElementById('main-menu');
        const saveSlotMenu = document.getElementById('save-slot-menu');
        const gameContainer = document.getElementById('game-container');
        
        // Hide menus
        mainMenu.classList.add('hidden');
        saveSlotMenu.classList.add('hidden');
        
        // Show game
        gameContainer.classList.remove('hidden');
        
        // Start the game
        const success = await this.game.startGame(saveSlot);
        if (!success) {
            console.error('Failed to start game');
            this.showMainMenu();
        }
    }
    
    showMultiplayerMenu() {
        this.showAlert('Multiplayer features coming soon!');
    }

    showSettingsMenu() {
        this.showAlert('Settings menu coming soon!');
    }

    showFriendsMenu() {
        this.showAlert('Friends system coming soon!');
    }

    showStore() {
        this.showAlert('Donut store coming soon!');
    }
    
    exitGame() {
        if (window.electronAPI) {
            // Electron app - close window
            window.close();
        } else {
            // Browser - show confirmation
            if (confirm('Are you sure you want to exit?')) {
                window.close();
            }
        }
    }
    
    showErrorScreen(error) {
        const loadingScreen = document.getElementById('loading-screen');
        const loadingContent = loadingScreen.querySelector('.loading-content');
        
        loadingContent.innerHTML = `
            <h1>😞 Oops!</h1>
            <p>Something went wrong loading the game:</p>
            <p style="color: red; font-size: 0.9em;">${error.message}</p>
            <button onclick="location.reload()" class="menu-button">Try Again</button>
        `;
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Custom dialog methods to replace prompt() and alert()
    showTownNameDialog(defaultName) {
        return new Promise((resolve) => {
            // Create dialog overlay
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            // Create dialog box
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                background: linear-gradient(135deg, #FFE4B5 0%, #F0E68C 100%);
                padding: 30px;
                border-radius: 15px;
                border: 3px solid #8B4513;
                box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                text-align: center;
                min-width: 300px;
                font-family: 'Comic Sans MS', cursive, sans-serif;
            `;

            dialog.innerHTML = `
                <h3 style="margin: 0 0 20px 0; color: #8B4513;">🍩 Name Your Town 🍩</h3>
                <input type="text" id="town-name-input" value="${defaultName}"
                       style="width: 200px; padding: 10px; border: 2px solid #8B4513; border-radius: 5px;
                              font-size: 16px; text-align: center; margin-bottom: 20px;">
                <div>
                    <button id="confirm-btn" style="background: #32CD32; color: white; border: none;
                                                   padding: 10px 20px; margin: 0 10px; border-radius: 5px;
                                                   font-size: 16px; cursor: pointer;">Create Town</button>
                    <button id="cancel-btn" style="background: #DC143C; color: white; border: none;
                                                  padding: 10px 20px; margin: 0 10px; border-radius: 5px;
                                                  font-size: 16px; cursor: pointer;">Cancel</button>
                </div>
            `;

            overlay.appendChild(dialog);
            document.body.appendChild(overlay);

            // Focus input and select text
            const input = dialog.querySelector('#town-name-input');
            input.focus();
            input.select();

            // Handle buttons
            const confirmBtn = dialog.querySelector('#confirm-btn');
            const cancelBtn = dialog.querySelector('#cancel-btn');

            const cleanup = () => {
                document.body.removeChild(overlay);
            };

            confirmBtn.addEventListener('click', () => {
                const townName = input.value.trim();
                cleanup();
                resolve(townName || defaultName);
            });

            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(null);
            });

            // Handle Enter key
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const townName = input.value.trim();
                    cleanup();
                    resolve(townName || defaultName);
                }
            });

            // Handle Escape key
            overlay.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    cleanup();
                    resolve(null);
                }
            });
        });
    }

    showAlert(message) {
        return new Promise((resolve) => {
            // Create dialog overlay
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            // Create dialog box
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                background: linear-gradient(135deg, #FFE4B5 0%, #F0E68C 100%);
                padding: 30px;
                border-radius: 15px;
                border: 3px solid #8B4513;
                box-shadow: 0 10px 30px rgba(0,0,0,0.5);
                text-align: center;
                min-width: 300px;
                font-family: 'Comic Sans MS', cursive, sans-serif;
            `;

            dialog.innerHTML = `
                <h3 style="margin: 0 0 20px 0; color: #8B4513;">🍩 Springfield Alert 🍩</h3>
                <p style="margin: 0 0 20px 0; color: #8B4513;">${message}</p>
                <button id="ok-btn" style="background: #32CD32; color: white; border: none;
                                          padding: 10px 20px; border-radius: 5px;
                                          font-size: 16px; cursor: pointer;">OK</button>
            `;

            overlay.appendChild(dialog);
            document.body.appendChild(overlay);

            // Handle button
            const okBtn = dialog.querySelector('#ok-btn');

            const cleanup = () => {
                document.body.removeChild(overlay);
                resolve();
            };

            okBtn.addEventListener('click', cleanup);

            // Handle Enter or Escape key
            overlay.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === 'Escape') {
                    cleanup();
                }
            });

            // Focus the button
            okBtn.focus();
        });
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const gameLoader = new GameLoader();
    gameLoader.init();
});

// Handle window resize
window.addEventListener('resize', () => {
    if (window.game && window.game.engine3D) {
        window.game.engine3D.onWindowResize();
    }
});

// Handle visibility change for auto-pause
document.addEventListener('visibilitychange', () => {
    if (window.game && window.game.isRunning) {
        if (document.hidden) {
            window.game.pauseGame();
        } else {
            window.game.resumeGame();
        }
    }
});
