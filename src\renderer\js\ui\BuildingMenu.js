/**
 * Springfield Town Builder - Building Menu
 * Handles the single button building selection interface
 */

class BuildingMenu extends EventEmitter {
    constructor(buildingSystem, currencySystem) {
        super();
        this.buildingSystem = buildingSystem;
        this.currencySystem = currencySystem;

        this.currentCategory = 'all';
        this.isVisible = false;
        this.searchTerm = '';

        this.init();
    }

    init() {
        this.setupEventListeners();
        // Wait for building data to load before populating
        if (this.buildingSystem.buildingData) {
            this.populateBuildingButtons();
        } else {
            // Wait for building system to be ready
            setTimeout(() => this.init(), 100);
        }
        console.log('Single Button Building Menu initialized');
    }
    
    setupEventListeners() {
        // Main build button toggle
        const buildMenuButton = document.getElementById('build-menu-toggle');
        const buildingPanel = document.getElementById('building-panel');

        if (buildMenuButton && buildingPanel) {
            buildMenuButton.addEventListener('click', () => {
                console.log('Build menu button clicked');
                this.togglePanel();
            });
        }

        // Close panel button
        const closePanelBtn = document.getElementById('close-building-panel');
        if (closePanelBtn) {
            closePanelBtn.addEventListener('click', () => {
                console.log('Close panel button clicked');
                this.hidePanel();
            });
        }

        // Category tabs
        const categoryTabs = document.querySelectorAll('.category-tab');
        categoryTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const category = tab.dataset.category;
                console.log('Category tab clicked:', category);
                this.setCategory(category);
            });
        });

        // Search functionality
        const searchInput = document.getElementById('building-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchTerm = e.target.value;
                console.log('Search term:', this.searchTerm);
                this.filterBuildings();
            });
        }

        // Listen for building system events
        this.buildingSystem.on('buildingDataLoaded', () => {
            this.populateBuildingButtons();
        });

        this.buildingSystem.on('buildingAvailabilityChanged', () => {
            this.populateBuildingButtons();
        });
    }
    
    togglePanel() {
        const buildingPanel = document.getElementById('building-panel');
        if (buildingPanel) {
            this.isVisible = !this.isVisible;
            buildingPanel.style.display = this.isVisible ? 'block' : 'none';

            if (this.isVisible) {
                this.populateBuildingButtons();
                this.updateCategoryTabs();
            }
        }
    }

    showPanel() {
        const buildingPanel = document.getElementById('building-panel');
        if (buildingPanel) {
            this.isVisible = true;
            buildingPanel.style.display = 'block';
            this.populateBuildingButtons();
            this.updateCategoryTabs();
        }
    }

    hidePanel() {
        const buildingPanel = document.getElementById('building-panel');
        if (buildingPanel) {
            this.isVisible = false;
            buildingPanel.style.display = 'none';
        }
    }

    setCategory(category) {
        this.currentCategory = category;
        this.updateCategoryTabs();
        this.populateBuildingButtons();
    }

    updateCategoryTabs() {
        const categoryTabs = document.querySelectorAll('.category-tab');
        categoryTabs.forEach(tab => {
            if (tab.dataset.category === this.currentCategory) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });
    }
    
    populateBuildingButtons() {
        const container = document.getElementById('building-buttons');
        if (!container) {
            console.error('Building buttons container not found');
            return;
        }

        container.innerHTML = '';

        if (!this.buildingSystem.buildingData) {
            container.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">Loading buildings...</div>';
            return;
        }

        // Get all buildings or filtered by category
        let buildings;
        if (this.currentCategory === 'all') {
            buildings = Object.values(this.buildingSystem.buildingData);
        } else {
            buildings = Object.values(this.buildingSystem.buildingData).filter(building =>
                building.category === this.currentCategory
            );
        }

        if (buildings.length === 0) {
            container.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">No buildings in this category</div>';
            return;
        }

        console.log(`Populating ${buildings.length} buildings for category: ${this.currentCategory}`);

        buildings.forEach(building => {
            const button = this.createBuildingButton(building);
            container.appendChild(button);
        });

        // Apply search filter if active
        if (this.searchTerm) {
            this.filterBuildings();
        }
    }

    filterBuildings() {
        const buildingButtons = document.querySelectorAll('.building-btn');
        const term = this.searchTerm.toLowerCase();

        buildingButtons.forEach(btn => {
            const buildingName = btn.querySelector('.building-name')?.textContent.toLowerCase() || '';
            const matches = buildingName.includes(term);
            btn.style.display = matches ? 'flex' : 'none';
        });
    }
    
    createBuildingButton(building) {
        const button = document.createElement('div');
        button.className = 'building-btn';
        
        const isAffordable = this.buildingSystem.canAffordBuilding(building);
        const isUnlocked = this.buildingSystem.isBuildingUnlocked(building);
        
        if (!isAffordable || !isUnlocked) {
            button.classList.add('disabled');
        }
        
        if (building.cost.type === 'donuts') {
            button.classList.add('donut-cost');
        }
        
        // Create building icon (emoji fallback if no image)
        const buildingIcons = {
            'simpson_house': '🏠',
            'kwik_e_mart': '🏪',
            'moes_tavern': '🍺',
            'springfield_elementary': '🏫',
            'nuclear_power_plant': '⚡',
            'krusty_burger': '🍔',
            'androids_dungeon': '📚',
            'town_hall': '🏛️',
            'springfield_library': '📖',
            'flanders_house': '🏡',
            'tree': '🌳',
            'bench': '🪑',
            'fountain': '⛲'
        };

        const icon = buildingIcons[building.id] || '🏢';

        button.innerHTML = `
            <div class="building-icon" style="font-size: 2em; margin-bottom: 5px;">${icon}</div>
            <div class="building-name">${building.name}</div>
            <div class="building-cost">
                ${building.cost.type === 'free' ? 'FREE' : building.cost.amount}
            </div>
        `;
        
        if (isAffordable && isUnlocked) {
            button.addEventListener('click', () => {
                console.log('=== BUILDING BUTTON CLICKED ===');
                console.log('Building ID:', building.id);
                console.log('Building name:', building.name);
                console.log('Building system available:', !!this.buildingSystem);

                if (this.buildingSystem && this.buildingSystem.enterPlacementMode) {
                    console.log('Calling enterPlacementMode...');
                    const result = this.buildingSystem.enterPlacementMode(building.id);
                    console.log('enterPlacementMode result:', result);

                    // Close the panel after selecting a building
                    if (result) {
                        this.hidePanel();
                    }
                } else {
                    console.error('Building system or enterPlacementMode method not available!');
                    alert('Building system error - please refresh the page');
                }
            });
        } else {
            button.addEventListener('click', () => {
                console.log('=== BUILDING NOT AVAILABLE ===');
                console.log('Building ID:', building.id);
                console.log('Affordable:', isAffordable);
                console.log('Unlocked:', isUnlocked);
                console.log('Player level:', this.currencySystem?.getLevel());
                console.log('Player money:', this.currencySystem?.getMoney());
                console.log('Player donuts:', this.currencySystem?.getDonuts());
                console.log('Building cost:', building.cost);
                console.log('Building unlock level:', building.unlockLevel);

                if (!isAffordable) {
                    alert(`Not enough ${building.cost.type === 'donuts' ? 'donuts' : 'money'} to build ${building.name}!`);
                } else if (!isUnlocked) {
                    alert(`${building.name} unlocks at level ${building.unlockLevel}!`);
                }
            });
        }
        
        return button;
    }
    
    dispose() {
        this.removeAllListeners();
    }
}
