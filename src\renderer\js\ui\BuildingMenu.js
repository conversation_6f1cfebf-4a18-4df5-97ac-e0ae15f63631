/**
 * Springfield Town Builder - Building Menu
 * Handles the building selection interface
 */

class BuildingMenu extends EventEmitter {
    constructor(buildingSystem) {
        super();
        this.buildingSystem = buildingSystem;
        
        this.currentCategory = 'residential';
        this.isVisible = true;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        // Wait for building data to load before populating
        if (this.buildingSystem.buildingData) {
            this.populateBuildingButtons();
        } else {
            // Wait for building system to be ready
            setTimeout(() => this.init(), 100);
        }
        console.log('Building Menu initialized');
    }
    
    setupEventListeners() {
        // Category buttons
        const categoryButtons = document.querySelectorAll('.category-btn');
        categoryButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const category = btn.dataset.category;
                this.setCategory(category);
            });
        });
        
        // Listen for building system events
        this.buildingSystem.on('buildingDataLoaded', () => {
            this.populateBuildingButtons();
        });

        this.buildingSystem.on('categoryChanged', (category) => {
            this.updateCategoryButtons(category);
            this.populateBuildingButtons();
        });

        this.buildingSystem.on('buildingAvailabilityChanged', () => {
            this.populateBuildingButtons();
        });
    }
    
    setCategory(category) {
        this.currentCategory = category;
        this.buildingSystem.setCategory(category);
        this.updateCategoryButtons(category);
        this.populateBuildingButtons();
    }
    
    updateCategoryButtons(activeCategory) {
        const categoryButtons = document.querySelectorAll('.category-btn');
        categoryButtons.forEach(btn => {
            if (btn.dataset.category === activeCategory) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }
    
    populateBuildingButtons() {
        const container = document.getElementById('building-buttons');
        if (!container) return;

        container.innerHTML = '';

        if (!this.buildingSystem.buildingData) {
            container.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">Loading buildings...</div>';
            return;
        }

        const buildings = this.buildingSystem.getBuildingsInCategory(this.currentCategory);

        if (buildings.length === 0) {
            container.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">No buildings in this category</div>';
            return;
        }

        buildings.forEach(building => {
            const button = this.createBuildingButton(building);
            container.appendChild(button);
        });
    }
    
    createBuildingButton(building) {
        const button = document.createElement('div');
        button.className = 'building-btn';
        
        const isAffordable = this.buildingSystem.canAffordBuilding(building);
        const isUnlocked = this.buildingSystem.isBuildingUnlocked(building);
        
        if (!isAffordable || !isUnlocked) {
            button.classList.add('disabled');
        }
        
        if (building.cost.type === 'donuts') {
            button.classList.add('donut-cost');
        }
        
        // Create building icon (emoji fallback if no image)
        const buildingIcons = {
            'simpson_house': '🏠',
            'kwik_e_mart': '🏪',
            'moes_tavern': '🍺',
            'springfield_elementary': '🏫',
            'nuclear_power_plant': '⚡',
            'krusty_burger': '🍔',
            'androids_dungeon': '📚',
            'town_hall': '🏛️',
            'springfield_library': '📖',
            'flanders_house': '🏡',
            'tree': '🌳',
            'bench': '🪑',
            'fountain': '⛲'
        };

        const icon = buildingIcons[building.id] || '🏢';

        button.innerHTML = `
            <div class="building-icon" style="font-size: 2em; margin-bottom: 5px;">${icon}</div>
            <div class="building-name">${building.name}</div>
            <div class="building-cost">
                ${building.cost.type === 'free' ? 'FREE' : building.cost.amount}
            </div>
        `;
        
        if (isAffordable && isUnlocked) {
            button.addEventListener('click', () => {
                console.log('=== BUILDING BUTTON CLICKED ===');
                console.log('Building ID:', building.id);
                console.log('Building name:', building.name);
                console.log('Building system available:', !!this.buildingSystem);
                console.log('Building system methods:', this.buildingSystem ? Object.getOwnPropertyNames(Object.getPrototypeOf(this.buildingSystem)) : 'N/A');

                if (this.buildingSystem && this.buildingSystem.enterPlacementMode) {
                    console.log('Calling enterPlacementMode...');
                    const result = this.buildingSystem.enterPlacementMode(building.id);
                    console.log('enterPlacementMode result:', result);
                } else {
                    console.error('Building system or enterPlacementMode method not available!');
                    alert('Building system error - please refresh the page');
                }
            });
        } else {
            button.addEventListener('click', () => {
                console.log('=== BUILDING NOT AVAILABLE ===');
                console.log('Building ID:', building.id);
                console.log('Affordable:', isAffordable);
                console.log('Unlocked:', isUnlocked);
                console.log('Player level:', this.currencySystem?.getLevel());
                console.log('Player money:', this.currencySystem?.getMoney());
                console.log('Player donuts:', this.currencySystem?.getDonuts());
                console.log('Building cost:', building.cost);
                console.log('Building unlock level:', building.unlockLevel);

                if (!isAffordable) {
                    alert(`Not enough ${building.cost.type === 'donuts' ? 'donuts' : 'money'} to build ${building.name}!`);
                } else if (!isUnlocked) {
                    alert(`${building.name} unlocks at level ${building.unlockLevel}!`);
                }
            });
        }
        
        return button;
    }
    
    dispose() {
        this.removeAllListeners();
    }
}
