/**
 * Springfield Town Builder - Quest Panel UI
 * Displays active quests, storyline progress, and unlock information
 */

class QuestPanel {
    constructor(questSystem) {
        this.questSystem = questSystem;
        this.isVisible = false;
        this.currentTab = 'active';
        
        this.createPanel();
        this.setupEventListeners();
    }
    
    createPanel() {
        this.panel = document.createElement('div');
        this.panel.className = 'quest-panel';
        this.panel.innerHTML = `
            <div class="quest-header">
                <h3>📜 Quests & Story</h3>
                <button class="close-btn" id="close-quest-panel">×</button>
            </div>
            
            <div class="quest-tabs">
                <button class="quest-tab active" data-tab="active">Active Quests</button>
                <button class="quest-tab" data-tab="story">Story Progress</button>
                <button class="quest-tab" data-tab="unlocks">Unlocks</button>
                <button class="quest-tab" data-tab="completed">Completed</button>
            </div>
            
            <div class="quest-content">
                <!-- Active Quests Tab -->
                <div class="quest-tab-content active" id="active-quests-tab">
                    <div class="quest-list" id="active-quest-list">
                        <!-- Active quests will be populated here -->
                    </div>
                </div>
                
                <!-- Story Progress Tab -->
                <div class="quest-tab-content" id="story-progress-tab">
                    <div class="story-info">
                        <div class="chapter-info">
                            <h4>Current Chapter: <span id="current-chapter">1</span></h4>
                            <div class="progress-bar">
                                <div class="progress-fill" id="story-progress"></div>
                            </div>
                            <p class="progress-text" id="story-progress-text">0% Complete</p>
                        </div>
                        
                        <div class="story-summary" id="story-summary">
                            <h4>Springfield Beginnings</h4>
                            <p>Help Homer rebuild Springfield from scratch and bring the community back together.</p>
                        </div>
                        
                        <div class="available-quests">
                            <h4>Available Quests</h4>
                            <div id="available-quest-list">
                                <!-- Available quests will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Unlocks Tab -->
                <div class="quest-tab-content" id="unlocks-tab">
                    <div class="unlock-categories">
                        <div class="unlock-category">
                            <h4>🏢 Buildings</h4>
                            <div class="unlock-grid" id="building-unlocks">
                                <!-- Building unlocks will be populated here -->
                            </div>
                        </div>
                        
                        <div class="unlock-category">
                            <h4>👥 Characters</h4>
                            <div class="unlock-grid" id="character-unlocks">
                                <!-- Character unlocks will be populated here -->
                            </div>
                        </div>
                        
                        <div class="unlock-category">
                            <h4>⭐ Features</h4>
                            <div class="unlock-list" id="feature-unlocks">
                                <!-- Feature unlocks will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Completed Quests Tab -->
                <div class="quest-tab-content" id="completed-quests-tab">
                    <div class="completed-stats">
                        <div class="stat-item">
                            <span>Quests Completed:</span>
                            <span id="completed-count">0</span>
                        </div>
                        <div class="stat-item">
                            <span>Total Rewards:</span>
                            <span id="total-rewards">$0</span>
                        </div>
                    </div>
                    
                    <div class="quest-list" id="completed-quest-list">
                        <!-- Completed quests will be populated here -->
                    </div>
                </div>
            </div>
        `;
        
        // Style the panel
        this.panel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 600px;
            max-height: 80vh;
            background: rgba(255, 255, 255, 0.95);
            border: 3px solid #4CAF50;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            overflow: hidden;
            backdrop-filter: blur(10px);
            font-family: 'Comic Sans MS', cursive, sans-serif;
            display: none;
        `;
        
        document.body.appendChild(this.panel);
    }
    
    setupEventListeners() {
        // Close button
        document.getElementById('close-quest-panel').addEventListener('click', () => {
            this.hide();
        });
        
        // Tab switching
        const tabs = this.panel.querySelectorAll('.quest-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                this.switchTab(tab.dataset.tab);
            });
        });
        
        // Quest system events
        if (this.questSystem) {
            this.questSystem.on('questStarted', () => this.updateActiveQuests());
            this.questSystem.on('questCompleted', () => this.updateAllTabs());
            this.questSystem.on('questProgressUpdated', () => this.updateActiveQuests());
            this.questSystem.on('unlocksUpdated', () => this.updateUnlocksTab());
        }
        
        // Click outside to close
        document.addEventListener('click', (e) => {
            if (this.isVisible && !this.panel.contains(e.target) && !e.target.classList.contains('quest-button')) {
                this.hide();
            }
        });
    }
    
    switchTab(tabName) {
        // Update tab buttons
        const tabs = this.panel.querySelectorAll('.quest-tab');
        tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // Update tab content
        const contents = this.panel.querySelectorAll('.quest-tab-content');
        contents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-${tabName === 'active' ? 'quests-' : tabName === 'story' ? 'progress-' : tabName === 'unlocks' ? '' : 'quests-'}tab`);
        });
        
        this.currentTab = tabName;
        
        // Update content for the selected tab
        switch (tabName) {
            case 'active':
                this.updateActiveQuests();
                break;
            case 'story':
                this.updateStoryProgress();
                break;
            case 'unlocks':
                this.updateUnlocksTab();
                break;
            case 'completed':
                this.updateCompletedQuests();
                break;
        }
    }
    
    updateActiveQuests() {
        const container = document.getElementById('active-quest-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        if (this.questSystem.activeQuests.size === 0) {
            container.innerHTML = `
                <div class="no-quests">
                    <p>No active quests</p>
                    <p class="hint">Complete current objectives to unlock new quests!</p>
                </div>
            `;
            return;
        }
        
        for (const [questId, quest] of this.questSystem.activeQuests) {
            const questElement = this.createQuestElement(quest, 'active');
            container.appendChild(questElement);
        }
    }
    
    updateStoryProgress() {
        // Update chapter info
        document.getElementById('current-chapter').textContent = this.questSystem.currentStoryChapter;
        
        // Update progress bar
        const progressBar = document.getElementById('story-progress');
        const progressText = document.getElementById('story-progress-text');
        const progress = Math.min(this.questSystem.storyProgress, 100);
        
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
        if (progressText) {
            progressText.textContent = `${progress}% Complete`;
        }
        
        // Update available quests
        const availableContainer = document.getElementById('available-quest-list');
        if (availableContainer) {
            availableContainer.innerHTML = '';
            
            this.questSystem.availableQuests.forEach(quest => {
                const questElement = this.createQuestElement(quest, 'available');
                availableContainer.appendChild(questElement);
            });
        }
    }
    
    updateUnlocksTab() {
        this.updateBuildingUnlocks();
        this.updateCharacterUnlocks();
        this.updateFeatureUnlocks();
    }
    
    updateBuildingUnlocks() {
        const container = document.getElementById('building-unlocks');
        if (!container) return;
        
        container.innerHTML = '';
        
        for (const [buildingId, requirements] of Object.entries(this.questSystem.buildingUnlocks)) {
            const isUnlocked = this.questSystem.isUnlocked('building', buildingId);
            const unlockElement = this.createUnlockElement('building', buildingId, requirements, isUnlocked);
            container.appendChild(unlockElement);
        }
    }
    
    updateCharacterUnlocks() {
        const container = document.getElementById('character-unlocks');
        if (!container) return;
        
        container.innerHTML = '';
        
        for (const [characterId, requirements] of Object.entries(this.questSystem.characterUnlocks)) {
            const isUnlocked = this.questSystem.isUnlocked('character', characterId);
            const unlockElement = this.createUnlockElement('character', characterId, requirements, isUnlocked);
            container.appendChild(unlockElement);
        }
    }
    
    updateFeatureUnlocks() {
        const container = document.getElementById('feature-unlocks');
        if (!container) return;
        
        container.innerHTML = '';
        
        for (const [featureId, requirements] of Object.entries(this.questSystem.featureUnlocks)) {
            const isUnlocked = this.questSystem.isUnlocked('feature', featureId);
            const unlockElement = this.createFeatureUnlockElement(featureId, requirements, isUnlocked);
            container.appendChild(unlockElement);
        }
    }
    
    updateCompletedQuests() {
        const container = document.getElementById('completed-quest-list');
        const countElement = document.getElementById('completed-count');
        
        if (countElement) {
            countElement.textContent = this.questSystem.completedQuests.size;
        }
        
        if (!container) return;
        
        container.innerHTML = '';
        
        // Get completed quests with their data
        const completedQuests = [];
        for (const questId of this.questSystem.completedQuests) {
            const quest = this.questSystem.findQuestById(questId);
            if (quest) {
                completedQuests.push(quest);
            }
        }
        
        completedQuests.forEach(quest => {
            const questElement = this.createQuestElement(quest, 'completed');
            container.appendChild(questElement);
        });
    }
    
    createQuestElement(quest, status) {
        const element = document.createElement('div');
        element.className = `quest-item quest-${status}`;
        
        const typeIcons = {
            tutorial: '🎓',
            building: '🏢',
            character: '👤',
            story: '📖'
        };
        
        const icon = typeIcons[quest.type] || '📜';
        
        let objectivesHtml = '';
        if (status === 'active' && quest.objectives) {
            objectivesHtml = quest.objectives.map(obj => {
                const progress = typeof obj.target === 'number' 
                    ? `${obj.current}/${obj.target}`
                    : obj.current > 0 ? '✓' : '○';
                
                const isComplete = typeof obj.target === 'number' 
                    ? obj.current >= obj.target
                    : obj.current > 0;
                
                return `
                    <div class="objective ${isComplete ? 'complete' : ''}">
                        <span class="objective-text">${obj.description}</span>
                        <span class="objective-progress">${progress}</span>
                    </div>
                `;
            }).join('');
        }
        
        let rewardsHtml = '';
        if (quest.rewards) {
            const rewards = [];
            if (quest.rewards.money) rewards.push(`💰 $${quest.rewards.money}`);
            if (quest.rewards.xp) rewards.push(`⭐ ${quest.rewards.xp} XP`);
            if (quest.rewards.donuts) rewards.push(`🍩 ${quest.rewards.donuts}`);
            
            rewardsHtml = `<div class="quest-rewards">${rewards.join(' | ')}</div>`;
        }
        
        element.innerHTML = `
            <div class="quest-header">
                <span class="quest-icon">${icon}</span>
                <div class="quest-info">
                    <h4 class="quest-name">${quest.name}</h4>
                    <p class="quest-description">${quest.description}</p>
                </div>
                <span class="quest-chapter">Ch. ${quest.chapter || 1}</span>
            </div>
            ${objectivesHtml ? `<div class="quest-objectives">${objectivesHtml}</div>` : ''}
            ${rewardsHtml}
        `;
        
        // Add click handler for available quests
        if (status === 'available') {
            element.style.cursor = 'pointer';
            element.addEventListener('click', () => {
                this.questSystem.startQuest(quest.id);
            });
        }
        
        return element;
    }
    
    createUnlockElement(type, id, requirements, isUnlocked) {
        const element = document.createElement('div');
        element.className = `unlock-item ${isUnlocked ? 'unlocked' : 'locked'}`;
        
        const name = id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        const icon = type === 'building' ? '🏢' : '👤';
        
        let requirementText = '';
        if (!isUnlocked) {
            const reqParts = [];
            if (requirements.level > 1) reqParts.push(`Level ${requirements.level}`);
            if (requirements.quest) reqParts.push(`Complete "${requirements.quest.replace(/_/g, ' ')}"`);
            requirementText = reqParts.join(', ');
        }
        
        let costText = '';
        if (requirements.cost && requirements.cost.type !== 'free') {
            const costIcon = requirements.cost.type === 'donuts' ? '🍩' : '💰';
            costText = `${costIcon} ${requirements.cost.amount}`;
        }
        
        element.innerHTML = `
            <div class="unlock-icon">${icon}</div>
            <div class="unlock-info">
                <h5>${name}</h5>
                ${!isUnlocked && requirementText ? `<p class="requirement">${requirementText}</p>` : ''}
                ${costText ? `<p class="cost">${costText}</p>` : ''}
            </div>
            <div class="unlock-status">
                ${isUnlocked ? '✅' : '🔒'}
            </div>
        `;
        
        return element;
    }
    
    createFeatureUnlockElement(featureId, requirements, isUnlocked) {
        const element = document.createElement('div');
        element.className = `feature-unlock ${isUnlocked ? 'unlocked' : 'locked'}`;
        
        const name = featureId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        
        let requirementText = '';
        if (!isUnlocked) {
            const reqParts = [];
            if (requirements.level > 1) reqParts.push(`Level ${requirements.level}`);
            if (requirements.quest) reqParts.push(`Complete "${requirements.quest.replace(/_/g, ' ')}"`);
            requirementText = reqParts.join(', ');
        }
        
        element.innerHTML = `
            <div class="feature-info">
                <h5>⭐ ${name}</h5>
                ${!isUnlocked && requirementText ? `<p class="requirement">${requirementText}</p>` : ''}
            </div>
            <div class="feature-status">
                ${isUnlocked ? '✅' : '🔒'}
            </div>
        `;
        
        return element;
    }
    
    updateAllTabs() {
        this.updateActiveQuests();
        this.updateStoryProgress();
        this.updateUnlocksTab();
        this.updateCompletedQuests();
    }
    
    show() {
        this.isVisible = true;
        this.panel.style.display = 'block';
        this.updateAllTabs();
        
        // Add entrance animation
        this.panel.style.opacity = '0';
        this.panel.style.transform = 'translate(-50%, -50%) scale(0.9)';
        
        requestAnimationFrame(() => {
            this.panel.style.transition = 'all 0.3s ease-out';
            this.panel.style.opacity = '1';
            this.panel.style.transform = 'translate(-50%, -50%) scale(1)';
        });
    }
    
    hide() {
        this.isVisible = false;
        
        this.panel.style.transition = 'all 0.3s ease-in';
        this.panel.style.opacity = '0';
        this.panel.style.transform = 'translate(-50%, -50%) scale(0.9)';
        
        setTimeout(() => {
            this.panel.style.display = 'none';
        }, 300);
    }
    
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
}
