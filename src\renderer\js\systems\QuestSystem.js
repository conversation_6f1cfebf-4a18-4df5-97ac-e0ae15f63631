/**
 * Springfield Town Builder - Quest System
 * Manages storyline quests, character progression, and unlock system
 */

class QuestSystem extends EventEmitter {
    constructor(game) {
        super();
        this.game = game;
        
        // Quest tracking
        this.activeQuests = new Map();
        this.completedQuests = new Set();
        this.availableQuests = [];
        this.questChains = new Map();
        
        // Unlock tracking - start with nothing unlocked
        this.unlockedBuildings = new Set();
        this.unlockedCharacters = new Set();
        this.unlockedFeatures = new Set(['basic_building']);
        
        // Story progression
        this.currentStoryChapter = 1;
        this.storyProgress = 0;
        this.tutorialCompleted = false;
        
        this.init();
    }
    
    init() {
        this.setupQuestData();
        this.setupUnlockRequirements();
        this.setupEventListeners();
        this.initializeStartingQuests();
        
        console.log('Quest System initialized with storyline progression');
    }
    
    setupQuestData() {
        // Define all quest chains and storylines
        this.questChains.set('main_story', {
            name: 'Springfield Beginnings',
            description: 'Help <PERSON> rebuild Springfield from scratch',
            quests: [
                {
                    id: 'welcome_to_springfield',
                    name: 'Welcome to Springfield!',
                    description: '<PERSON> has arrived in an empty Springfield. Help him get started by building his house!',
                    type: 'tutorial',
                    chapter: 1,
                    objectives: [
                        { type: 'build_building', target: 'simpsons_house', current: 0, description: 'Build the Simpson House' }
                    ],
                    rewards: {
                        money: 300,
                        xp: 50,
                        unlocks: ['homer', 'marge']
                    },
                    dialogue: {
                        start: "D'oh! Where is everyone? I guess I'll have to rebuild Springfield myself! First, I need a place to live!",
                        complete: "Woo-hoo! Home sweet home! Maybe Marge will come back now that we have a house."
                    }
                },
                {
                    id: 'family_reunion',
                    name: 'Family Reunion',
                    description: 'Bring the Simpson family back together',
                    type: 'character',
                    chapter: 1,
                    prerequisites: ['welcome_to_springfield'],
                    objectives: [
                        { type: 'unlock_character', target: 'marge', current: 0, description: 'Unlock Marge Simpson' },
                        { type: 'complete_tasks', target: 3, current: 0, description: 'Complete 3 family tasks' }
                    ],
                    rewards: {
                        money: 300,
                        xp: 75,
                        unlocks: ['bart', 'lisa']
                    },
                    dialogue: {
                        start: "Marge! You're back! Now we need to find the kids.",
                        complete: "The family's back together! Now let's rebuild our town!"
                    }
                },
                {
                    id: 'build_roads',
                    name: 'Springfield Streets',
                    description: 'Every town needs proper roads! Build some road sections to connect your buildings.',
                    type: 'tutorial',
                    chapter: 1,
                    prerequisites: ['welcome_to_springfield'],
                    objectives: [
                        { type: 'build_building', target: 'road_straight', current: 0, description: 'Build 2 straight road sections' },
                        { type: 'build_building', target: 'road_corner', current: 0, description: 'Build 1 corner road section' }
                    ],
                    rewards: {
                        money: 150,
                        xp: 30,
                        unlocks: ['road_intersection']
                    },
                    dialogue: {
                        start: "A town needs proper streets! Let's build some roads to connect our buildings.",
                        complete: "Now that's what I call infrastructure! Springfield is starting to look like a real town."
                    }
                },
                {
                    id: 'first_business',
                    name: 'First Business',
                    description: 'Springfield needs its first business establishment',
                    type: 'building',
                    chapter: 1,
                    prerequisites: ['family_reunion'],
                    objectives: [
                        { type: 'build_building', target: 'kwik_e_mart', current: 0, description: 'Build the Kwik-E-Mart' },
                        { type: 'earn_money', target: 500, current: 0, description: 'Earn $500 from businesses' }
                    ],
                    rewards: {
                        money: 500,
                        xp: 100,
                        unlocks: ['apu', 'moes_tavern']
                    },
                    dialogue: {
                        start: "We need somewhere to buy our daily essentials... and beer!",
                        complete: "Excellent! Now we have a proper convenience store!"
                    }
                },
                {
                    id: 'social_hub',
                    name: 'Social Hub',
                    description: 'Every town needs a place for people to gather',
                    type: 'building',
                    chapter: 2,
                    prerequisites: ['first_business'],
                    objectives: [
                        { type: 'build_building', target: 'moes_tavern', current: 0, description: "Build Moe's Tavern" },
                        { type: 'unlock_character', target: 'moe', current: 0, description: 'Unlock Moe Szyslak' },
                        { type: 'social_interactions', target: 5, current: 0, description: 'Have 5 character social interactions' }
                    ],
                    rewards: {
                        money: 750,
                        xp: 150,
                        unlocks: ['barney', 'community_center']
                    },
                    dialogue: {
                        start: "A man needs a place to drink and complain about his problems!",
                        complete: "Perfect! Now Springfield has a real social center!"
                    }
                },
                {
                    id: 'power_to_the_people',
                    name: 'Power to the People',
                    description: 'Springfield needs reliable power generation',
                    type: 'building',
                    chapter: 2,
                    prerequisites: ['social_hub'],
                    objectives: [
                        { type: 'build_building', target: 'power_plant', current: 0, description: 'Build the Nuclear Power Plant' },
                        { type: 'unlock_character', target: 'mr_burns', current: 0, description: 'Unlock Mr. Burns' },
                        { type: 'reach_level', target: 5, current: 0, description: 'Reach town level 5' }
                    ],
                    rewards: {
                        money: 1000,
                        xp: 200,
                        unlocks: ['smithers', 'advanced_buildings']
                    },
                    dialogue: {
                        start: "We need power for all these new buildings! Time to go nuclear!",
                        complete: "Excellent! Now Springfield has unlimited power... probably safe too!"
                    }
                }
            ]
        });
        
        // Side quest chains
        this.questChains.set('education', {
            name: 'Education First',
            description: 'Build educational facilities for Springfield',
            quests: [
                {
                    id: 'school_days',
                    name: 'School Days',
                    description: 'Springfield needs a proper school',
                    type: 'building',
                    chapter: 2,
                    prerequisites: ['family_reunion'],
                    objectives: [
                        { type: 'build_building', target: 'elementary_school', current: 0, description: 'Build Springfield Elementary' },
                        { type: 'unlock_character', target: 'principal_skinner', current: 0, description: 'Unlock Principal Skinner' }
                    ],
                    rewards: {
                        money: 600,
                        xp: 120,
                        unlocks: ['library', 'university']
                    }
                }
            ]
        });
        
        this.questChains.set('entertainment', {
            name: 'Entertainment District',
            description: 'Create entertainment venues for Springfield residents',
            quests: [
                {
                    id: 'comic_relief',
                    name: 'Comic Relief',
                    description: 'Every town needs a comic book store',
                    type: 'building',
                    chapter: 2,
                    prerequisites: ['first_business'],
                    objectives: [
                        { type: 'build_building', target: 'androids_dungeon', current: 0, description: "Build Android's Dungeon" },
                        { type: 'unlock_character', target: 'comic_book_guy', current: 0, description: 'Unlock Comic Book Guy' }
                    ],
                    rewards: {
                        money: 400,
                        xp: 80,
                        unlocks: ['arcade', 'movie_theater']
                    }
                }
            ]
        });
    }
    
    setupUnlockRequirements() {
        // Define unlock requirements for buildings
        this.buildingUnlocks = {
            'simpsons_house': { level: 1, quest: null, cost: { type: 'free' } }, // Free starter building
            'kwik_e_mart': { level: 1, quest: 'welcome_to_springfield', cost: { type: 'money', amount: 500 } },
            'moes_tavern': { level: 2, quest: 'first_business', cost: { type: 'money', amount: 1000 } },
            'power_plant': { level: 3, quest: 'social_hub', cost: { type: 'money', amount: 2500 } },
            'elementary_school': { level: 2, quest: 'family_reunion', cost: { type: 'money', amount: 1500 } },
            'androids_dungeon': { level: 2, quest: 'first_business', cost: { type: 'money', amount: 800 } },
            'community_center': { level: 3, quest: 'social_hub', cost: { type: 'money', amount: 2000 } },
            'library': { level: 3, quest: 'school_days', cost: { type: 'money', amount: 1200 } },
            'arcade': { level: 3, quest: 'comic_relief', cost: { type: 'donuts', amount: 25 } },
            'movie_theater': { level: 4, quest: 'comic_relief', cost: { type: 'money', amount: 3000 } },
            'university': { level: 5, quest: 'school_days', cost: { type: 'donuts', amount: 50 } },

            // Roads - available early for town planning
            'road_straight': { level: 1, quest: null, cost: { type: 'money', amount: 25 } },
            'road_corner': { level: 1, quest: null, cost: { type: 'money', amount: 25 } },
            'road_t_junction': { level: 1, quest: null, cost: { type: 'money', amount: 35 } },
            'road_intersection': { level: 2, quest: null, cost: { type: 'money', amount: 50 } }
        };
        
        // Define unlock requirements for characters
        this.characterUnlocks = {
            'homer': { level: 1, quest: null, cost: { type: 'free' } }, // Homer is always available
            'marge': { level: 1, quest: 'welcome_to_springfield', cost: { type: 'free' } },
            'bart': { level: 1, quest: 'family_reunion', cost: { type: 'money', amount: 200 } },
            'lisa': { level: 1, quest: 'family_reunion', cost: { type: 'money', amount: 200 } },
            'apu': { level: 2, quest: 'first_business', cost: { type: 'money', amount: 300 } },
            'moe': { level: 2, quest: 'social_hub', cost: { type: 'money', amount: 400 } },
            'barney': { level: 2, quest: 'social_hub', cost: { type: 'money', amount: 250 } },
            'mr_burns': { level: 3, quest: 'power_to_the_people', cost: { type: 'donuts', amount: 30 } },
            'smithers': { level: 3, quest: 'power_to_the_people', cost: { type: 'money', amount: 600 } },
            'principal_skinner': { level: 2, quest: 'school_days', cost: { type: 'money', amount: 350 } },
            'comic_book_guy': { level: 2, quest: 'comic_relief', cost: { type: 'money', amount: 300 } }
        };
        
        // Define feature unlocks
        this.featureUnlocks = {
            'basic_building': { level: 1, quest: null },
            'advanced_buildings': { level: 3, quest: 'power_to_the_people' },
            'decorations': { level: 2, quest: 'family_reunion' },
            'premium_content': { level: 4, quest: 'power_to_the_people' },
            'multiplayer': { level: 5, quest: 'power_to_the_people' },
            'weather_control': { level: 6, quest: 'power_to_the_people' }
        };
    }
    
    setupEventListeners() {
        // Listen to game events to update quest progress
        if (this.game.buildingSystem) {
            this.game.buildingSystem.on('buildingPlaced', (data) => {
                this.updateQuestProgress('build_building', data.building.userData.type);
            });
            
            this.game.buildingSystem.on('incomeCollected', (data) => {
                this.updateQuestProgress('collect_income', data.amount);
                this.updateQuestProgress('earn_money', data.amount);
            });
        }
        
        if (this.game.characterSystem) {
            this.game.characterSystem.on('characterSpawned', (data) => {
                this.updateQuestProgress('unlock_character', data.character.userData.type);
            });
            
            this.game.characterSystem.on('socialInteraction', (data) => {
                this.updateQuestProgress('social_interactions', 1);
            });
        }
        
        if (this.game.taskSystem) {
            this.game.taskSystem.on('taskCompleted', (data) => {
                this.updateQuestProgress('complete_tasks', 1);
            });
        }
        
        if (this.game.currencySystem) {
            this.game.currencySystem.on('levelUp', (data) => {
                this.updateQuestProgress('reach_level', data.newLevel);
                this.checkLevelUnlocks(data.newLevel);
            });
        }
    }
    
    initializeStartingQuests() {
        // Unlock the basic starting content
        this.unlockedCharacters.add('homer');
        this.unlockedBuildings.add('simpsons_house');

        // Unlock basic roads from the start for town planning
        this.unlockedBuildings.add('road_straight');
        this.unlockedBuildings.add('road_corner');
        this.unlockedBuildings.add('road_t_junction');

        // Start with the first main story quest
        this.startQuest('welcome_to_springfield');
    }
    
    startQuest(questId) {
        const quest = this.findQuestById(questId);
        if (!quest) {
            console.error('Quest not found:', questId);
            return false;
        }
        
        // Check prerequisites
        if (quest.prerequisites) {
            for (const prereq of quest.prerequisites) {
                if (!this.completedQuests.has(prereq)) {
                    console.log('Quest prerequisites not met:', questId);
                    return false;
                }
            }
        }
        
        // Initialize quest progress
        const activeQuest = {
            ...quest,
            startTime: Date.now(),
            objectives: quest.objectives.map(obj => ({ ...obj, current: 0 }))
        };
        
        this.activeQuests.set(questId, activeQuest);
        
        // Show quest start notification
        this.showQuestNotification(activeQuest, 'started');
        
        // Show dialogue if available
        if (quest.dialogue && quest.dialogue.start) {
            this.showDialogue(quest.dialogue.start, 'homer');
        }
        
        this.emit('questStarted', activeQuest);
        return true;
    }
    
    updateQuestProgress(objectiveType, value, specificTarget = null) {
        for (const [questId, quest] of this.activeQuests) {
            let questUpdated = false;
            
            for (const objective of quest.objectives) {
                if (objective.type === objectiveType) {
                    // Check if this objective matches the specific target (if provided)
                    if (specificTarget && objective.target !== specificTarget) {
                        continue;
                    }
                    
                    // Update progress
                    if (typeof objective.target === 'number') {
                        objective.current = Math.min(objective.current + value, objective.target);
                    } else if (objective.target === value) {
                        objective.current = 1;
                    }
                    
                    questUpdated = true;
                }
            }
            
            if (questUpdated) {
                this.checkQuestCompletion(questId);
                this.emit('questProgressUpdated', { questId, quest });
            }
        }
    }
    
    checkQuestCompletion(questId) {
        const quest = this.activeQuests.get(questId);
        if (!quest) return;
        
        // Check if all objectives are complete
        const allComplete = quest.objectives.every(obj => {
            if (typeof obj.target === 'number') {
                return obj.current >= obj.target;
            } else {
                return obj.current > 0;
            }
        });
        
        if (allComplete) {
            this.completeQuest(questId);
        }
    }
    
    completeQuest(questId) {
        const quest = this.activeQuests.get(questId);
        if (!quest) return;
        
        // Mark as completed
        this.completedQuests.add(questId);
        this.activeQuests.delete(questId);
        
        // Give rewards
        this.giveQuestRewards(quest);
        
        // Process unlocks
        if (quest.rewards.unlocks) {
            this.processUnlocks(quest.rewards.unlocks);
        }
        
        // Show completion notification
        this.showQuestNotification(quest, 'completed');
        
        // Show completion dialogue
        if (quest.dialogue && quest.dialogue.complete) {
            this.showDialogue(quest.dialogue.complete, 'homer');
        }
        
        // Check for follow-up quests
        this.checkFollowUpQuests(questId);
        
        // Update story progress
        this.updateStoryProgress(quest);
        
        this.emit('questCompleted', quest);
    }
    
    giveQuestRewards(quest) {
        const rewards = quest.rewards;
        
        if (rewards.money && this.game.currencySystem) {
            this.game.currencySystem.addMoney(rewards.money, 'quest_reward');
        }
        
        if (rewards.xp && this.game.currencySystem) {
            this.game.currencySystem.addXP(rewards.xp, 'quest_reward');
        }
        
        if (rewards.donuts && this.game.currencySystem) {
            this.game.currencySystem.addDonuts(rewards.donuts, 'quest_reward');
        }
    }
    
    processUnlocks(unlocks) {
        for (const unlock of unlocks) {
            if (this.characterUnlocks[unlock]) {
                this.unlockedCharacters.add(unlock);
                this.showUnlockNotification('character', unlock);
            } else if (this.buildingUnlocks[unlock]) {
                this.unlockedBuildings.add(unlock);
                this.showUnlockNotification('building', unlock);
            } else if (this.featureUnlocks[unlock]) {
                this.unlockedFeatures.add(unlock);
                this.showUnlockNotification('feature', unlock);
            }
        }
        
        // Update UI to reflect new unlocks
        this.updateUnlockUI();
    }
    
    checkFollowUpQuests(completedQuestId) {
        // Find quests that have this quest as a prerequisite
        for (const [chainName, chain] of this.questChains) {
            for (const quest of chain.quests) {
                if (quest.prerequisites && quest.prerequisites.includes(completedQuestId)) {
                    if (!this.activeQuests.has(quest.id) && !this.completedQuests.has(quest.id)) {
                        // Start the follow-up quest
                        setTimeout(() => {
                            this.startQuest(quest.id);
                        }, 2000); // Small delay for better UX
                    }
                }
            }
        }
    }
    
    updateStoryProgress(quest) {
        if (quest.type === 'tutorial' || quest.chapter === 1) {
            this.storyProgress += 10;
            
            if (quest.type === 'tutorial') {
                this.tutorialCompleted = true;
            }
        }
        
        // Check for chapter progression
        const currentChapterQuests = this.getQuestsForChapter(this.currentStoryChapter);
        const completedChapterQuests = currentChapterQuests.filter(q => this.completedQuests.has(q.id));
        
        if (completedChapterQuests.length >= currentChapterQuests.length * 0.8) {
            this.advanceToNextChapter();
        }
    }
    
    advanceToNextChapter() {
        this.currentStoryChapter++;
        this.showChapterNotification(this.currentStoryChapter);
        
        // Unlock new quest chains for the new chapter
        this.unlockChapterQuests(this.currentStoryChapter);
    }
    
    unlockChapterQuests(chapter) {
        for (const [chainName, chain] of this.questChains) {
            for (const quest of chain.quests) {
                if (quest.chapter === chapter && !this.activeQuests.has(quest.id) && !this.completedQuests.has(quest.id)) {
                    // Check if prerequisites are met
                    let canStart = true;
                    if (quest.prerequisites) {
                        canStart = quest.prerequisites.every(prereq => this.completedQuests.has(prereq));
                    }
                    
                    if (canStart) {
                        this.availableQuests.push(quest);
                    }
                }
            }
        }
    }
    
    checkLevelUnlocks(level) {
        // Check building unlocks
        for (const [buildingId, requirements] of Object.entries(this.buildingUnlocks)) {
            if (requirements.level <= level && !this.unlockedBuildings.has(buildingId)) {
                // Check if quest requirement is met
                if (!requirements.quest || this.completedQuests.has(requirements.quest)) {
                    this.unlockedBuildings.add(buildingId);
                    this.showUnlockNotification('building', buildingId);
                }
            }
        }
        
        // Check character unlocks
        for (const [characterId, requirements] of Object.entries(this.characterUnlocks)) {
            if (requirements.level <= level && !this.unlockedCharacters.has(characterId)) {
                if (!requirements.quest || this.completedQuests.has(requirements.quest)) {
                    this.unlockedCharacters.add(characterId);
                    this.showUnlockNotification('character', characterId);
                }
            }
        }
        
        // Check feature unlocks
        for (const [featureId, requirements] of Object.entries(this.featureUnlocks)) {
            if (requirements.level <= level && !this.unlockedFeatures.has(featureId)) {
                if (!requirements.quest || this.completedQuests.has(requirements.quest)) {
                    this.unlockedFeatures.add(featureId);
                    this.showUnlockNotification('feature', featureId);
                }
            }
        }
    }
    
    // Utility methods
    findQuestById(questId) {
        for (const [chainName, chain] of this.questChains) {
            const quest = chain.quests.find(q => q.id === questId);
            if (quest) return quest;
        }
        return null;
    }
    
    getQuestsForChapter(chapter) {
        const quests = [];
        for (const [chainName, chain] of this.questChains) {
            quests.push(...chain.quests.filter(q => q.chapter === chapter));
        }
        return quests;
    }
    
    isUnlocked(type, id) {
        switch (type) {
            case 'building':
                return this.unlockedBuildings.has(id);
            case 'character':
                return this.unlockedCharacters.has(id);
            case 'feature':
                return this.unlockedFeatures.has(id);
            default:
                return false;
        }
    }
    
    getUnlockRequirements(type, id) {
        switch (type) {
            case 'building':
                return this.buildingUnlocks[id];
            case 'character':
                return this.characterUnlocks[id];
            case 'feature':
                return this.featureUnlocks[id];
            default:
                return null;
        }
    }
    
    // UI notification methods
    showQuestNotification(quest, type) {
        if (window.notificationSystem) {
            const title = type === 'started' ? 'New Quest!' : 'Quest Complete!';
            const icon = type === 'started' ? '📜' : '✅';
            
            notificationSystem.show({
                title,
                message: quest.name,
                type: type === 'started' ? 'info' : 'success',
                icon,
                duration: 4000
            });
        }
    }
    
    showUnlockNotification(type, id) {
        if (window.notificationSystem) {
            const typeNames = {
                building: 'Building',
                character: 'Character',
                feature: 'Feature'
            };
            
            const icons = {
                building: '🏢',
                character: '👤',
                feature: '⭐'
            };
            
            notificationSystem.special(
                `${typeNames[type]} Unlocked!`,
                `${id.replace(/_/g, ' ')} is now available`,
                {
                    icon: icons[type],
                    duration: 5000
                }
            );
        }
    }
    
    showChapterNotification(chapter) {
        if (window.notificationSystem) {
            notificationSystem.special(
                'New Chapter!',
                `Chapter ${chapter} has begun! New quests and content available.`,
                {
                    icon: '📖',
                    duration: 6000
                }
            );
        }
    }
    
    showDialogue(text, character) {
        // This would integrate with a dialogue system
        console.log(`${character}: ${text}`);
        
        if (window.notificationSystem) {
            notificationSystem.info(
                character.charAt(0).toUpperCase() + character.slice(1),
                text,
                {
                    icon: '💬',
                    duration: 5000
                }
            );
        }
    }
    
    updateUnlockUI() {
        // Trigger UI updates for building and character menus
        this.emit('unlocksUpdated', {
            buildings: Array.from(this.unlockedBuildings),
            characters: Array.from(this.unlockedCharacters),
            features: Array.from(this.unlockedFeatures)
        });
    }
    
    // Save/Load state
    getState() {
        return {
            activeQuests: Array.from(this.activeQuests.entries()),
            completedQuests: Array.from(this.completedQuests),
            unlockedBuildings: Array.from(this.unlockedBuildings),
            unlockedCharacters: Array.from(this.unlockedCharacters),
            unlockedFeatures: Array.from(this.unlockedFeatures),
            currentStoryChapter: this.currentStoryChapter,
            storyProgress: this.storyProgress,
            tutorialCompleted: this.tutorialCompleted
        };
    }
    
    setState(state) {
        if (state.activeQuests) {
            this.activeQuests = new Map(state.activeQuests);
        }
        if (state.completedQuests) {
            this.completedQuests = new Set(state.completedQuests);
        }
        if (state.unlockedBuildings) {
            this.unlockedBuildings = new Set(state.unlockedBuildings);
        }
        if (state.unlockedCharacters) {
            this.unlockedCharacters = new Set(state.unlockedCharacters);
        }
        if (state.unlockedFeatures) {
            this.unlockedFeatures = new Set(state.unlockedFeatures);
        }
        
        this.currentStoryChapter = state.currentStoryChapter || 1;
        this.storyProgress = state.storyProgress || 0;
        this.tutorialCompleted = state.tutorialCompleted || false;
    }
    
    dispose() {
        this.removeAllListeners();
    }
}
