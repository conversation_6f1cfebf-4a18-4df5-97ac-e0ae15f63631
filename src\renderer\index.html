<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Springfield Town Builder</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/ui.css">
    <!-- <link rel="icon" href="../../assets/icons/donut-icon.png"> -->
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-donut">🍩</div>
            <h1>Springfield Town Builder</h1>
            <div class="loading-bar">
                <div class="loading-progress" id="loading-progress"></div>
            </div>
            <p id="loading-text">Loading Springfield...</p>
        </div>
    </div>

    <!-- Main Menu -->
    <div id="main-menu" class="main-menu hidden">
        <div class="menu-background">
            <div class="menu-content">
                <h1 class="game-title">
                    <span class="title-donut">🍩</span>
                    Springfield Town Builder
                </h1>
                <div class="menu-buttons">
                    <button id="new-game-btn" class="menu-button">New Game</button>
                    <button id="load-game-btn" class="menu-button">Load Game</button>
                    <button id="multiplayer-btn" class="menu-button">Multiplayer</button>
                    <button id="settings-btn" class="menu-button">Settings</button>
                    <button id="exit-btn" class="menu-button">Exit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Save Slot Selection -->
    <div id="save-slot-menu" class="save-slot-menu hidden">
        <div class="menu-background">
            <div class="menu-content">
                <h2>Select Save Slot</h2>
                <div class="save-slots" id="save-slots">
                    <!-- Save slots will be populated dynamically -->
                </div>
                <button id="back-to-menu-btn" class="menu-button">Back to Menu</button>
            </div>
        </div>
    </div>

    <!-- Game Container -->
    <div id="game-container" class="game-container hidden">
        <!-- 3D Game Canvas -->
        <canvas id="game-canvas" class="game-canvas"></canvas>
        
        <!-- UI Overlay -->
        <div class="ui-overlay">
            <!-- Top UI Bar -->
            <div class="top-ui-bar">
                <div class="currency-display">
                    <div class="currency-item">
                        <span class="currency-icon">💰</span>
                        <span id="money-amount">1000</span>
                    </div>
                    <div class="currency-item donut-currency">
                        <span class="currency-icon">🍩</span>
                        <span id="donut-amount">50</span>
                    </div>
                    <div class="currency-item">
                        <span class="currency-icon">⭐</span>
                        <span id="xp-amount">0</span>
                    </div>
                </div>
                <div class="town-info">
                    <h3 id="town-name">Springfield</h3>
                    <div class="level-display">Level <span id="town-level">1</span></div>
                </div>
                <div class="top-buttons">
                    <button id="menu-btn" class="ui-button">Menu</button>
                    <button id="friends-btn" class="ui-button">Friends</button>
                    <button id="store-btn" class="ui-button">Store</button>
                </div>
            </div>

            <!-- Building Menu (Bottom Left) -->
            <div class="building-menu">
                <button id="build-menu-toggle" class="build-menu-button">
                    🏗️ Build
                </button>
                <div class="building-panel" id="building-panel" style="display: none;">
                    <div class="building-panel-header">
                        <h3>🏗️ Buildings</h3>
                        <button class="close-panel-btn" id="close-building-panel">×</button>
                    </div>
                    <div class="building-search">
                        <input type="text" id="building-search" placeholder="🔍 Search buildings..." />
                    </div>
                    <div class="building-categories-tabs">
                        <button class="category-tab active" data-category="all">All</button>
                        <button class="category-tab" data-category="residential">🏠</button>
                        <button class="category-tab" data-category="commercial">🏪</button>
                        <button class="category-tab" data-category="entertainment">🎪</button>
                        <button class="category-tab" data-category="roads">🛣️</button>
                        <button class="category-tab" data-category="decorations">🌳</button>
                    </div>
                    <div class="building-buttons" id="building-buttons">
                        <!-- Building buttons will be populated dynamically -->
                    </div>
                </div>
            </div>

            <!-- Task Panel (Bottom Right) -->
            <div class="task-panel">
                <div class="task-header">
                    <h4>Tasks</h4>
                    <span class="task-count" id="task-count">0</span>
                </div>
                <div class="task-list" id="task-list">
                    <!-- Tasks will be populated dynamically -->
                </div>
                <div class="task-buttons">
                    <button id="collect-all-btn" class="task-button">Collect All</button>
                    <button id="assign-tasks-btn" class="task-button">Assign Tasks</button>
                </div>
            </div>

            <!-- Character Panel -->
            <div class="character-panel hidden" id="character-panel">
                <div class="character-info">
                    <img id="character-avatar" src="" alt="Character">
                    <h4 id="character-name"></h4>
                    <p id="character-status"></p>
                </div>
                <div class="character-tasks" id="character-tasks">
                    <!-- Character-specific tasks -->
                </div>
                <button id="close-character-panel" class="ui-button">Close</button>
            </div>

            <!-- Building Info Panel -->
            <div class="building-info-panel hidden" id="building-info-panel">
                <div class="building-info">
                    <img id="building-image" src="" alt="Building">
                    <h4 id="building-name"></h4>
                    <p id="building-description"></p>
                </div>
                <div class="building-actions">
                    <button id="upgrade-building-btn" class="ui-button">Upgrade</button>
                    <button id="move-building-btn" class="ui-button">Move</button>
                    <button id="store-building-btn" class="ui-button">Store</button>
                </div>
                <button id="close-building-panel" class="ui-button">Close</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- Three.js Local -->
    <script src="../../assets/js/three.min.js"></script>

    <!-- Game Scripts -->
    <script src="js/utils/EventEmitter.js"></script>
    <script src="js/engine/Engine3D.js"></script>
    <script src="js/engine/SceneManager.js"></script>
    <script src="js/engine/CameraController.js"></script>
    <script src="js/engine/PostProcessing.js"></script>
    <script src="js/engine/DayNightCycle.js"></script>
    <script src="js/systems/SaveSystem.js"></script>
    <script src="js/systems/CurrencySystem.js"></script>
    <script src="js/systems/BuildingSystem.js"></script>
    <script src="js/systems/CharacterSystem.js"></script>
    <script src="js/systems/TaskSystem.js"></script>
    <script src="js/systems/QuestSystem.js"></script>
    <script src="js/systems/MultiplayerSystem.js"></script>
    <script src="js/ui/UIManager.js"></script>
    <script src="js/ui/BuildingMenu.js"></script>
    <script src="js/ui/TaskPanel.js"></script>
    <script src="js/ui/MenuSystem.js"></script>
    <script src="js/ui/EffectsPanel.js"></script>
    <script src="js/ui/RealTimeStatus.js"></script>
    <script src="js/ui/QuestPanel.js"></script>
    <script src="js/ui/NotificationSystem.js"></script>
    <script src="js/demo/RealTimeDemo.js"></script>
    <script src="js/demo/StorylineDemo.js"></script>
    <script src="js/Game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
