/* Springfield Town Builder - UI Styles */

.ui-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 100;
}

.ui-overlay > * {
    pointer-events: auto;
}

/* Top UI Bar */
.top-ui-bar {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    height: 60px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 30px;
    border: 3px solid #FFD700;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.currency-display {
    display: flex;
    gap: 20px;
}

.currency-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.8);
    padding: 8px 15px;
    border-radius: 20px;
    border: 2px solid #E0E0E0;
    font-weight: bold;
    font-size: 1.1em;
}

.currency-item.donut-currency {
    border-color: #FF6B35;
    background: linear-gradient(135deg, #FFE0B2 0%, #FFCC80 100%);
}

.currency-icon {
    font-size: 1.3em;
}

.town-info {
    text-align: center;
}

.town-info h3 {
    font-size: 1.4em;
    color: #FF6B35;
    margin-bottom: 5px;
}

.level-display {
    font-size: 0.9em;
    color: #666;
    font-weight: bold;
}

.top-buttons {
    display: flex;
    gap: 10px;
}

.ui-button {
    padding: 10px 20px;
    font-family: inherit;
    font-size: 1em;
    background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
    border: 2px solid #2E7D32;
    border-radius: 20px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.ui-button:hover {
    background: linear-gradient(135deg, #45A049 0%, #388E3C 100%);
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.3);
}

.ui-button:active {
    transform: translateY(0);
}

/* Building Menu (Bottom Left) */
.building-menu {
    position: absolute;
    bottom: 20px;
    left: 20px;
}

.build-menu-button {
    padding: 15px 25px;
    font-size: 1.2em;
    font-family: inherit;
    background: linear-gradient(135deg, #FF6B35 0%, #FF8C42 100%);
    border: 3px solid #E55A2B;
    border-radius: 25px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.build-menu-button:hover {
    background: linear-gradient(135deg, #FF8C42 0%, #FFA500 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.build-menu-button:active {
    transform: translateY(0);
}

.building-panel {
    position: absolute;
    bottom: 70px;
    left: 0;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 15px;
    border: 3px solid #FFD700;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    width: 450px;
    max-height: 500px;
    z-index: 1000;
}

.building-categories {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    justify-content: center;
}

.category-btn {
    width: 50px;
    height: 50px;
    border: 2px solid #E0E0E0;
    border-radius: 25px;
    background: white;
    font-size: 1.5em;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-btn:hover {
    border-color: #FFD700;
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
    background: linear-gradient(135deg, #ffffff 0%, #fff8dc 100%);
}

.category-btn.active {
    border-color: #FF6B35;
    background: linear-gradient(135deg, #FFE0B2 0%, #FFCC80 100%);
    transform: scale(1.1);
}

.building-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;
}

.building-btn {
    width: 80px;
    height: 80px;
    border: 2px solid #E0E0E0;
    border-radius: 10px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 5px;
    position: relative;
}

.building-btn:hover {
    border-color: #FFD700;
    transform: scale(1.05) translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
    background: linear-gradient(135deg, #ffffff 0%, #fff8dc 100%);
}

.building-btn img {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.building-btn .building-name {
    font-size: 0.7em;
    text-align: center;
    margin-top: 2px;
    color: #333;
    font-weight: bold;
}

.building-btn .building-cost {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background: rgba(255, 215, 0, 0.9);
    color: #8B4513;
    font-size: 0.6em;
    padding: 1px 4px;
    border-radius: 8px;
    font-weight: bold;
}

.building-btn.donut-cost .building-cost {
    background: rgba(255, 107, 53, 0.9);
    color: white;
}

/* Task Panel (Bottom Right) */
.task-panel {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    border: 3px solid #4CAF50;
    padding: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Panel Tabs */
.panel-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 2px solid #E0E0E0;
}

.tab-btn {
    flex: 1;
    padding: 8px 12px;
    background: none;
    border: none;
    cursor: pointer;
    font-family: inherit;
    font-weight: bold;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: #4CAF50;
    border-bottom-color: #4CAF50;
}

.tab-btn:hover {
    color: #4CAF50;
}

.tab-content {
    display: block;
}

.tab-content.hidden {
    display: none;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #E0E0E0;
}

.task-header h4 {
    font-size: 1.3em;
    color: #4CAF50;
}

.task-count {
    background: #4CAF50;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.9em;
    font-weight: bold;
}

.task-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    margin-bottom: 8px;
    background: rgba(76, 175, 80, 0.1);
    border-radius: 10px;
    border: 1px solid #C8E6C9;
    cursor: pointer;
    transition: all 0.3s ease;
}

.task-item:hover {
    background: rgba(76, 175, 80, 0.2);
    transform: translateX(5px) scale(1.02);
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.task-item.completed {
    background: rgba(255, 193, 7, 0.2);
    border-color: #FFD54F;
}

.task-character-avatar {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    border: 2px solid #4CAF50;
}

.task-info {
    flex: 1;
}

.task-character-name {
    font-weight: bold;
    color: #2E7D32;
    font-size: 0.9em;
}

.task-description {
    font-size: 0.8em;
    color: #666;
}

.task-progress {
    text-align: right;
    font-size: 0.8em;
}

.task-time {
    color: #FF6B35;
    font-weight: bold;
}

.task-reward {
    color: #4CAF50;
    font-weight: bold;
}

.task-buttons {
    display: flex;
    gap: 10px;
}

.task-button {
    flex: 1;
    padding: 10px;
    font-family: inherit;
    font-size: 0.9em;
    background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
    border: 2px solid #2E7D32;
    border-radius: 15px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.task-button:hover {
    background: linear-gradient(135deg, #45A049 0%, #388E3C 100%);
    transform: translateY(-1px);
}

/* Character List */
.character-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.character-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    margin-bottom: 8px;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 10px;
    border: 1px solid #FFD54F;
    cursor: pointer;
    transition: all 0.3s ease;
}

.character-item:hover {
    background: rgba(255, 193, 7, 0.2);
    transform: translateX(5px);
}

.character-item.locked {
    background: rgba(158, 158, 158, 0.1);
    border-color: #BDBDBD;
    opacity: 0.6;
}

.character-avatar {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    border: 2px solid #FFD700;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
}

.character-info {
    flex: 1;
}

.character-name {
    font-weight: bold;
    color: #FF6B35;
    font-size: 0.9em;
}

.character-status {
    font-size: 0.8em;
    color: #666;
}

.character-cost {
    text-align: right;
    font-size: 0.8em;
    font-weight: bold;
    color: #4CAF50;
}

.character-cost.donut-cost {
    color: #FF6B35;
}

.character-count {
    background: #FFD700;
    color: #8B4513;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.9em;
    font-weight: bold;
}

/* Character Panel */
.character-panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 20px;
    border: 3px solid #FF6B35;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 200;
}

.character-info {
    text-align: center;
    margin-bottom: 20px;
}

.character-info img {
    width: 80px;
    height: 80px;
    border-radius: 40px;
    border: 3px solid #FF6B35;
    margin-bottom: 10px;
}

.character-info h4 {
    font-size: 1.5em;
    color: #FF6B35;
    margin-bottom: 5px;
}

.character-info p {
    color: #666;
    font-style: italic;
}

/* Building Info Panel */
.building-info-panel {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 350px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 20px;
    border: 3px solid #2196F3;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 200;
}

.building-info {
    text-align: center;
    margin-bottom: 20px;
}

.building-info img {
    width: 100px;
    height: 100px;
    object-fit: contain;
    margin-bottom: 10px;
}

.building-info h4 {
    font-size: 1.4em;
    color: #2196F3;
    margin-bottom: 10px;
}

.building-info p {
    color: #666;
    font-size: 0.9em;
    line-height: 1.4;
}

.building-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(224, 224, 224, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #FFD700;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #FFA500;
}

/* Effects Panel */
.effects-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    max-height: 80vh;
    background: rgba(255, 255, 255, 0.95);
    border: 3px solid #FFD700;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    overflow-y: auto;
    backdrop-filter: blur(10px);
}

.effects-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border-radius: 12px 12px 0 0;
    margin: -3px -3px 0 -3px;
}

.effects-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.effects-content {
    padding: 20px;
}

.effects-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #E0E0E0;
}

.effects-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.effects-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: bold;
}

.time-display {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    color: #FFD700;
    background: #333;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 15px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.control-group input[type="range"] {
    width: 100%;
    margin-bottom: 5px;
}

.control-group input[type="checkbox"] {
    margin-right: 8px;
}

.time-presets, .weather-buttons, .camera-buttons, .preset-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
    margin-top: 10px;
}

.preset-btn, .weather-btn, .effect-btn {
    padding: 8px 12px;
    border: 2px solid #DDD;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    text-align: center;
}

.preset-btn:hover, .weather-btn:hover, .effect-btn:hover {
    border-color: #FFD700;
    background: #FFF8DC;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.weather-btn.active {
    background: #FFD700;
    border-color: #FFA500;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.camera-buttons {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
}

.preset-buttons {
    grid-template-columns: repeat(2, 1fr);
}

/* Range slider styling */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: #DDD;
    border-radius: 3px;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #FFD700;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #FFD700;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .building-menu {
        width: 100%;
        height: 200px;
        bottom: 0;
        left: 0;
        border-radius: 0;
    }

    .task-panel {
        width: 100%;
        height: 150px;
        top: 0;
        right: 0;
        border-radius: 0;
    }

    .building-btn, .category-btn {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }

    .effects-panel {
        width: 95%;
        max-height: 90vh;
    }

    .time-presets, .weather-buttons, .camera-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* New Building Panel Styles */
.building-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #FFD700;
}

.building-panel-header h3 {
    margin: 0;
    color: #FF6B35;
    font-size: 1.3em;
}

.close-panel-btn {
    background: #FF6B35;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    color: white;
    font-size: 1.2em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-panel-btn:hover {
    background: #E55A2B;
    transform: scale(1.1);
}

.building-search {
    margin-bottom: 15px;
}

.building-search input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #E0E0E0;
    border-radius: 20px;
    font-size: 1em;
    outline: none;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.building-search input:focus {
    border-color: #FFD700;
}

.building-categories-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.category-tab {
    padding: 8px 12px;
    border: 2px solid #E0E0E0;
    border-radius: 15px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
    font-weight: bold;
    color: #666;
}

.category-tab:hover {
    border-color: #FFD700;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.category-tab.active {
    border-color: #FF6B35;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(255, 107, 53, 0.4);
}
