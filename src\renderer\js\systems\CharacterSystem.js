/**
 * Springfield Town Builder - Character System
 * Handles character management, animations, and AI behavior
 */

class CharacterSystem extends EventEmitter {
    constructor(engine3D, buildingSystem, currencySystem) {
        super();
        this.engine3D = engine3D;
        this.buildingSystem = buildingSystem;
        this.currencySystem = currencySystem;
        
        // Character data and state
        this.characterData = null;
        this.activeCharacters = new Map();
        this.characterPaths = new Map();
        
        // Animation and movement
        this.walkSpeed = 1.0;
        this.animationMixer = null;
        
        this.init();
    }
    
    async init() {
        await this.loadCharacterData();
        this.setupEventListeners();
        console.log('Character System initialized');
    }
    
    async loadCharacterData() {
        try {
            const response = await fetch('../../data/characters.json');
            const data = await response.json();
            this.characterData = data.characters;
            console.log('Character data loaded:', Object.keys(this.characterData).length, 'characters');
        } catch (error) {
            console.error('Failed to load character data:', error);
            this.createFallbackCharacterData();
        }
    }

    createFallbackCharacterData() {
        this.characterData = {
            'homer': {
                id: 'homer',
                name: '<PERSON>',
                description: 'Nuclear safety inspector and beer enthusiast',
                personality: 'lazy',
                walkSpeed: 1.0,
                unlockLevel: 1,
                cost: { type: 'free' },
                tasks: [
                    {
                        id: 'drink_beer',
                        name: 'Drink Beer at Moe\'s',
                        description: 'Homer enjoys a cold Duff at Moe\'s Tavern',
                        duration: 300,
                        reward: { money: 50, xp: 10 },
                        building: 'moes_tavern'
                    },
                    {
                        id: 'eat_donuts',
                        name: 'Eat Donuts',
                        description: 'Homer indulges in his favorite treat',
                        duration: 180,
                        reward: { money: 30, xp: 8 },
                        building: 'simpson_house'
                    },
                    {
                        id: 'sleep',
                        name: 'Take a Nap',
                        description: 'Homer takes a well-deserved nap',
                        duration: 240,
                        reward: { money: 25, xp: 5 },
                        building: 'simpson_house'
                    }
                ]
            },
            'apu': {
                id: 'apu',
                name: 'Apu Nahasapeemapetilon',
                description: 'Hardworking Kwik-E-Mart owner',
                personality: 'hardworking',
                walkSpeed: 1.0,
                unlockLevel: 2,
                cost: { type: 'money', amount: 1500 },
                tasks: [
                    {
                        id: 'work_kwik_e_mart',
                        name: 'Work at Kwik-E-Mart',
                        description: 'Apu manages his convenience store',
                        duration: 480,
                        reward: { money: 120, xp: 20 },
                        building: 'kwik_e_mart'
                    },
                    {
                        id: 'restock_shelves',
                        name: 'Restock Shelves',
                        description: 'Apu restocks the Kwik-E-Mart inventory',
                        duration: 300,
                        reward: { money: 80, xp: 15 },
                        building: 'kwik_e_mart'
                    }
                ]
            }
        };
        console.log('Using fallback character data');
    }
    
    setupEventListeners() {
        // Listen for character clicks
        this.engine3D.on('characterClicked', (character) => {
            this.selectCharacter(character);
        });
        
        // Listen for building placement to assign characters
        this.buildingSystem.on('buildingPlaced', (event) => {
            this.onBuildingPlaced(event);
        });
        
        // Listen for engine updates for character animations
        this.engine3D.on('update', (deltaTime) => {
            this.updateCharacters(deltaTime);
        });
    }
    
    // Character creation and management
    createCharacter(characterType, position) {
        const characterData = this.characterData[characterType];
        if (!characterData) {
            console.error('Unknown character type:', characterType);
            return null;
        }
        
        const character = this.buildCharacterMesh(characterData);
        character.position.copy(position);
        
        // Add character data
        character.userData = {
            id: this.generateCharacterId(),
            type: characterType,
            name: characterData.name,
            personality: characterData.personality,
            walkSpeed: characterData.walkSpeed,
            currentTask: null,
            currentBuilding: null,
            isWalking: false,
            targetPosition: null,
            path: [],
            pathIndex: 0,
            idleTime: 0,
            lastTaskTime: 0,
            isCharacter: true
        };
        
        this.engine3D.addCharacter(character);
        this.activeCharacters.set(character.userData.id, character);
        
        // Start AI behavior
        this.startCharacterAI(character);
        
        this.emit('characterCreated', character);
        return character;
    }
    
    buildCharacterMesh(characterData) {
        // Create enhanced character representation with multiple parts
        const characterGroup = new THREE.Group();

        // Create character based on specific character type
        switch (characterData.name) {
            case 'Homer Simpson':
                return this.createHomerMesh();
            case 'Marge Simpson':
                return this.createMargeMesh();
            case 'Bart Simpson':
                return this.createBartMesh();
            case 'Lisa Simpson':
                return this.createLisaMesh();
            case 'Apu Nahasapeemapetilon':
                return this.createApuMesh();
            case 'Moe Szyslak':
                return this.createMoeMesh();
            case 'Comic Book Guy':
                return this.createComicBookGuyMesh();
            default:
                return this.createGenericCharacterMesh(characterData);
        }
    }

    createHomerMesh() {
        const group = new THREE.Group();

        // Body - larger, round
        const bodyGeometry = new THREE.CapsuleGeometry(0.4, 1.2, 4, 8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFD700,
            shininess: 20
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.6;
        group.add(body);

        // Head - larger and rounder
        const headGeometry = new THREE.SphereGeometry(0.35, 16, 12);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFD700,
            shininess: 30
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.5;
        group.add(head);

        // Bald spot indicator
        const baldGeometry = new THREE.SphereGeometry(0.15, 8, 6);
        const baldMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFE55C,
            shininess: 50
        });
        const bald = new THREE.Mesh(baldGeometry, baldMaterial);
        bald.position.set(0, 1.65, 0);
        group.add(bald);

        this.setupCharacterShadows(group);
        return group;
    }

    createMargeMesh() {
        const group = new THREE.Group();

        // Body - tall and slender
        const bodyGeometry = new THREE.CapsuleGeometry(0.3, 1.4, 4, 8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: 0x87CEEB,
            shininess: 25
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.7;
        group.add(body);

        // Head
        const headGeometry = new THREE.SphereGeometry(0.3, 16, 12);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFD700,
            shininess: 30
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.6;
        group.add(head);

        // Tall blue hair
        const hairGeometry = new THREE.CylinderGeometry(0.15, 0.25, 0.8, 8);
        const hairMaterial = new THREE.MeshPhongMaterial({
            color: 0x0000FF,
            shininess: 40
        });
        const hair = new THREE.Mesh(hairGeometry, hairMaterial);
        hair.position.y = 2.2;
        group.add(hair);

        this.setupCharacterShadows(group);
        return group;
    }

    createBartMesh() {
        const group = new THREE.Group();

        // Body - smaller, child-like
        const bodyGeometry = new THREE.CapsuleGeometry(0.25, 1.0, 4, 8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: 0xFF6347,
            shininess: 20
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.5;
        group.add(body);

        // Head
        const headGeometry = new THREE.SphereGeometry(0.25, 16, 12);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFD700,
            shininess: 30
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.2;
        group.add(head);

        // Spiky hair
        for (let i = 0; i < 6; i++) {
            const spikeGeometry = new THREE.ConeGeometry(0.05, 0.2, 4);
            const spikeMaterial = new THREE.MeshPhongMaterial({
                color: 0xFFD700,
                shininess: 20
            });
            const spike = new THREE.Mesh(spikeGeometry, spikeMaterial);
            const angle = (i / 6) * Math.PI * 2;
            spike.position.set(
                Math.cos(angle) * 0.2,
                1.4,
                Math.sin(angle) * 0.2
            );
            group.add(spike);
        }

        this.setupCharacterShadows(group);
        return group;
    }

    createLisaMesh() {
        const group = new THREE.Group();

        // Body - child-like but slightly taller than Bart
        const bodyGeometry = new THREE.CapsuleGeometry(0.25, 1.1, 4, 8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: 0x9370DB,
            shininess: 25
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.55;
        group.add(body);

        // Head
        const headGeometry = new THREE.SphereGeometry(0.25, 16, 12);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFD700,
            shininess: 30
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.25;
        group.add(head);

        // Star-shaped hair
        const hairGeometry = new THREE.CylinderGeometry(0.1, 0.2, 0.3, 8);
        const hairMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFD700,
            shininess: 25
        });
        const hair = new THREE.Mesh(hairGeometry, hairMaterial);
        hair.position.y = 1.5;
        group.add(hair);

        // Saxophone (Lisa's signature item)
        const saxGeometry = new THREE.CylinderGeometry(0.03, 0.05, 0.3, 6);
        const saxMaterial = new THREE.MeshPhongMaterial({
            color: 0xDAA520,
            shininess: 80
        });
        const sax = new THREE.Mesh(saxGeometry, saxMaterial);
        sax.position.set(0.3, 0.8, 0);
        sax.rotation.z = Math.PI / 4;
        group.add(sax);

        this.setupCharacterShadows(group);
        return group;
    }

    createApuMesh() {
        const group = new THREE.Group();

        // Body - standard height
        const bodyGeometry = new THREE.CapsuleGeometry(0.3, 1.3, 4, 8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: 0x32CD32,
            shininess: 25
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.65;
        group.add(body);

        // Head
        const headGeometry = new THREE.SphereGeometry(0.3, 16, 12);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xDEB887,
            shininess: 30
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.5;
        group.add(head);

        // Turban
        const turbanGeometry = new THREE.SphereGeometry(0.32, 16, 8);
        const turbanMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFFFFF,
            shininess: 20
        });
        const turban = new THREE.Mesh(turbanGeometry, turbanMaterial);
        turban.position.y = 1.6;
        turban.scale.y = 0.6;
        group.add(turban);

        this.setupCharacterShadows(group);
        return group;
    }

    createMoeMesh() {
        const group = new THREE.Group();

        // Body - slightly hunched
        const bodyGeometry = new THREE.CapsuleGeometry(0.35, 1.2, 4, 8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: 0x8B4513,
            shininess: 20
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.6;
        body.rotation.x = 0.1; // Slight hunch
        group.add(body);

        // Head - slightly larger
        const headGeometry = new THREE.SphereGeometry(0.32, 16, 12);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFD700,
            shininess: 30
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.45;
        group.add(head);

        // Balding hair on sides
        const hairGeometry = new THREE.SphereGeometry(0.1, 8, 6);
        const hairMaterial = new THREE.MeshPhongMaterial({
            color: 0x8B4513,
            shininess: 15
        });
        const leftHair = new THREE.Mesh(hairGeometry, hairMaterial);
        leftHair.position.set(-0.25, 1.55, 0);
        group.add(leftHair);

        const rightHair = leftHair.clone();
        rightHair.position.set(0.25, 1.55, 0);
        group.add(rightHair);

        this.setupCharacterShadows(group);
        return group;
    }

    createComicBookGuyMesh() {
        const group = new THREE.Group();

        // Body - larger, overweight
        const bodyGeometry = new THREE.CapsuleGeometry(0.45, 1.1, 4, 8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: 0xFF69B4,
            shininess: 20
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.55;
        group.add(body);

        // Head
        const headGeometry = new THREE.SphereGeometry(0.32, 16, 12);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFD700,
            shininess: 30
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.4;
        group.add(head);

        // Ponytail
        const ponytailGeometry = new THREE.CylinderGeometry(0.05, 0.08, 0.3, 6);
        const ponytailMaterial = new THREE.MeshPhongMaterial({
            color: 0x8B4513,
            shininess: 15
        });
        const ponytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
        ponytail.position.set(0, 1.5, -0.3);
        group.add(ponytail);

        this.setupCharacterShadows(group);
        return group;
    }

    createGenericCharacterMesh(characterData) {
        const group = new THREE.Group();

        // Character colors based on personality
        const personalityColors = {
            lazy: 0xFFD700,      // Homer - yellow
            caring: 0x87CEEB,    // Marge - blue
            mischievous: 0xFF6347, // Bart - red
            intelligent: 0x9370DB, // Lisa - purple
            hardworking: 0x32CD32, // Apu - green
            gruff: 0x8B4513,     // Moe - brown
            sarcastic: 0xFF69B4   // Comic Book Guy - pink
        };

        const color = personalityColors[characterData.personality] || 0xFFFFFF;

        // Body
        const bodyGeometry = new THREE.CapsuleGeometry(0.3, 1.3, 4, 8);
        const bodyMaterial = new THREE.MeshPhongMaterial({
            color: color,
            shininess: 25
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.65;
        group.add(body);

        // Head
        const headGeometry = new THREE.SphereGeometry(0.3, 16, 12);
        const headMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFD700,
            shininess: 30
        });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.5;
        group.add(head);

        // Simple face indicator
        const faceGeometry = new THREE.SphereGeometry(0.08, 8, 6);
        const faceMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
        const face = new THREE.Mesh(faceGeometry, faceMaterial);
        face.position.set(0, 1.55, 0.25);
        group.add(face);

        this.setupCharacterShadows(group);
        return group;
    }

    setupCharacterShadows(group) {
        group.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });
    }

    // Character AI and behavior
    startCharacterAI(character) {
        const aiInterval = setInterval(() => {
            if (!this.activeCharacters.has(character.userData.id)) {
                clearInterval(aiInterval);
                return;
            }
            
            this.updateCharacterAI(character);
        }, 2000); // Update AI every 2 seconds
        
        character.userData.aiInterval = aiInterval;
    }
    
    updateCharacterAI(character) {
        const userData = character.userData;
        
        // If character has a current task, don't interrupt
        if (userData.currentTask) return;
        
        // Increase idle time
        userData.idleTime += 2;
        
        // If character has been idle for too long, find something to do
        if (userData.idleTime > 10) {
            this.assignRandomTask(character);
            userData.idleTime = 0;
        } else if (userData.idleTime > 5 && !userData.isWalking) {
            // Start wandering around
            this.startWandering(character);
        }
    }
    
    assignRandomTask(character) {
        console.log('Assigning random task to:', character.userData.name);

        const characterData = this.characterData[character.userData.type];
        console.log('Character data:', characterData);

        if (!characterData || !characterData.tasks || characterData.tasks.length === 0) {
            console.log('No tasks available for character');
            return;
        }

        console.log('Available tasks for character:', characterData.tasks);

        // Filter available tasks based on buildings
        const availableTasks = characterData.tasks.filter(task => {
            if (!task.building) return true; // Tasks that don't require buildings

            // Check if the required building exists
            for (const [id, building] of this.buildingSystem.placedBuildings) {
                if (building.userData.type === task.building) {
                    return true;
                }
            }
            return false;
        });

        console.log('Filtered available tasks:', availableTasks);

        if (availableTasks.length === 0) {
            console.log('No available tasks after filtering');
            return;
        }

        // Select a random task
        const randomTask = availableTasks[Math.floor(Math.random() * availableTasks.length)];
        console.log('Selected task:', randomTask);
        this.assignTask(character, randomTask);
    }
    
    assignTask(character, task) {
        character.userData.currentTask = task;
        character.userData.lastTaskTime = Date.now();
        
        // If task requires a building, move to it
        if (task.building) {
            const targetBuilding = this.findBuildingByType(task.building);
            if (targetBuilding) {
                character.userData.currentBuilding = targetBuilding;
                this.moveCharacterToBuilding(character, targetBuilding);
            }
        } else {
            // Start task immediately
            this.startTask(character, task);
        }
        
        this.emit('taskAssigned', { character, task });
    }
    
    startTask(character, task) {
        console.log(`${character.userData.name} started task: ${task.name}`);
        
        // Set task duration
        const taskDuration = task.duration * 1000; // Convert to milliseconds
        
        // Start task animation
        this.playTaskAnimation(character, task.animation);
        
        // Complete task after duration
        setTimeout(() => {
            this.completeTask(character, task);
        }, taskDuration);
        
        this.emit('taskStarted', { character, task });
    }
    
    completeTask(character, task) {
        // Award rewards
        if (task.reward.money) {
            this.currencySystem.addMoney(task.reward.money, `task_${task.id}`);
        }
        if (task.reward.xp) {
            this.currencySystem.addXP(task.reward.xp, `task_${task.id}`);
        }
        
        // Clear task
        character.userData.currentTask = null;
        character.userData.currentBuilding = null;
        
        // Stop task animation
        this.stopTaskAnimation(character);
        
        console.log(`${character.userData.name} completed task: ${task.name}`);
        this.emit('taskCompleted', { character, task });
    }
    
    // Character movement
    moveCharacterToBuilding(character, building) {
        const targetPosition = building.position.clone();
        targetPosition.x += (Math.random() - 0.5) * 4; // Random offset around building
        targetPosition.z += (Math.random() - 0.5) * 4;
        targetPosition.y = 0;
        
        this.moveCharacterTo(character, targetPosition);
    }
    
    moveCharacterTo(character, targetPosition) {
        character.userData.targetPosition = targetPosition.clone();
        character.userData.isWalking = true;
        
        // Calculate path (simple direct path for now)
        const path = [character.position.clone(), targetPosition.clone()];
        character.userData.path = path;
        character.userData.pathIndex = 0;
        
        this.playWalkAnimation(character);
    }
    
    startWandering(character) {
        // Pick a random nearby position to wander to
        const wanderRadius = 5;
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.random() * wanderRadius;
        
        const targetPosition = new THREE.Vector3(
            character.position.x + Math.cos(angle) * distance,
            0,
            character.position.z + Math.sin(angle) * distance
        );
        
        // Keep within bounds
        targetPosition.x = Math.max(-40, Math.min(40, targetPosition.x));
        targetPosition.z = Math.max(-40, Math.min(40, targetPosition.z));
        
        this.moveCharacterTo(character, targetPosition);
    }
    
    updateCharacters(deltaTime) {
        for (const [id, character] of this.activeCharacters) {
            this.updateCharacterMovement(character, deltaTime);
        }
    }
    
    updateCharacterMovement(character, deltaTime) {
        const userData = character.userData;
        
        if (!userData.isWalking || !userData.targetPosition) return;
        
        const currentPos = character.position;
        const targetPos = userData.targetPosition;
        const distance = currentPos.distanceTo(targetPos);
        
        if (distance < 0.1) {
            // Reached target
            userData.isWalking = false;
            userData.targetPosition = null;
            this.stopWalkAnimation(character);
            
            // If character was moving to a building for a task, start the task
            if (userData.currentTask && userData.currentBuilding) {
                this.startTask(character, userData.currentTask);
            }
        } else {
            // Move towards target
            const direction = new THREE.Vector3()
                .subVectors(targetPos, currentPos)
                .normalize();
            
            const moveDistance = userData.walkSpeed * deltaTime;
            const movement = direction.multiplyScalar(moveDistance);
            
            character.position.add(movement);
            
            // Face movement direction
            character.lookAt(targetPos);
        }
    }
    
    // Animation methods (simplified)
    playWalkAnimation(character) {
        // Simple walking animation - bob up and down
        if (!character.userData.walkAnimation) {
            const animate = () => {
                if (character.userData.isWalking) {
                    const time = Date.now() * 0.01;
                    character.position.y = Math.sin(time) * 0.1;
                    requestAnimationFrame(animate);
                } else {
                    character.position.y = 0;
                }
            };
            character.userData.walkAnimation = animate;
            animate();
        }
    }
    
    stopWalkAnimation(character) {
        character.userData.walkAnimation = null;
        character.position.y = 0;
    }
    
    playTaskAnimation(character, animationType) {
        // Simple task animations
        switch (animationType) {
            case 'working':
                this.playWorkingAnimation(character);
                break;
            case 'drinking':
                this.playDrinkingAnimation(character);
                break;
            case 'eating':
                this.playEatingAnimation(character);
                break;
            default:
                this.playIdleAnimation(character);
        }
    }
    
    playWorkingAnimation(character) {
        const animate = () => {
            if (character.userData.currentTask) {
                const time = Date.now() * 0.005;
                character.rotation.y = Math.sin(time) * 0.1;
                requestAnimationFrame(animate);
            } else {
                character.rotation.y = 0;
            }
        };
        animate();
    }
    
    playDrinkingAnimation(character) {
        // Tilt character slightly as if drinking
        character.rotation.z = 0.1;
    }
    
    playEatingAnimation(character) {
        // Simple eating animation
        const animate = () => {
            if (character.userData.currentTask) {
                const time = Date.now() * 0.01;
                character.scale.y = 1 + Math.sin(time) * 0.05;
                requestAnimationFrame(animate);
            } else {
                character.scale.y = 1;
            }
        };
        animate();
    }
    
    playIdleAnimation(character) {
        // Gentle swaying
        const animate = () => {
            if (!character.userData.currentTask && !character.userData.isWalking) {
                const time = Date.now() * 0.002;
                character.rotation.y = Math.sin(time) * 0.05;
                requestAnimationFrame(animate);
            }
        };
        animate();
    }
    
    stopTaskAnimation(character) {
        // Reset character transformations
        character.rotation.set(0, 0, 0);
        character.scale.set(1, 1, 1);
    }
    
    // Utility methods
    findBuildingByType(buildingType) {
        for (const [id, building] of this.buildingSystem.placedBuildings) {
            if (building.userData.type === buildingType) {
                return building;
            }
        }
        return null;
    }
    
    generateCharacterId() {
        return 'character_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    selectCharacter(character) {
        this.emit('characterSelected', character);
    }
    
    onBuildingPlaced(event) {
        // When certain buildings are placed, automatically create associated characters
        const buildingType = event.type;
        const position = event.position.clone();
        position.x += 2; // Offset from building
        
        const characterMappings = {
            'kwik_e_mart': 'apu',
            'moes_tavern': 'moe',
            'androids_dungeon': 'comic_book_guy'
        };
        
        const characterType = characterMappings[buildingType];
        if (characterType && this.characterData[characterType]) {
            // Check if character is already unlocked/affordable
            const characterData = this.characterData[characterType];
            if (this.currencySystem.getLevel() >= characterData.unlockLevel) {
                this.createCharacter(characterType, position);
            }
        }
    }
    
    // Save/Load state
    getState() {
        const characters = [];
        
        for (const [id, character] of this.activeCharacters) {
            characters.push({
                id: id,
                type: character.userData.type,
                position: {
                    x: character.position.x,
                    y: character.position.y,
                    z: character.position.z
                },
                currentTask: character.userData.currentTask,
                lastTaskTime: character.userData.lastTaskTime
            });
        }
        
        return { characters };
    }
    
    setState(state) {
        // Clear existing characters
        for (const [id, character] of this.activeCharacters) {
            this.removeCharacter(id);
        }
        
        // Recreate characters from state
        if (state.characters) {
            state.characters.forEach(characterState => {
                const position = new THREE.Vector3(
                    characterState.position.x,
                    characterState.position.y,
                    characterState.position.z
                );
                
                const character = this.createCharacter(characterState.type, position);
                if (character) {
                    character.userData.id = characterState.id;
                    character.userData.currentTask = characterState.currentTask;
                    character.userData.lastTaskTime = characterState.lastTaskTime;
                    
                    this.activeCharacters.set(characterState.id, character);
                }
            });
        }
    }
    
    removeCharacter(characterId) {
        const character = this.activeCharacters.get(characterId);
        if (!character) return false;
        
        // Stop AI
        if (character.userData.aiInterval) {
            clearInterval(character.userData.aiInterval);
        }
        
        // Remove from scene
        this.engine3D.removeCharacter(character);
        this.activeCharacters.delete(characterId);
        
        this.emit('characterRemoved', character);
        return true;
    }
    
    dispose() {
        // Clean up all characters
        for (const [id, character] of this.activeCharacters) {
            if (character.userData.aiInterval) {
                clearInterval(character.userData.aiInterval);
            }
        }
        
        this.removeAllListeners();
    }
}
