{"version": "1.0.0", "lastSaved": "2025-06-13T08:19:48.471Z", "townName": "Springfield 1", "level": 1, "money": 1000, "donuts": 50, "xp": 0, "buildings": [{"id": "building_1749802737694_a6lssdiog", "type": "simpson_house", "position": {"x": 0, "y": 0, "z": 0}, "level": 1, "lastCollection": 1749802737694, "pendingIncome": 0}, {"id": "building_1749802737695_e0fskndms", "type": "kwik_e_mart", "position": {"x": 6, "y": 0, "z": -4}, "level": 1, "lastCollection": 1749802737695, "pendingIncome": 0}, {"id": "building_1749802737746_hck2hmq7e", "type": "simpson_house", "position": {"x": 0, "y": 0, "z": 0}, "level": 1, "lastCollection": 1749802737746, "pendingIncome": 0}, {"id": "building_1749802737746_u2gns8zlw", "type": "kwik_e_mart", "position": {"x": 6, "y": 0, "z": -4}, "level": 1, "lastCollection": 1749802737746, "pendingIncome": 0}], "characters": [{"id": "character_1749802738212_hlzth7lfy", "type": "homer", "position": {"x": 1.4399519504042488, "y": 0, "z": 1.1027038000655336}, "currentTask": {"id": "eat_donuts", "name": "Eat Donuts", "description": "<PERSON> indulges in his favorite treat", "duration": 180, "reward": {"money": 30, "xp": 8}, "building": "simpson_house", "animation": "eating"}, "lastTaskTime": 1749802750220}, {"id": "character_1749802738213_tk3x66ltw", "type": "apu", "position": {"x": 0.4017253043193503, "y": 0, "z": -1.9265215037309613}, "currentTask": {"id": "meditation", "name": "Meditation", "description": "<PERSON><PERSON> finds inner peace through meditation", "duration": 240, "reward": {"money": 40, "xp": 20}, "building": "simpson_house", "animation": "meditating"}, "lastTaskTime": 1749802750221}, {"id": "character_1749802738259_hphokjk6i", "type": "homer", "position": {"x": -1.0080963833402197, "y": 0, "z": 1.2650978786540654}, "currentTask": {"id": "sleep", "name": "Take a Nap", "description": "<PERSON> takes a well-deserved nap", "duration": 240, "reward": {"money": 25, "xp": 5}, "building": "simpson_house", "animation": "sleeping"}, "lastTaskTime": 1749802750271}, {"id": "character_1749802738260_1z0hxfwow", "type": "apu", "position": {"x": -1.662599332997797, "y": 0, "z": 0.0288166046830786}, "currentTask": {"id": "meditation", "name": "Meditation", "description": "<PERSON><PERSON> finds inner peace through meditation", "duration": 240, "reward": {"money": 40, "xp": 20}, "building": "simpson_house", "animation": "meditating"}, "lastTaskTime": 1749802750272}], "tasks": [], "settings": {"soundEnabled": true, "musicEnabled": true, "notifications": true, "autoSave": true}, "stats": {"timePlayed": 51.71799999999934, "buildingsBuilt": 0, "tasksCompleted": 0, "donutsSpent": 0}, "friends": [], "unlockedBuildings": ["simpson_house", "kwik_e_mart"], "unlockedCharacters": ["homer"], "completedQuests": []}