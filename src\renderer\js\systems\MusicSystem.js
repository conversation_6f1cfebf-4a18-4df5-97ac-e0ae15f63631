/**
 * Springfield Town Builder - Music System
 * Handles background music, ambient sounds, and audio management
 */

class MusicSystem extends EventEmitter {
    constructor() {
        super();

        // Detect if running in Electron
        this.isElectron = typeof window !== 'undefined' && window.electronAPI;

        // Audio context and nodes
        this.audioContext = null;
        this.masterGainNode = null;
        this.musicGainNode = null;
        this.ambientGainNode = null;

        // Music tracks
        this.musicTracks = new Map();
        this.currentTrack = null;
        this.currentAudio = null;
        this.isPlaying = false;
        this.isPaused = false;

        // Volume settings
        this.masterVolume = 0.7;
        this.musicVolume = 0.4; // Lower volume for background music
        this.ambientVolume = 0.6;

        // Playback settings
        this.fadeTime = 2000; // 2 seconds fade
        this.crossfadeTime = 3000; // 3 seconds crossfade
        this.autoPlay = true;
        this.shuffle = false;
        this.repeat = true;

        // Track management
        this.playlist = [];
        this.currentTrackIndex = 0;
        this.trackHistory = [];

        // Time-based music
        this.timeBasedMusic = true;
        this.lastTimeCheck = null;

        console.log(`Music System initializing in ${this.isElectron ? 'Electron' : 'Browser'} mode`);
        this.init();
    }
    
    async init() {
        try {
            // Initialize Web Audio API
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Create audio nodes
            this.masterGainNode = this.audioContext.createGain();
            this.musicGainNode = this.audioContext.createGain();
            this.ambientGainNode = this.audioContext.createGain();
            
            // Connect nodes
            this.musicGainNode.connect(this.masterGainNode);
            this.ambientGainNode.connect(this.masterGainNode);
            this.masterGainNode.connect(this.audioContext.destination);
            
            // Set initial volumes
            this.setMasterVolume(this.masterVolume);
            this.setMusicVolume(this.musicVolume);
            this.setAmbientVolume(this.ambientVolume);
            
            // Load music tracks
            await this.loadMusicTracks();
            
            // Setup time-based music changes
            this.setupTimeBasedMusic();

            // Auto-start background music
            this.startBackgroundMusic();

            console.log('Music System initialized');
            this.emit('initialized');
            
        } catch (error) {
            console.error('Failed to initialize Music System:', error);
            this.emit('initializationFailed', error);
        }
    }
    
    async loadMusicTracks() {
        if (this.isElectron) {
            await this.loadMusicTracksElectron();
        } else {
            await this.loadMusicTracksBrowser();
        }

        console.log(`Loaded ${this.musicTracks.size} music tracks`);

        // Show success notification for Electron
        if (this.isElectron && this.musicTracks.size > 0) {
            setTimeout(() => {
                if (window.notificationSystem) {
                    notificationSystem.success(
                        '🎵 Music System Ready',
                        `Background music loaded successfully! ${this.musicTracks.size} tracks available.`,
                        { duration: 4000 }
                    );
                }
            }, 2000); // Delay to ensure notification system is ready
        }

        this.emit('tracksLoaded', Array.from(this.musicTracks.keys()));
    }

    async loadMusicTracksElectron() {
        try {
            // Wait for electronAPI to be available
            let retries = 0;
            while (!window.electronAPI && retries < 10) {
                await new Promise(resolve => setTimeout(resolve, 100));
                retries++;
            }

            if (!window.electronAPI) {
                console.error('Electron API not available after waiting');
                return;
            }

            // Get available music files from Electron main process
            const result = await window.electronAPI.getMusicFiles();

            if (result.success && result.files.length > 0) {
                console.log(`Found ${result.files.length} music files in Electron app`);

                for (const file of result.files) {
                    try {
                        const trackDef = this.createTrackDefinitionFromFile(file);
                        const track = await this.loadTrack(trackDef);
                        if (track) {
                            this.musicTracks.set(trackDef.id, track);
                            this.playlist.push(trackDef.id);
                            console.log(`Loaded music track: ${trackDef.name}`);
                        }
                    } catch (error) {
                        console.log(`Could not load track ${file.name}:`, error.message);
                    }
                }
            } else {
                console.log('No music files found in Electron app');
                console.log('Result:', result);
            }
        } catch (error) {
            console.error('Failed to load music files in Electron:', error);
        }
    }

    async loadMusicTracksBrowser() {
        // Define music tracks with metadata for browser
        const trackDefinitions = [
            {
                id: 'springfield_dream',
                name: 'Springfield Dream',
                file: 'assets/sound/music/Springfield Dream.mp3',
                mood: 'peaceful',
                timeOfDay: ['day', 'dawn'],
                description: 'Peaceful ambient music for daytime Springfield',
                duration: null,
                loop: true
            },
            {
                id: 'springfield_night',
                name: 'Springfield Night',
                file: 'assets/sound/music/springfield_night.mp3',
                mood: 'calm',
                timeOfDay: ['night', 'dusk'],
                description: 'Calm nighttime ambience',
                duration: null,
                loop: true
            },
            {
                id: 'springfield_busy',
                name: 'Springfield Busy',
                file: 'assets/sound/music/springfield_busy.mp3',
                mood: 'energetic',
                timeOfDay: ['day'],
                description: 'Upbeat music for active town building',
                duration: null,
                loop: true
            },
            {
                id: 'springfield_celebration',
                name: 'Springfield Celebration',
                file: 'assets/sound/music/springfield_celebration.mp3',
                mood: 'joyful',
                timeOfDay: ['any'],
                description: 'Celebratory music for achievements',
                duration: null,
                loop: false
            }
        ];

        // Load available tracks
        for (const trackDef of trackDefinitions) {
            try {
                const track = await this.loadTrack(trackDef);
                if (track) {
                    this.musicTracks.set(trackDef.id, track);
                    this.playlist.push(trackDef.id);
                    console.log(`Loaded music track: ${trackDef.name}`);
                }
            } catch (error) {
                console.log(`Could not load track ${trackDef.name}:`, error.message);
            }
        }
    }

    createTrackDefinitionFromFile(file) {
        // Create track definition from file info
        const fileName = file.name.toLowerCase();
        const baseName = file.name.replace(/\.[^/.]+$/, ""); // Remove extension

        // Determine track properties based on filename
        let mood = 'peaceful';
        let timeOfDay = ['day'];
        let description = `Background music: ${baseName}`;
        let loop = true;

        if (fileName.includes('night') || fileName.includes('evening')) {
            mood = 'calm';
            timeOfDay = ['night', 'dusk'];
            description = 'Calm nighttime ambience';
        } else if (fileName.includes('busy') || fileName.includes('active') || fileName.includes('upbeat')) {
            mood = 'energetic';
            timeOfDay = ['day'];
            description = 'Upbeat music for active town building';
        } else if (fileName.includes('celebration') || fileName.includes('party') || fileName.includes('victory')) {
            mood = 'joyful';
            timeOfDay = ['any'];
            description = 'Celebratory music for achievements';
            loop = false;
        } else if (fileName.includes('dream') || fileName.includes('peaceful') || fileName.includes('calm')) {
            mood = 'peaceful';
            timeOfDay = ['day', 'dawn'];
            description = 'Peaceful ambient music for daytime Springfield';
        }

        return {
            id: baseName.toLowerCase().replace(/\s+/g, '_'),
            name: baseName,
            file: file.url, // Use the file URL for Electron
            mood: mood,
            timeOfDay: timeOfDay,
            description: description,
            duration: null,
            loop: loop
        };
    }
    
    async loadTrack(trackDef) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();

            audio.addEventListener('loadedmetadata', () => {
                trackDef.duration = audio.duration;
                resolve({
                    ...trackDef,
                    audio: audio,
                    loaded: true
                });
            });

            audio.addEventListener('error', (e) => {
                reject(new Error(`Failed to load ${trackDef.file}: ${e.message || 'Unknown error'}`));
            });

            audio.addEventListener('canplaythrough', () => {
                // Track is ready to play
                console.log(`Track ready: ${trackDef.name}`);
            });

            // Set audio source
            try {
                audio.src = trackDef.file;
                audio.preload = 'metadata';
                audio.loop = trackDef.loop;

                // For Electron, we might need to handle CORS differently
                if (this.isElectron) {
                    audio.crossOrigin = 'anonymous';
                }

                console.log(`Loading track: ${trackDef.name} from ${trackDef.file}`);
            } catch (error) {
                reject(new Error(`Failed to set audio source for ${trackDef.name}: ${error.message}`));
            }
        });
    }
    
    // Playback control methods
    async playTrack(trackId, fadeIn = true) {
        if (!this.musicTracks.has(trackId)) {
            console.error('Track not found:', trackId);
            return false;
        }
        
        // Resume audio context if suspended
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }
        
        const track = this.musicTracks.get(trackId);
        
        // Stop current track if playing
        if (this.currentAudio && this.isPlaying) {
            await this.stopTrack(true); // Fade out current track
        }
        
        this.currentTrack = trackId;
        this.currentAudio = track.audio;
        
        // Set volume for fade in
        if (fadeIn) {
            this.currentAudio.volume = 0;
        } else {
            this.currentAudio.volume = this.musicVolume;
        }
        
        // Play the track
        try {
            await this.currentAudio.play();
            this.isPlaying = true;
            this.isPaused = false;

            // Fade in if requested
            if (fadeIn) {
                this.fadeIn(this.currentAudio, this.musicVolume, this.fadeTime);
            }

            // Setup ended event for non-looping tracks
            if (!track.loop) {
                this.currentAudio.addEventListener('ended', () => {
                    this.onTrackEnded();
                }, { once: true });
            }

            console.log(`Playing track: ${track.name}`);
            this.emit('trackStarted', { trackId, track });

            return true;

        } catch (error) {
            // Check if it's an autoplay policy error
            if (error.name === 'NotAllowedError') {
                console.log('Music playback blocked by autoplay policy');
                this.emit('autoplayBlocked', { trackId, error });
                throw error; // Re-throw to be handled by caller
            } else {
                console.error('Failed to play track:', error);
                this.emit('playbackError', { trackId, error });
                return false;
            }
        }
    }
    
    async stopTrack(fadeOut = true) {
        if (!this.currentAudio || !this.isPlaying) return;
        
        if (fadeOut) {
            await this.fadeOut(this.currentAudio, this.fadeTime);
        }
        
        this.currentAudio.pause();
        this.currentAudio.currentTime = 0;
        this.isPlaying = false;
        this.isPaused = false;
        
        const trackId = this.currentTrack;
        this.currentTrack = null;
        this.currentAudio = null;
        
        console.log('Stopped music track');
        this.emit('trackStopped', { trackId });
    }
    
    pauseTrack() {
        if (this.currentAudio && this.isPlaying && !this.isPaused) {
            this.currentAudio.pause();
            this.isPaused = true;
            console.log('Paused music track');
            this.emit('trackPaused', { trackId: this.currentTrack });
        }
    }
    
    resumeTrack() {
        if (this.currentAudio && this.isPaused) {
            this.currentAudio.play();
            this.isPaused = false;
            console.log('Resumed music track');
            this.emit('trackResumed', { trackId: this.currentTrack });
        }
    }
    
    // Volume control methods
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.masterGainNode) {
            this.masterGainNode.gain.value = this.masterVolume;
        }
        this.emit('volumeChanged', { type: 'master', volume: this.masterVolume });
    }
    
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        if (this.musicGainNode) {
            this.musicGainNode.gain.value = this.musicVolume;
        }
        if (this.currentAudio && this.isPlaying && !this.isPaused) {
            this.currentAudio.volume = this.musicVolume;
        }
        this.emit('volumeChanged', { type: 'music', volume: this.musicVolume });
    }
    
    setAmbientVolume(volume) {
        this.ambientVolume = Math.max(0, Math.min(1, volume));
        if (this.ambientGainNode) {
            this.ambientGainNode.gain.value = this.ambientVolume;
        }
        this.emit('volumeChanged', { type: 'ambient', volume: this.ambientVolume });
    }
    
    // Fade effects
    fadeIn(audio, targetVolume, duration) {
        return new Promise((resolve) => {
            const startVolume = 0;
            const volumeStep = targetVolume / (duration / 50);
            let currentVolume = startVolume;
            
            const fadeInterval = setInterval(() => {
                currentVolume += volumeStep;
                if (currentVolume >= targetVolume) {
                    currentVolume = targetVolume;
                    clearInterval(fadeInterval);
                    resolve();
                }
                audio.volume = currentVolume;
            }, 50);
        });
    }
    
    fadeOut(audio, duration) {
        return new Promise((resolve) => {
            const startVolume = audio.volume;
            const volumeStep = startVolume / (duration / 50);
            let currentVolume = startVolume;

            const fadeInterval = setInterval(() => {
                currentVolume -= volumeStep;
                if (currentVolume <= 0) {
                    currentVolume = 0;
                    clearInterval(fadeInterval);
                    resolve();
                }
                audio.volume = currentVolume;
            }, 50);
        });
    }

    // Playlist management
    nextTrack() {
        if (this.playlist.length === 0) return;

        if (this.shuffle) {
            this.currentTrackIndex = Math.floor(Math.random() * this.playlist.length);
        } else {
            this.currentTrackIndex = (this.currentTrackIndex + 1) % this.playlist.length;
        }

        const nextTrackId = this.playlist[this.currentTrackIndex];
        this.playTrack(nextTrackId);
    }

    previousTrack() {
        if (this.playlist.length === 0) return;

        this.currentTrackIndex = this.currentTrackIndex - 1;
        if (this.currentTrackIndex < 0) {
            this.currentTrackIndex = this.playlist.length - 1;
        }

        const prevTrackId = this.playlist[this.currentTrackIndex];
        this.playTrack(prevTrackId);
    }

    onTrackEnded() {
        this.emit('trackEnded', { trackId: this.currentTrack });

        if (this.autoPlay) {
            if (this.repeat && this.playlist.length > 1) {
                this.nextTrack();
            } else if (this.repeat) {
                // Repeat current track
                this.playTrack(this.currentTrack);
            }
        }
    }

    // Auto-start background music
    async startBackgroundMusic() {
        // Wait a moment for tracks to load
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log(`Starting background music - ${this.musicTracks.size} tracks available`);

        if (this.musicTracks.size > 0) {
            try {
                // Try to start background music
                if (this.timeBasedMusic) {
                    this.checkTimeBasedMusic();
                } else {
                    // Start with first available track
                    const firstTrack = Array.from(this.musicTracks.keys())[0];
                    await this.playTrack(firstTrack, true);
                }

                console.log('Background music started automatically');
            } catch (error) {
                console.error('Failed to start background music:', error);

                if (this.isElectron) {
                    // In Electron, show a different message
                    console.log('Music system error in Electron app');
                    this.showElectronMusicError(error);
                } else {
                    // Browser autoplay policy blocked the music
                    console.log('Background music blocked by browser autoplay policy');
                    this.showAutoplayNotification();
                }
            }
        } else {
            console.log('No music tracks available for background music');
            if (this.isElectron) {
                this.showNoMusicFilesNotification();
            }
        }
    }

    // Show notification for Electron music errors
    showElectronMusicError(error) {
        if (window.notificationSystem) {
            notificationSystem.warning(
                '🎵 Music System Error',
                `Background music encountered an error: ${error.message}. Check that music files are accessible.`,
                {
                    duration: 8000,
                    actions: [
                        {
                            text: 'Retry',
                            callback: () => this.startBackgroundMusic()
                        }
                    ]
                }
            );
        }
    }

    // Show notification when no music files found
    showNoMusicFilesNotification() {
        if (window.notificationSystem) {
            notificationSystem.info(
                '🎵 No Music Files',
                'No music files found. Add MP3 files to the assets/sound/music directory to enable background music.',
                { duration: 6000 }
            );
        }
    }

    // Show notification to enable music
    showAutoplayNotification() {
        if (window.notificationSystem) {
            notificationSystem.info(
                '🎵 Background Music Available',
                'Click anywhere to enable background music for Springfield',
                {
                    duration: 8000,
                    actions: [
                        {
                            text: 'Enable Music',
                            callback: () => this.enableBackgroundMusic()
                        }
                    ]
                }
            );
        }

        // Also add a one-time click listener to enable music
        const enableMusicOnClick = async () => {
            await this.enableBackgroundMusic();
            document.removeEventListener('click', enableMusicOnClick);
        };

        document.addEventListener('click', enableMusicOnClick, { once: true });
    }

    // Enable background music after user interaction
    async enableBackgroundMusic() {
        try {
            // Resume audio context if suspended
            if (this.audioContext && this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            // Start music if not already playing
            if (!this.isPlaying && this.musicTracks.size > 0) {
                if (this.timeBasedMusic) {
                    this.checkTimeBasedMusic();
                } else {
                    const firstTrack = Array.from(this.musicTracks.keys())[0];
                    await this.playTrack(firstTrack, true);
                }

                console.log('Background music enabled');

                if (window.notificationSystem) {
                    notificationSystem.success(
                        '🎵 Music Enabled',
                        'Background music is now playing',
                        { duration: 3000 }
                    );
                }
            }
        } catch (error) {
            console.error('Failed to enable background music:', error);
        }
    }

    // Time-based music system
    setupTimeBasedMusic() {
        if (!this.timeBasedMusic) return;

        // Check time every minute
        setInterval(() => {
            this.checkTimeBasedMusic();
        }, 60000);

        // Initial check will be called by startBackgroundMusic
    }

    checkTimeBasedMusic() {
        const now = new Date();
        const hour = now.getHours();
        let timeOfDay;

        if (hour >= 5 && hour < 7) {
            timeOfDay = 'dawn';
        } else if (hour >= 7 && hour < 18) {
            timeOfDay = 'day';
        } else if (hour >= 18 && hour < 20) {
            timeOfDay = 'dusk';
        } else {
            timeOfDay = 'night';
        }

        // Only change if time period changed
        if (this.lastTimeCheck !== timeOfDay) {
            this.lastTimeCheck = timeOfDay;
            this.playMusicForTime(timeOfDay);
        }
    }

    playMusicForTime(timeOfDay) {
        // Find tracks suitable for this time
        const suitableTracks = Array.from(this.musicTracks.values())
            .filter(track => track.timeOfDay.includes(timeOfDay) || track.timeOfDay.includes('any'));

        if (suitableTracks.length > 0) {
            const randomTrack = suitableTracks[Math.floor(Math.random() * suitableTracks.length)];

            // Only change if different from current track
            if (this.currentTrack !== randomTrack.id) {
                console.log(`Changing music for ${timeOfDay}: ${randomTrack.name}`);
                this.playTrack(randomTrack.id);
            }
        }
    }

    // Mood-based music
    playMusicForMood(mood) {
        const suitableTracks = Array.from(this.musicTracks.values())
            .filter(track => track.mood === mood);

        if (suitableTracks.length > 0) {
            const randomTrack = suitableTracks[Math.floor(Math.random() * suitableTracks.length)];
            console.log(`Playing ${mood} music: ${randomTrack.name}`);
            this.playTrack(randomTrack.id);
        }
    }

    // Event-based music
    playEventMusic(eventType) {
        const eventMusicMap = {
            'levelUp': 'springfield_celebration',
            'buildingComplete': 'springfield_busy',
            'questComplete': 'springfield_celebration',
            'townGrowing': 'springfield_busy',
            'peaceful': 'springfield_dream'
        };

        const trackId = eventMusicMap[eventType];
        if (trackId && this.musicTracks.has(trackId)) {
            this.playTrack(trackId, false); // No fade for event music
        }
    }

    // Settings management
    toggleAutoPlay() {
        this.autoPlay = !this.autoPlay;
        this.emit('settingChanged', { setting: 'autoPlay', value: this.autoPlay });
    }

    toggleShuffle() {
        this.shuffle = !this.shuffle;
        this.emit('settingChanged', { setting: 'shuffle', value: this.shuffle });
    }

    toggleRepeat() {
        this.repeat = !this.repeat;
        this.emit('settingChanged', { setting: 'repeat', value: this.repeat });
    }

    toggleTimeBasedMusic() {
        this.timeBasedMusic = !this.timeBasedMusic;
        if (this.timeBasedMusic) {
            this.setupTimeBasedMusic();
        }
        this.emit('settingChanged', { setting: 'timeBasedMusic', value: this.timeBasedMusic });
    }

    // Utility methods
    getCurrentTrackInfo() {
        if (!this.currentTrack) return null;

        const track = this.musicTracks.get(this.currentTrack);
        return {
            id: this.currentTrack,
            name: track.name,
            description: track.description,
            mood: track.mood,
            timeOfDay: track.timeOfDay,
            isPlaying: this.isPlaying,
            isPaused: this.isPaused,
            currentTime: this.currentAudio ? this.currentAudio.currentTime : 0,
            duration: track.duration
        };
    }

    getAvailableTracks() {
        return Array.from(this.musicTracks.values()).map(track => ({
            id: track.id,
            name: track.name,
            description: track.description,
            mood: track.mood,
            timeOfDay: track.timeOfDay,
            duration: track.duration
        }));
    }

    getSettings() {
        return {
            masterVolume: this.masterVolume,
            musicVolume: this.musicVolume,
            ambientVolume: this.ambientVolume,
            autoPlay: this.autoPlay,
            shuffle: this.shuffle,
            repeat: this.repeat,
            timeBasedMusic: this.timeBasedMusic
        };
    }

    setSettings(settings) {
        if (settings.masterVolume !== undefined) this.setMasterVolume(settings.masterVolume);
        if (settings.musicVolume !== undefined) this.setMusicVolume(settings.musicVolume);
        if (settings.ambientVolume !== undefined) this.setAmbientVolume(settings.ambientVolume);
        if (settings.autoPlay !== undefined) this.autoPlay = settings.autoPlay;
        if (settings.shuffle !== undefined) this.shuffle = settings.shuffle;
        if (settings.repeat !== undefined) this.repeat = settings.repeat;
        if (settings.timeBasedMusic !== undefined) {
            this.timeBasedMusic = settings.timeBasedMusic;
            if (this.timeBasedMusic) this.setupTimeBasedMusic();
        }
    }

    // Debug method for testing music system
    async debugMusicSystem() {
        console.log('=== Music System Debug ===');
        console.log('Environment:', this.isElectron ? 'Electron' : 'Browser');
        console.log('Audio Context State:', this.audioContext?.state);
        console.log('Tracks Loaded:', this.musicTracks.size);
        console.log('Current Track:', this.currentTrack);
        console.log('Is Playing:', this.isPlaying);
        console.log('Volume:', this.musicVolume);

        if (this.isElectron) {
            try {
                const result = await window.electronAPI.getMusicFiles();
                console.log('Electron Music Files:', result);
            } catch (error) {
                console.error('Electron API Error:', error);
            }
        }

        console.log('Available Tracks:');
        this.musicTracks.forEach((track, id) => {
            console.log(`  ${id}: ${track.name} (${track.file})`);
        });

        console.log('=== End Debug ===');
    }

    // Cleanup
    dispose() {
        this.stopTrack(false);

        if (this.audioContext) {
            this.audioContext.close();
        }

        this.musicTracks.clear();
        this.removeAllListeners();
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MusicSystem;
}

// Make sure MusicSystem is available globally
window.MusicSystem = MusicSystem;

// Global debug function for testing
window.debugMusicSystem = function() {
    if (window.game && window.game.musicSystem) {
        window.game.musicSystem.debugMusicSystem();
    } else {
        console.log('Music system not available. Game not loaded or music system not initialized.');
    }
};
