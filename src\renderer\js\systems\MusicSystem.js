/**
 * Springfield Town Builder - Music System
 * Handles background music, ambient sounds, and audio management
 */

class MusicSystem extends EventEmitter {
    constructor() {
        super();
        
        // Audio context and nodes
        this.audioContext = null;
        this.masterGainNode = null;
        this.musicGainNode = null;
        this.ambientGainNode = null;
        
        // Music tracks
        this.musicTracks = new Map();
        this.currentTrack = null;
        this.currentAudio = null;
        this.isPlaying = false;
        this.isPaused = false;
        
        // Volume settings
        this.masterVolume = 0.7;
        this.musicVolume = 0.8;
        this.ambientVolume = 0.6;
        
        // Playback settings
        this.fadeTime = 2000; // 2 seconds fade
        this.crossfadeTime = 3000; // 3 seconds crossfade
        this.autoPlay = true;
        this.shuffle = false;
        this.repeat = true;
        
        // Track management
        this.playlist = [];
        this.currentTrackIndex = 0;
        this.trackHistory = [];
        
        // Time-based music
        this.timeBasedMusic = true;
        this.lastTimeCheck = null;
        
        this.init();
    }
    
    async init() {
        try {
            // Initialize Web Audio API
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Create audio nodes
            this.masterGainNode = this.audioContext.createGain();
            this.musicGainNode = this.audioContext.createGain();
            this.ambientGainNode = this.audioContext.createGain();
            
            // Connect nodes
            this.musicGainNode.connect(this.masterGainNode);
            this.ambientGainNode.connect(this.masterGainNode);
            this.masterGainNode.connect(this.audioContext.destination);
            
            // Set initial volumes
            this.setMasterVolume(this.masterVolume);
            this.setMusicVolume(this.musicVolume);
            this.setAmbientVolume(this.ambientVolume);
            
            // Load music tracks
            await this.loadMusicTracks();
            
            // Setup time-based music changes
            this.setupTimeBasedMusic();
            
            console.log('Music System initialized');
            this.emit('initialized');
            
        } catch (error) {
            console.error('Failed to initialize Music System:', error);
            this.emit('initializationFailed', error);
        }
    }
    
    async loadMusicTracks() {
        // Define music tracks with metadata
        const trackDefinitions = [
            {
                id: 'springfield_dream',
                name: 'Springfield Dream',
                file: 'assets/sound/music/Springfield Dream.mp3',
                mood: 'peaceful',
                timeOfDay: ['day', 'dawn'],
                description: 'Peaceful ambient music for daytime Springfield',
                duration: null, // Will be set when loaded
                loop: true
            },
            {
                id: 'springfield_night',
                name: 'Springfield Night',
                file: 'assets/sound/music/springfield_night.mp3',
                mood: 'calm',
                timeOfDay: ['night', 'dusk'],
                description: 'Calm nighttime ambience',
                duration: null,
                loop: true
            },
            {
                id: 'springfield_busy',
                name: 'Springfield Busy',
                file: 'assets/sound/music/springfield_busy.mp3',
                mood: 'energetic',
                timeOfDay: ['day'],
                description: 'Upbeat music for active town building',
                duration: null,
                loop: true
            },
            {
                id: 'springfield_celebration',
                name: 'Springfield Celebration',
                file: 'assets/sound/music/springfield_celebration.mp3',
                mood: 'joyful',
                timeOfDay: ['any'],
                description: 'Celebratory music for achievements',
                duration: null,
                loop: false
            }
        ];
        
        // Load available tracks
        for (const trackDef of trackDefinitions) {
            try {
                const track = await this.loadTrack(trackDef);
                if (track) {
                    this.musicTracks.set(trackDef.id, track);
                    this.playlist.push(trackDef.id);
                    console.log(`Loaded music track: ${trackDef.name}`);
                }
            } catch (error) {
                console.log(`Could not load track ${trackDef.name}:`, error.message);
            }
        }
        
        console.log(`Loaded ${this.musicTracks.size} music tracks`);
        this.emit('tracksLoaded', Array.from(this.musicTracks.keys()));
    }
    
    async loadTrack(trackDef) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            
            audio.addEventListener('loadedmetadata', () => {
                trackDef.duration = audio.duration;
                resolve({
                    ...trackDef,
                    audio: audio,
                    loaded: true
                });
            });
            
            audio.addEventListener('error', (e) => {
                reject(new Error(`Failed to load ${trackDef.file}`));
            });
            
            audio.addEventListener('canplaythrough', () => {
                // Track is ready to play
            });
            
            audio.src = trackDef.file;
            audio.preload = 'metadata';
            audio.loop = trackDef.loop;
        });
    }
    
    // Playback control methods
    async playTrack(trackId, fadeIn = true) {
        if (!this.musicTracks.has(trackId)) {
            console.error('Track not found:', trackId);
            return false;
        }
        
        // Resume audio context if suspended
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }
        
        const track = this.musicTracks.get(trackId);
        
        // Stop current track if playing
        if (this.currentAudio && this.isPlaying) {
            await this.stopTrack(true); // Fade out current track
        }
        
        this.currentTrack = trackId;
        this.currentAudio = track.audio;
        
        // Set volume for fade in
        if (fadeIn) {
            this.currentAudio.volume = 0;
        } else {
            this.currentAudio.volume = this.musicVolume;
        }
        
        // Play the track
        try {
            await this.currentAudio.play();
            this.isPlaying = true;
            this.isPaused = false;
            
            // Fade in if requested
            if (fadeIn) {
                this.fadeIn(this.currentAudio, this.musicVolume, this.fadeTime);
            }
            
            // Setup ended event for non-looping tracks
            if (!track.loop) {
                this.currentAudio.addEventListener('ended', () => {
                    this.onTrackEnded();
                }, { once: true });
            }
            
            console.log(`Playing track: ${track.name}`);
            this.emit('trackStarted', { trackId, track });
            
            return true;
            
        } catch (error) {
            console.error('Failed to play track:', error);
            this.emit('playbackError', { trackId, error });
            return false;
        }
    }
    
    async stopTrack(fadeOut = true) {
        if (!this.currentAudio || !this.isPlaying) return;
        
        if (fadeOut) {
            await this.fadeOut(this.currentAudio, this.fadeTime);
        }
        
        this.currentAudio.pause();
        this.currentAudio.currentTime = 0;
        this.isPlaying = false;
        this.isPaused = false;
        
        const trackId = this.currentTrack;
        this.currentTrack = null;
        this.currentAudio = null;
        
        console.log('Stopped music track');
        this.emit('trackStopped', { trackId });
    }
    
    pauseTrack() {
        if (this.currentAudio && this.isPlaying && !this.isPaused) {
            this.currentAudio.pause();
            this.isPaused = true;
            console.log('Paused music track');
            this.emit('trackPaused', { trackId: this.currentTrack });
        }
    }
    
    resumeTrack() {
        if (this.currentAudio && this.isPaused) {
            this.currentAudio.play();
            this.isPaused = false;
            console.log('Resumed music track');
            this.emit('trackResumed', { trackId: this.currentTrack });
        }
    }
    
    // Volume control methods
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.masterGainNode) {
            this.masterGainNode.gain.value = this.masterVolume;
        }
        this.emit('volumeChanged', { type: 'master', volume: this.masterVolume });
    }
    
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        if (this.musicGainNode) {
            this.musicGainNode.gain.value = this.musicVolume;
        }
        if (this.currentAudio && this.isPlaying && !this.isPaused) {
            this.currentAudio.volume = this.musicVolume;
        }
        this.emit('volumeChanged', { type: 'music', volume: this.musicVolume });
    }
    
    setAmbientVolume(volume) {
        this.ambientVolume = Math.max(0, Math.min(1, volume));
        if (this.ambientGainNode) {
            this.ambientGainNode.gain.value = this.ambientVolume;
        }
        this.emit('volumeChanged', { type: 'ambient', volume: this.ambientVolume });
    }
    
    // Fade effects
    fadeIn(audio, targetVolume, duration) {
        return new Promise((resolve) => {
            const startVolume = 0;
            const volumeStep = targetVolume / (duration / 50);
            let currentVolume = startVolume;
            
            const fadeInterval = setInterval(() => {
                currentVolume += volumeStep;
                if (currentVolume >= targetVolume) {
                    currentVolume = targetVolume;
                    clearInterval(fadeInterval);
                    resolve();
                }
                audio.volume = currentVolume;
            }, 50);
        });
    }
    
    fadeOut(audio, duration) {
        return new Promise((resolve) => {
            const startVolume = audio.volume;
            const volumeStep = startVolume / (duration / 50);
            let currentVolume = startVolume;

            const fadeInterval = setInterval(() => {
                currentVolume -= volumeStep;
                if (currentVolume <= 0) {
                    currentVolume = 0;
                    clearInterval(fadeInterval);
                    resolve();
                }
                audio.volume = currentVolume;
            }, 50);
        });
    }

    // Playlist management
    nextTrack() {
        if (this.playlist.length === 0) return;

        if (this.shuffle) {
            this.currentTrackIndex = Math.floor(Math.random() * this.playlist.length);
        } else {
            this.currentTrackIndex = (this.currentTrackIndex + 1) % this.playlist.length;
        }

        const nextTrackId = this.playlist[this.currentTrackIndex];
        this.playTrack(nextTrackId);
    }

    previousTrack() {
        if (this.playlist.length === 0) return;

        this.currentTrackIndex = this.currentTrackIndex - 1;
        if (this.currentTrackIndex < 0) {
            this.currentTrackIndex = this.playlist.length - 1;
        }

        const prevTrackId = this.playlist[this.currentTrackIndex];
        this.playTrack(prevTrackId);
    }

    onTrackEnded() {
        this.emit('trackEnded', { trackId: this.currentTrack });

        if (this.autoPlay) {
            if (this.repeat && this.playlist.length > 1) {
                this.nextTrack();
            } else if (this.repeat) {
                // Repeat current track
                this.playTrack(this.currentTrack);
            }
        }
    }

    // Time-based music system
    setupTimeBasedMusic() {
        if (!this.timeBasedMusic) return;

        // Check time every minute
        setInterval(() => {
            this.checkTimeBasedMusic();
        }, 60000);

        // Initial check
        this.checkTimeBasedMusic();
    }

    checkTimeBasedMusic() {
        const now = new Date();
        const hour = now.getHours();
        let timeOfDay;

        if (hour >= 5 && hour < 7) {
            timeOfDay = 'dawn';
        } else if (hour >= 7 && hour < 18) {
            timeOfDay = 'day';
        } else if (hour >= 18 && hour < 20) {
            timeOfDay = 'dusk';
        } else {
            timeOfDay = 'night';
        }

        // Only change if time period changed
        if (this.lastTimeCheck !== timeOfDay) {
            this.lastTimeCheck = timeOfDay;
            this.playMusicForTime(timeOfDay);
        }
    }

    playMusicForTime(timeOfDay) {
        // Find tracks suitable for this time
        const suitableTracks = Array.from(this.musicTracks.values())
            .filter(track => track.timeOfDay.includes(timeOfDay) || track.timeOfDay.includes('any'));

        if (suitableTracks.length > 0) {
            const randomTrack = suitableTracks[Math.floor(Math.random() * suitableTracks.length)];

            // Only change if different from current track
            if (this.currentTrack !== randomTrack.id) {
                console.log(`Changing music for ${timeOfDay}: ${randomTrack.name}`);
                this.playTrack(randomTrack.id);
            }
        }
    }

    // Mood-based music
    playMusicForMood(mood) {
        const suitableTracks = Array.from(this.musicTracks.values())
            .filter(track => track.mood === mood);

        if (suitableTracks.length > 0) {
            const randomTrack = suitableTracks[Math.floor(Math.random() * suitableTracks.length)];
            console.log(`Playing ${mood} music: ${randomTrack.name}`);
            this.playTrack(randomTrack.id);
        }
    }

    // Event-based music
    playEventMusic(eventType) {
        const eventMusicMap = {
            'levelUp': 'springfield_celebration',
            'buildingComplete': 'springfield_busy',
            'questComplete': 'springfield_celebration',
            'townGrowing': 'springfield_busy',
            'peaceful': 'springfield_dream'
        };

        const trackId = eventMusicMap[eventType];
        if (trackId && this.musicTracks.has(trackId)) {
            this.playTrack(trackId, false); // No fade for event music
        }
    }

    // Settings management
    toggleAutoPlay() {
        this.autoPlay = !this.autoPlay;
        this.emit('settingChanged', { setting: 'autoPlay', value: this.autoPlay });
    }

    toggleShuffle() {
        this.shuffle = !this.shuffle;
        this.emit('settingChanged', { setting: 'shuffle', value: this.shuffle });
    }

    toggleRepeat() {
        this.repeat = !this.repeat;
        this.emit('settingChanged', { setting: 'repeat', value: this.repeat });
    }

    toggleTimeBasedMusic() {
        this.timeBasedMusic = !this.timeBasedMusic;
        if (this.timeBasedMusic) {
            this.setupTimeBasedMusic();
        }
        this.emit('settingChanged', { setting: 'timeBasedMusic', value: this.timeBasedMusic });
    }

    // Utility methods
    getCurrentTrackInfo() {
        if (!this.currentTrack) return null;

        const track = this.musicTracks.get(this.currentTrack);
        return {
            id: this.currentTrack,
            name: track.name,
            description: track.description,
            mood: track.mood,
            timeOfDay: track.timeOfDay,
            isPlaying: this.isPlaying,
            isPaused: this.isPaused,
            currentTime: this.currentAudio ? this.currentAudio.currentTime : 0,
            duration: track.duration
        };
    }

    getAvailableTracks() {
        return Array.from(this.musicTracks.values()).map(track => ({
            id: track.id,
            name: track.name,
            description: track.description,
            mood: track.mood,
            timeOfDay: track.timeOfDay,
            duration: track.duration
        }));
    }

    getSettings() {
        return {
            masterVolume: this.masterVolume,
            musicVolume: this.musicVolume,
            ambientVolume: this.ambientVolume,
            autoPlay: this.autoPlay,
            shuffle: this.shuffle,
            repeat: this.repeat,
            timeBasedMusic: this.timeBasedMusic
        };
    }

    setSettings(settings) {
        if (settings.masterVolume !== undefined) this.setMasterVolume(settings.masterVolume);
        if (settings.musicVolume !== undefined) this.setMusicVolume(settings.musicVolume);
        if (settings.ambientVolume !== undefined) this.setAmbientVolume(settings.ambientVolume);
        if (settings.autoPlay !== undefined) this.autoPlay = settings.autoPlay;
        if (settings.shuffle !== undefined) this.shuffle = settings.shuffle;
        if (settings.repeat !== undefined) this.repeat = settings.repeat;
        if (settings.timeBasedMusic !== undefined) {
            this.timeBasedMusic = settings.timeBasedMusic;
            if (this.timeBasedMusic) this.setupTimeBasedMusic();
        }
    }

    // Cleanup
    dispose() {
        this.stopTrack(false);

        if (this.audioContext) {
            this.audioContext.close();
        }

        this.musicTracks.clear();
        this.removeAllListeners();
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MusicSystem;
}
}
